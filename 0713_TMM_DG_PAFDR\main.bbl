% Generated by IEEEtran.bst, version: 1.14 (2015/08/26)
\begin{thebibliography}{10}
\providecommand{\url}[1]{#1}
\csname url@samestyle\endcsname
\providecommand{\newblock}{\relax}
\providecommand{\bibinfo}[2]{#2}
\providecommand{\BIBentrySTDinterwordspacing}{\spaceskip=0pt\relax}
\providecommand{\BIBentryALTinterwordstretchfactor}{4}
\providecommand{\BIBentryALTinterwordspacing}{\spaceskip=\fontdimen2\font plus
\BIBentryALTinterwordstretchfactor\fontdimen3\font minus
  \fontdimen4\font\relax}
\providecommand{\BIBforeignlanguage}[2]{{%
\expandafter\ifx\csname l@#1\endcsname\relax
\typeout{** WARNING: IEEEtran.bst: No hyphenation pattern has been}%
\typeout{** loaded for the language `#1'. Using the pattern for}%
\typeout{** the default language instead.}%
\else
\language=\csname l@#1\endcsname
\fi
#2}}
\providecommand{\BIBdecl}{\relax}
\BIBdecl

\bibitem{qian2018pose}
X.~<PERSON><PERSON>, <PERSON>.~<PERSON>, <PERSON>.~<PERSON>ang, W.~<PERSON>, J.~Qiu, Y.~Wu, Y.-G. Jiang, and X.~Xue,
  ``Pose-normalized image generation for person re-identification,'' in
  \emph{ECCV}, 2018.

\bibitem{jin2021style}
X.~Jin, C.~Lan, W.~Zeng, and Z.~Chen, ``Style normalization and restitution for
  domain generalization and adaptation,'' \emph{IEEE Transactions on
  Multimedia}, vol.~24, pp. 3636--3651, 2021.

\bibitem{Wang2022DGSurvey}
J.~Wang, P.~Cui, W.~Niu, Z.~Hu, and S.~Wang, ``Generalizing to unseen domains:
  A survey on domain generalization,'' \emph{IEEE Transactions on Knowledge and
  Data Engineering}, vol.~35, no.~8, pp. 7926--7946, 2022.

\bibitem{Ganin2016DANN}
Y.~Ganin, E.~Ustinova, H.~Ajakan, P.~Germain, H.~Larochelle, F.~Laviolette,
  M.~Marchand, and V.~Lempitsky, ``Domain-adversarial training of neural
  networks,'' \emph{Journal of Machine Learning Research}, vol.~17, no.~59, pp.
  1--35, 2016.

\bibitem{zhou2019omni}
K.~Zhou, Y.~Yang, A.~Cavallaro, and T.~Xiang, ``Omni-scale feature learning for
  person re-identification,'' in \emph{Proceedings of the IEEE/CVF
  international conference on computer vision}, 2019, pp. 3702--3712.

\bibitem{ioffe2015batch}
S.~Ioffe and C.~Szegedy, ``Batch normalization: Accelerating deep network
  training by reducing internal covariate shift,'' in \emph{ICML}, 2015.

\bibitem{choi2021meta}
S.~Choi, T.~Kim, M.~Jeong, H.~Park, and C.~Kim, ``Meta batch-instance
  normalization for generalizable person re-identification,'' in
  \emph{Proceedings of the IEEE/CVF conference on Computer Vision and Pattern
  Recognition}, 2021, pp. 3425--3435.

\bibitem{huang2017arbitrary}
X.~Huang and S.~Belongie, ``Arbitrary style transfer in real-time with adaptive
  instance normalization,'' in \emph{ICCV}, 2017.

\bibitem{pan2018two}
X.~Pan, P.~Luo, J.~Shi, and X.~Tang, ``Two at once: Enhancing learning and
  generalization capacities via ibn-net,'' in \emph{ECCV}, 2018.

\bibitem{zhou2019osnet}
K.~Zhou, Y.~Yang, A.~Cavallaro, and T.~Xiang, ``Omni-scale feature learning for
  person re-identification,'' in \emph{ICCV}, 2019.

\bibitem{zheng2021calibrated}
K.~Zheng, J.~Liu, W.~Wu, L.~Li, and Z.-j. Zha, ``Calibrated feature
  decomposition for generalizable person re-identification,'' \emph{arXiv
  preprint arXiv:2111.13945}, 2021.

\bibitem{zhou2016learning}
B.~Zhou, A.~Khosla, A.~Lapedriza, A.~Oliva, and A.~Torralba, ``Learning deep
  features for discriminative localization,'' in \emph{Proceedings of the IEEE
  conference on computer vision and pattern recognition}, 2016, pp. 2921--2929.

\bibitem{li2018mmdaae}
H.~Li, S.~Jialin~Pan, S.~Wang, and A.~C. Kot, ``Domain generalization with
  adversarial feature learning,'' in \emph{CVPR}, 2018.

\bibitem{liu2020shape}
Q.~Liu, Q.~Dou, and P.-A. Heng, ``Shape-aware meta-learning for generalizing
  prostate mri segmentation to unseen domains,'' in \emph{MICCAI}, 2020.

\bibitem{cha2021domain}
J.~Cha, H.~Cho, K.~Lee, S.~Park, Y.~Lee, and S.~Park, ``Domain generalization
  needs stochastic weight averaging for robustness on domain shifts,''
  \emph{arXiv preprint arXiv:2102.08604}, 2021.

\bibitem{khodabandeh2019robust}
M.~Khodabandeh, A.~Vahdat, M.~Ranjbar, and W.~G. Macready, ``A robust learning
  approach to domain adaptive object detection,'' in \emph{Proceedings of the
  IEEE International Conference on Computer Vision}, 2019, pp. 480--490.

\bibitem{chen2018domain}
Y.~Chen, W.~Li, C.~Sakaridis, D.~Dai, and L.~Van~Gool, ``Domain adaptive faster
  r-cnn for object detection in the wild,'' in \emph{Proceedings of the IEEE
  conference on computer vision and pattern recognition}, 2018, pp. 3339--3348.

\bibitem{peng2019moment}
X.~Peng, Q.~Bai, X.~Xia, Z.~Huang, K.~Saenko, and B.~Wang, ``Moment matching
  for multi-source domain adaptation,'' in \emph{Proceedings of the IEEE/CVF
  International Conference on Computer Vision}, 2019, pp. 1406--1415.

\bibitem{higgins2016beta}
I.~Higgins, L.~Matthey, A.~Pal, C.~Burgess, X.~Glorot, M.~Botvinick,
  S.~Mohamed, and A.~Lerchner, ``{$\beta$-VAE: Learning Basic Visual Concepts
  with a Constrained Variational Framework},'' in \emph{International
  Conference on Learning Representations (ICLR)}, 2016.

\bibitem{wu2021stylespace}
Z.~Wu, D.~Lischinski, and E.~Shechtman, ``{StyleSpace Analysis: Disentangled
  Controls for StyleGAN Image Generation},'' in \emph{Proceedings of the
  IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)}, 2021,
  pp. 12\,863--12\,872.

\bibitem{he2016deep}
K.~He, X.~Zhang, S.~Ren, and J.~Sun, ``Deep residual learning for image
  recognition,'' in \emph{CVPR}, 2016.

\bibitem{carlucci2019domain}
F.~M. Carlucci, A.~D'Innocente, S.~Bucci, B.~Caputo, and T.~Tommasi, ``Domain
  generalization by solving jigsaw puzzles,'' in \emph{Proceedings of the
  IEEE/CVF conference on computer vision and pattern recognition}, 2019, pp.
  2229--2238.

\bibitem{li2020domain}
H.~Li, Y.~Wang, R.~Wan, S.~Wang, T.-Q. Li, and A.~C. Kot, ``Domain
  generalization for medical imaging classification with linear-dependency
  regularization,'' in \emph{NeurIPS}, 2020.

\bibitem{zhou2020learning}
K.~Zhou, Y.~Yang, T.~Hospedales, and T.~Xiang, ``Learning to generate novel
  domains for domain generalization,'' in \emph{ECCV}, 2020.

\bibitem{nam2021reducing}
H.~Nam, H.~Lee, J.~Park, W.~Yoon, and D.~Yoo, ``Reducing domain gap by reducing
  style bias,'' in \emph{Proceedings of the IEEE/CVF conference on computer
  vision and pattern recognition}, 2021, pp. 8690--8699.

\bibitem{zhou2021domain}
K.~Zhou, Y.~Yang, Y.~Qiao, and T.~Xiang, ``Domain adaptive ensemble learning,''
  \emph{IEEE Transactions on Image Processing}, vol.~30, pp. 8008--8018, 2021.

\bibitem{yao2022pcl}
X.~Yao, Y.~Bai, X.~Zhang, Y.~Zhang, Q.~Sun, R.~Chen, R.~Li, and B.~Yu, ``Pcl:
  Proxy-based contrastive learning for domain generalization,'' in
  \emph{Proceedings of the IEEE/CVF conference on computer vision and pattern
  recognition}, 2022, pp. 7097--7107.

\bibitem{jin2022style}
X.~Jin, C.~Lan, W.~Zeng, and Z.~Chen, ``Style normalization and restitution for
  domain generalization and adaptation,'' \emph{IEEE Transactions on
  Multimedia}, vol.~24, pp. 3636--3651, 2022.

\bibitem{wang2022domain}
H.~Wang and X.~Bi, ``Domain generalization and adaptation based on second-order
  style information,'' \emph{Pattern Recognition}, vol. 127, p. 108595, 2022.

\bibitem{segu2023batch}
M.~Segu, A.~Tonioni, and F.~Tombari, ``Batch normalization embeddings for deep
  domain generalization,'' \emph{Pattern Recognition}, vol. 135, p. 109115,
  2023.

\bibitem{niu2023knowledge}
Z.~Niu, J.~Yuan, X.~Ma, Y.~Xu, J.~Liu, Y.-W. Chen, R.~Tong, and L.~Lin,
  ``Knowledge distillation-based domain-invariant representation learning for
  domain generalization,'' \emph{IEEE Transactions on Multimedia}, 2023.

\bibitem{xu2024cbdmoe}
F.~Xu, D.~Chen, T.~Jia, S.~Deng, and H.~Wang, ``Cbdmoe: Consistent-but-diverse
  mixture of experts for domain generalization,'' \emph{IEEE Transactions on
  Multimedia}, 2024.

\bibitem{li2021t}
R.~Li, X.~Jia, J.~He, S.~Chen, and Q.~Hu, ``T-svdnet: Exploring high-order
  prototypical correlations for multi-source domain adaptation,'' in
  \emph{Proceedings of the IEEE/CVF International Conference on Computer
  Vision}, 2021, pp. 9991--10\,000.

\bibitem{ren2022multi}
C.-X. Ren, Y.-H. Liu, X.-W. Zhang, and K.-K. Huang, ``Multi-source unsupervised
  domain adaptation via pseudo target domain,'' \emph{IEEE Transactions on
  Image Processing}, vol.~31, pp. 2122--2135, 2022.

\bibitem{wang2022self}
Z.~Wang, C.~Zhou, B.~Du, and F.~He, ``Self-paced supervision for multi-source
  domain adaptation.'' in \emph{IJCAI}, 2022, pp. 3551--3557.

\bibitem{wu2023domain}
K.~Wu, F.~Jia, and Y.~Han, ``Domain-specific feature elimination: multi-source
  domain adaptation for image classification,'' \emph{Frontiers of Computer
  Science}, vol.~17, no.~4, p. 174705, 2023.

\bibitem{wen2024training}
L.~Wen, S.~Chen, M.~Xie, C.~Liu, and L.~Zheng, ``Training multi-source domain
  adaptation network by mutual information estimation and minimization,''
  \emph{Neural Networks}, vol. 171, pp. 353--361, 2024.

\bibitem{zhao2020multi}
S.~Zhao, G.~Wang, S.~Zhang, Y.~Gu, Y.~Li, Z.~Song, P.~Xu, R.~Hu, H.~Chai, and
  K.~Keutzer, ``Multi-source distilling domain adaptation,'' in \emph{AAAI},
  2020.

\bibitem{wang2020learning}
S.~Wang, L.~Yu, C.~Li, C.-W. Fu, and P.-A. Heng, ``Learning from extrinsic and
  intrinsic supervisions for domain generalization,'' in \emph{ECCV}, 2020.

\bibitem{xu2022graphical}
M.~Xu, H.~Wang, and B.~Ni, ``Graphical modeling for multi-source domain
  adaptation,'' \emph{IEEE Transactions on Pattern Analysis and Machine
  Intelligence}, vol.~46, no.~3, pp. 1727--1741, 2022.

\bibitem{deng2022dynamic}
Z.~Deng, K.~Zhou, D.~Li, J.~He, Y.-Z. Song, and T.~Xiang, ``Dynamic instance
  domain adaptation,'' \emph{IEEE Transactions on Image Processing}, vol.~31,
  pp. 4585--4597, 2022.

\end{thebibliography}
