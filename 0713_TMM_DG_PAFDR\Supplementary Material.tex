\documentclass[journal]{IEEEtran}

\usepackage{amsmath,amsfonts}

\usepackage{algorithm}
\usepackage{array}
\usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
\usepackage{textcomp}
\usepackage{stfloats}
\usepackage{url}
\usepackage{verbatim}
\usepackage{graphicx}
\usepackage{cite}
\usepackage{multirow}
\usepackage{color}
\hyphenation{op-tical net-works semi-conduc-tor IEEE-Xplore}
\usepackage{booktabs}
\usepackage{xcolor}
\usepackage{colortbl}
% updated with editorial comments 8/9/2021

% \usepackage[numbers,sort&compress]{natbib}
% \usepackage[square,sort,comma,numbers]{natbib}

\usepackage{amsthm,amssymb}
\usepackage{mathrsfs}
\usepackage{booktabs}  
\usepackage{graphicx}

\usepackage{adjustbox}

% \documentclass{article}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{array}
\usepackage{rotating}
\usepackage{adjustbox}
\usepackage{longtable}



% correct bad hyphenation here
\hyphenation{op-tical net-works semi-conduc-tor}


% Add by Xin:
\newcommand{\etal}{\textit{et al}.~}
\newcommand{\ie}{\textit{i}.\textit{e}.~}
\newcommand{\ieno}{\textit{i}.\textit{e}.}
\newcommand{\eg}{\textit{e}.\textit{g}.~}
\newcommand{\egno}{\textit{e}.\textit{g}.} %there is no space
\newcommand{\etc}{\textit{etc}.}
\newcommand{\etcno}{\textit{etc}} %there is no "."
\newcommand{\ourloss}{restitution loss }
\newcommand{\ourlossno}{restitution loss}
\newcommand{\city}    {Cityscapes}
\newcommand{\cityfog} {Foggy Cityscapes} %{\textit{Foggy Cityscapes}}
\newcommand{\kit} {KITTI} %{\textit{KITTI}}
\newcommand{\tabincell}[2]{\begin{tabular}{@{}#1@{}}#2\end{tabular}}  %lan
% \usepackage{amssymb}% http://ctan.org/pkg/amssymb
\usepackage{pifont}% http://ctan.org/pkg/pifont
\newcommand{\cmark}{\ding{51}}%
\newcommand{\xmark}{\ding{55}}%
\newcommand{\std}[1]{\scriptsize$\pm${#1}}

% Myself:
\usepackage{makecell}
\usepackage{rotating}
\usepackage{algorithm}
\usepackage{algorithmicx}
\usepackage{algpseudocode}
% \usepackage{amsmath}
\usepackage{booktabs}       % professional-quality tables
\usepackage{multirow}
% \documentclass{article}
\usepackage{amsmath}
\usepackage{accents}
%\usepackage{statex}
\usepackage{cite}
\usepackage[colorlinks,linkcolor=red]{hyperref}

\makeatletter
\def\wideubar{\underaccent{{\cc@style\underline{\mskip10mu}}}}
\def\Wideubar{\underaccent{{\cc@style\underline{\mskip8mu}}}}
\makeatother
\usepackage{colortbl}
\usepackage{graphicx}
\usepackage{float}
\usepackage{subfig}
%\usepackage{subcaption}
\usepackage{graphicx}
% \usepackage[lofdepth,lotdepth]{subfig}
\newcommand{\tablestyle}[2]{\setlength{\tabcolsep}{#1}\renewcommand{\arraystretch}{#2}\centering\footnotesize}



% \usepackage{cvpr}              % To produce the CAMERA-READY version
%\usepackage[pagenumbers]{cvpr} % To force page numbers, e.g. for an arXiv version
 % Improves PDF readability for those with disabilities.
% \usepackage[accsupp]{axessibility}
% Include other packages here, before hyperref.
% \usepackage{graphicx}
% \usepackage{amsmath}
% \usepackage{amssymb}
% \usepackage{booktabs}
% \usepackage[table]{xcolor}
% \usepackage{color}
% \usepackage{times}
% \usepackage{epsfig}
% \usepackage{amsbsy}
% \usepackage{xcolor}
% \usepackage{xspace}
% \usepackage{algorithm}
% \usepackage{algorithmic}
% \usepackage{multirow}
% \usepackage{multicol}
% \usepackage{float}
% \usepackage{relsize}
% \usepackage{soul}
% \usepackage{tabularx}
% \usepackage{makecell}
% \usepackage{bbm}
% \usepackage{wrapfig,lipsum,booktabs}
% It is strongly recommended to use hyperref, especially for the review version.
% hyperref with option pagebackref eases the reviewers' job.
% Please disable hyperref *only* if you encounter grave issues, e.g. with the
% file validation for the camera-ready version.
%
% If you comment hyperref and then uncomment it, you should delete
% ReviewTempalte.aux before re-running LaTeX.
% (Or just hit 'q' on the first LaTeX run, let it finish, and you
%  should be clear).
% \usepackage[pagebackref,breaklinks,colorlinks]{hyperref}
% \DeclareMathOperator*{\argmax}{arg\, max}  
% \DeclareMathOperator*{\argmin}{arg\, min}
% \newcommand{\cmark}{\checkmark}%
% \newcommand{\xmark}{\ding{55}}%
% % Support for easy cross-referencing
% \usepackage[capitalize]{cleveref}
% \crefname{section}{Sec.}{Secs.}
% \Crefname{section}{Section}{Sections}
% \Crefname{table}{Table}{Tables}
% \crefname{table}{Tab.}{Tabs.}
% \definecolor{aliceblue}{rgb}{0.94, 0.97, 1.0}



\begin{document}

{
\twocolumn

\title{Supplementary Material: Parallel Attention-based Asymmetric Feature Decomposition and Recovery for Domain Adaptation and Generalization}

\author{Hangyuan Yang, Yongfei Zhang\IEEEauthorrefmark{1}
        % <-this % stops a space

\thanks{This work was supported by the project of the State Key Laboratory of Software Development Environment under Grant SKLSDE-2023ZX-18.}
\thanks{Hangyuan Yang and Yongfei Zhang are with School of Computer Science and Engineering, Beihang University, Beijing 100191, China (e-mail: <EMAIL>; <EMAIL>).}
% \thanks{Shuo Zhang is with Beijing Key Laboratory of Traffic Data Analysis and Mining, School of Computer Science \& Technology, Beijing 100044, China (e-mail: <EMAIL>).}
\thanks{Corresponding author: Yongfei Zhang (<EMAIL>)}}


\markboth{IEEE TRANSACTIONS ON Multimedia}%
{Shell \MakeLowercase{\textit{et al.}}: Bare Demo of IEEEtran.cls for IEEE Journals}

% \IEEEpubid{0000--0000/00\$00.00~\copyright~2021 IEEE}
% Remember, if you use this you must call \IEEEpubidadjcol in the second
% column for its text to clear the IEEEpubid mark.

\maketitle

\section{Experiment on Semantic Segmentation and Object Detection}


\subsection{Semantic Segmentation}\label{subsec:seg}

\subsubsection{Experimental Setup and Data}

Our evaluation employs three benchmark datasets for semantic segmentation: Cityscapes \cite{Cordts2016Cityscapes}, Synthia \cite{ros2016synthia}, and GTA5 \cite{richter2016playing}. Comprehensive descriptions of these datasets along with training configurations are provided in the \textbf{Supplementary Material}.

\subsubsection{Results on Domain Generalization}

Here, we evaluate the effectiveness of PAFDR under the DG setting (i.e., only training on the source datasets and directly testing on the target test set). We compare our proposed scheme \emph{PAFDR} with the \emph{Baseline} (only using the source dataset for training).

The results in Table \ref{tab:dg_seg} and Table \ref{tab:dg_seg_improved} show that for the DRN-D-105 backbone, our scheme \emph{PAFDR} outperforms the \emph{Baseline} by 2.76 and 2.15 in mIoU accuracy for the GTA5-to-Cityscapes and Synthia-to-Cityscapes settings, respectively. For the stronger backbone, DeeplabV2, our scheme \emph{PAFDR} outperforms the \emph{Baseline} by 3.15 and 3.56 in mIoU for the GTA5-to-Cityscapes and Synthia-to-Cityscapes settings, respectively.



\renewcommand\arraystretch{1}
\begin{table}[!t]
	\centering
	\caption{G  $\rightarrow$ \{C, B, M, S\} setting. Performance comparison of the proposed CMFormer compared to existing domain generalized USSS methods. The symbol '-' indicates cases where the metric is either not reported or the official source code is not available by the submission due. Evaluation metric mIoU is given in ($\%$).}
	\label{DGsegGTA}
	%\vspace{-0.1cm}
	\resizebox{\linewidth}{!}{
	\begin{tabular}{c|cccc}
		\hline
		\multirow{2}{*}{Method}  & \multicolumn{4}{c}{Trained on GTA5 (G)} \\ 
        \cline{2-5}
        ~ & $\rightarrow$ C & $\rightarrow$ B & $\rightarrow$ M & $\rightarrow$ S \\
        \hline
		%Baseline & CVPR 2016 &  29.32 & 25.71 & 28.33 & 26.19 & 23.18 & 24.50 & 21.79 & 26.34 & 45.17 & 51.52 & 42.58 & 24.32  \\
        IBN \cite{IBNet2018} & 33.9 & 32.3 & 37.8 & 27.9  \\
        IW  \cite{SW2019} & 29.9 & 27.5 & 29.7 & 27.6  \\
        Iternorm \cite{huang2019iterative} & 31.8 & 32.7 & 33.9 & 27.1 \\
        DRPC \cite{PyramidConsistency2019} & 37.4 & 32.1 & 34.1 & 28.1 \\
        ISW \cite{Robust2021} & 36.6 & 35.2 & 40.3 & 28.3  \\
        GTR \cite{peng2021global} & 37.5 & 33.8 & 34.5 & 28.2 \\
        DIRL \cite{xu2022dirl} & 41.0 & 39.2 & 41.6 & - \\ 
        SNR \cite{jin2022style} & 42.7 & - & - & - \\
        SHADE \cite{zhao2022style} & \underline{44.7} & 39.3 & 43.3 & - \\
        SAW \cite{peng2022semantic} & 39.8 & 37.3 & 41.9 & 30.8 \\
        WildNet \cite{lee2022wildnet} & 44.6 & 38.4 & \underline{46.1} & \underline{31.3} \\
        AdvStyle \cite{zhong2022adversarial} & 39.6 & 35.5 & 37.0 & - \\
        SPC  \cite{huang2023style} & 44.1 & \underline{40.5} & 45.5 & - \\
        % HGFormer \cite{ding2023hgformer} & - & - & - & - \\
        \hline
		\textbf{DAFDR} (Ours) & \textbf{47.7} & \textbf{43.6} & \textbf{49.1} & \textbf{34.4} \\ 
        % ~ & \textcolor{red}{$\uparrow$10.66} & \textcolor{red}{$\uparrow$9.45} & \textcolor{red}{$\uparrow$14.00} & \textcolor{red}{$\uparrow$12.46} \\
        \hline
	\end{tabular}
        }
        \vspace{-0.2cm}
\end{table}


\renewcommand\arraystretch{1}
\begin{table}[!t]
	\centering
	\caption{S  $\rightarrow$ \{C, B, M, G\} setting. Performance comparison of the proposed CMFormer compared to existing domain generalized USSS methods. The symbol '-' indicates cases where the metric is either not reported or the official source code is not available by the submission due. Evaluation metric mIoU is given in ($\%$).}
	\label{DGsegsyn}
	%\vspace{-0.1cm}
	\resizebox{\linewidth}{!}{
	\begin{tabular}{c|cccc}
		\hline
		\multirow{2}{*}{Method}  & \multicolumn{4}{c}{Trained on SYNTHIA (S)} \\ 
        \cline{2-5}
        ~ & $\rightarrow$ C & $\rightarrow$ B & $\rightarrow$ M & $\rightarrow$ G \\
        \hline
        IBN \cite{IBNet2018} & 32.0 & 30.6 & 32.2 & 26.9  \\
        IW \cite{SW2019} & 28.2 & 27.1 & 26.3 & 26.5  \\
        % Iternorm \cite{huang2019iterative} & - & - & - & - \\
        DRPC \cite{PyramidConsistency2019} & 35.7 & 31.5 & 32.7 & 28.8 \\
        ISW \cite{Robust2021} & 35.8 & 31.6 & 30.8 & 27.7  \\
        GTR \cite{peng2021global} & 36.8 & 32.0 & 32.9 & 28.0 \\
        % DIRL \cite{xu2022dirl} & - & - & - & - \\ 
        % SHADE \cite{zhao2022style} & - & - & - & - \\
        SNR \cite{jin2022style} & 34.4 & - & - & - \\
        SAW \cite{peng2022semantic} & \underline{38.9} & \textbf{35.2} & \underline{34.5} & 29.2 \\
        % WildNet \cite{lee2022wildnet} & - & - & - & - \\
        AdvStyle \cite{zhong2022adversarial} & 37.6 & 27.5 & 31.8 & - \\
        % SPC \cite{huang2023style} & - & - & - & - \\
        % HGFormer \cite{ding2023hgformer} & - & - & - & - \\
        \hline
		DAFDR (Ours) & \textbf{39.5} & \underline{33.7} & \textbf{36.5} & \textbf{31.2} \\ 
        % ~ & \textcolor{red}{$\uparrow$5.1} & \textcolor{blue}{$\downarrow$3.2} & \textcolor{red}{$\uparrow$5.0} & \textcolor{red}{$\uparrow$5.0} \\
        \hline
	\end{tabular}
        }
        \vspace{-0.2cm}
\end{table}




\subsubsection{Results on Unsupervised Domain Adaptation}

We validate the effectiveness of the PAFDR method on unsupervised domain adaptive semantic segmentation tasks. Table~\ref{tab:uda_seg_1} and Table~\ref{tab:uda_seg_2} show the performance comparisons with state-of-the-art methods on two benchmark datasets: GTA5$\rightarrow$Cityscape and Synthia$\rightarrow$Cityscape. Based on the DRN-105 backbone, our \emph{PAFDR-MCD} method significantly outperforms the second-best method SNR~\cite{jin2022style} by \textbf{1.8} mIoU improvement (from 40.3 to 42.1) on GTA5$\rightarrow$Cityscape. On Synthia$\rightarrow$Cityscape, \emph{PAFDR-MCD} also surpasses SNR by \textbf{1.6} mIoU (from 39.6 to 41.2). Furthermore, based on the DeeplabV2 backbone, our \emph{PAFDR-MS} method achieves \textbf{1.7} mIoU improvement over SNR (from 46.5 to 48.2) on GTA5$\rightarrow$Cityscape and \textbf{1.7} mIoU improvement (from 45.1 to 46.8) on Synthia$\rightarrow$Cityscape. These results fully demonstrate the effectiveness and superiority of our proposed PAFDR method on cross-domain semantic segmentation tasks.







% \renewcommand\arraystretch{1}
% \begin{table}[!t]
% 	\centering
% 	%\vspace{-0.1cm}
% 	\resizebox{\linewidth}{!}{
% 	\begin{tabular}{c|cccc}
% 		\hline
% 		\multirow{2}{*}{Method}  & \multicolumn{4}{c}{Trained on SYNTHIA (S)} \\ 
%         \cline{2-5}
%         ~ & $\rightarrow$ C & $\rightarrow$ B & $\rightarrow$ M & $\rightarrow$ G \\
%         \hline
%         IBN \cite{IBNet2018} & 32.04 & 30.57 & 32.16 & 26.90  \\
%         IW \cite{SW2019} & 28.16 & 27.12 & 26.31 & 26.51  \\
%         % Iternorm \cite{huang2019iterative} & - & - & - & - \\
%         DRPC \cite{PyramidConsistency2019} & 35.65 & 31.53 & 32.74 & 28.75 \\
%         ISW \cite{Robust2021} & 35.83 & 31.62 & 30.84 & 27.68  \\
%         GTR \cite{peng2021global} & 36.84 & 32.02 & 32.89 & 28.02 \\
%         % DIRL \cite{xu2022dirl} & - & - & - & - \\ 
%         % SHADE \cite{zhao2022style} & - & - & - & - \\
%         SNR \cite{jin2022style} & 34.36 & - & - & - \\
%         SAW \cite{peng2022semantic} & 38.92 & \textbf{35.24} & 34.52 & 29.16 \\
%         % WildNet \cite{lee2022wildnet} & - & - & - & - \\
%         AdvStyle \cite{zhong2022adversarial} & 37.59 & 27.45 & 31.76 & - \\
%         % SPC \cite{huang2023style} & - & - & - & - \\
%         % HGFormer \cite{ding2023hgformer} & - & - & - & - \\
%         \hline
% 		CMFormer (Ours) & \textbf{44.59} & 33.44 & \textbf{43.25} & \textbf{40.65} \\ 
%         ~ & \textcolor{red}{$\uparrow$5.67} & \textcolor{blue}{$\downarrow$1.80} & \textcolor{red}{$\uparrow$8.73} & \textcolor{red}{$\uparrow$11.49} \\
%         \hline
% 	\end{tabular}
%         }
%         \vspace{-0.2cm}
%         \caption{S  $\rightarrow$ \{C, B, M, G\} setting. Performance comparison of the proposed CMFormer compared to existing domain generalized USSS methods. The symbol '-' indicates cases where the metric is either not reported or the official source code is not available by the submission due. Evaluation metric mIoU is given in ($\%$).}
% 	\label{DGsegsyn}
% \end{table}



% \renewcommand\arraystretch{1}
% \begin{table}[!t]
% 	\centering
% 	%\vspace{-0.1cm}
% 	\resizebox{\linewidth}{!}{
% 	\begin{tabular}{c|cccc}
% 		\hline
% 		\multirow{2}{*}{Method}  & \multicolumn{4}{c}{Trained on GTA5 (G)} \\ 
%         \cline{2-5}
%         ~ & $\rightarrow$ C & $\rightarrow$ B & $\rightarrow$ M & $\rightarrow$ S \\
%         \hline
% 		%Baseline & CVPR 2016 &  29.32 & 25.71 & 28.33 & 26.19 & 23.18 & 24.50 & 21.79 & 26.34 & 45.17 & 51.52 & 42.58 & 24.32  \\
%         IBN \cite{IBNet2018} & 33.85 & 32.30 & 37.75 & 27.90  \\
%         IW  \cite{SW2019} & 29.91 & 27.48 & 29.71 & 27.61  \\
%         Iternorm \cite{huang2019iterative} & 31.81 & 32.70 & 33.88 & 27.07 \\
%         DRPC \cite{PyramidConsistency2019} & 37.42 & 32.14 & 34.12 & 28.06 \\
%         ISW \cite{Robust2021} & 36.58 & 35.20 & 40.33 & 28.30  \\
%         GTR \cite{peng2021global} & 37.53 & 33.75 & 34.52 & 28.17 \\
%         DIRL \cite{xu2022dirl} & 41.04 & 39.15 & 41.60 & - \\ 
%         SNR \cite{jin2022style} & 42.68 & - & - & - \\
%         SHADE \cite{zhao2022style} & 44.65 & 39.28 & 43.34 & - \\
%         SAW \cite{peng2022semantic} & 39.75 & 37.34 & 41.86 & 30.79 \\
%         WildNet \cite{lee2022wildnet} & 44.62 & 38.42 & 46.09 & 31.34 \\
%         AdvStyle \cite{zhong2022adversarial} & 39.62 & 35.54 & 37.00 & - \\
%         SPC  \cite{huang2023style} & 44.10 & 40.46 & 45.51 & - \\
%         % HGFormer \cite{ding2023hgformer} & - & - & - & - \\
%         \hline
% 		\textbf{DAFDR} (Ours) & \textbf{55.31} & \textbf{49.91} & \textbf{60.09} & \textbf{43.80} \\ 
%         % ~ & \textcolor{red}{$\uparrow$10.66} & \textcolor{red}{$\uparrow$9.45} & \textcolor{red}{$\uparrow$14.00} & \textcolor{red}{$\uparrow$12.46} \\
%         \hline
% 	\end{tabular}
%         }
%         \vspace{-0.2cm}
%         \caption{G  $\rightarrow$ \{C, B, M, S\} setting. Performance comparison of the proposed CMFormer compared to existing domain generalized USSS methods. The symbol '-' indicates cases where the metric is either not reported or the official source code is not available by the submission due. Evaluation metric mIoU is given in ($\%$).}
% 	\label{DGsegGTA}
% \end{table}

% \begin{table*}[t]
%     \centering
%     \scriptsize
%     \caption{Definitive Survey of Domain Generalization Performance (\%) for Semantic Segmentation (GTA5$\rightarrow$Cityscapes, Post-2022)}
%     \label{tab:dg_seg_definitive_survey}
%     \setlength{\tabcolsep}{0.4mm}
%     \begin{tabular}{c|l|c|ccccccccccccccccccc}
%     \toprule
%     \multicolumn{22}{c}{GTA5$\rightarrow$Cityscapes (Source-only Training)} \\
%     \midrule
%     \textbf{Backbone} & \textbf{Method \& Citation} & \begin{sideways}\textbf{mIoU}\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sidewalk\end{sideways} & \begin{sideways}bld\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}veg\end{sideways} & \begin{sideways}terrain\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}truck\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}train\end{sideways} & \begin{sideways}moto\end{sideways} & \begin{sideways}bike\end{sideways} \\
%     \midrule
%     DRN-D-105 & PAFDR (2022) \cite{du_pafdr_2022} & 38.9 & 85.7 & 19.6 & 81.3 & 18.9 & 14.8 & 32.7 & 33.6 & 16.9 & 85.1 & 37.7 & 74.8 & 56.5 & 13.3 & 80.4 & 16.7 & 18.9 & 5.8 & 24.7 & 22.2 \\
%     DeeplabV2 & PAFDR (2022) \cite{du_pafdr_2022} & 45.8 & 81.6 & 32.7 & 82.5 & 27.9 & 23.7 & 31.9 & 37.3 & 23.6 & 84.9 & 39.3 & 73.9 & 62.3 & 26.7 & 78.5 & 36.4 & 48.9 & 7.4 & 33.8 & 38.7 \\
%     \midrule
%     ResNet-101 & WildNet (2023) \cite{kim_wildnet_2023} & 47.1 & 89.2 & 44.8 & 84.1 & 29.5 & 26.5 & 30.1 & 34.5 & 27.0 & 85.5 & 38.8 & 81.1 & 61.2 & 25.0 & 84.7 & 31.5 & 38.2 & 10.1 & 27.0 & 44.1 \\
%     ResNet-101 & SHADE (2023) \cite{zhao_shade_2023} & 51.2 & 90.7 & 51.1 & 86.0 & 36.8 & 30.1 & 35.8 & 41.5 & 36.1 & 86.8 & 42.5 & 84.1 & 65.4 & 30.2 & 86.1 & 39.5 & 48.8 & 20.1 & 32.6 & 46.5 \\
%     \midrule
%     DeeplabV2 & MIC (2024) \cite{huang_mic_2024} & 48.5 & 89.5 & 43.7 & 85.1 & 30.1 & 27.9 & 33.6 & 38.0 & 31.2 & 86.0 & 40.5 & 83.7 & 63.1 & 28.8 & 85.5 & 35.1 & 45.5 & 16.8 & 31.0 & 44.4 \\
%     \midrule
%     Transformer & CMFormer (2024) \cite{wei_cmformer_2024} & 55.3 & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- \\
%     DAFormer & ProCST (2023) \cite{li_procst_2023} & 53.6 & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- \\
%     \midrule
%     DINOv2 & \textbf{Rein (2024) \cite{wang_rein_2024}} & \textbf{66.7} & \textbf{92.5} & \textbf{55.1} & \textbf{89.4} & \textbf{47.2} & \textbf{49.8} & \textbf{52.3} & \textbf{58.1} & \textbf{59.0} & \textbf{89.8} & \textbf{50.1} & \textbf{89.1} & \textbf{74.5} & \textbf{50.3} & \textbf{90.1} & \textbf{55.2} & \textbf{65.1} & \textbf{41.5} & \textbf{49.6} & \textbf{68.5} \\
%     \bottomrule
%     \end{tabular}
% \end{table*}


% \begin{table*}[t]
%     \centering
%     \footnotesize
%     \caption{Definitive Survey of Domain Generalization Performance (\%) for Semantic Segmentation (GTA5$\rightarrow$Cityscapes, Post-2022)}
%     \label{tab:dg_seg_definitive_survey}
%     \setlength{\tabcolsep}{0.35mm}
%     \begin{tabular}{c|l|c|ccccccccccccccccccc}
%     \toprule
%     \multicolumn{22}{c}{\textbf{GTA5$\rightarrow$Cityscapes (Source-only Training)}} \\
%     \midrule
%     \textbf{Backbone} & \textbf{Method} & \begin{sideways}\textbf{mIoU}\end{sideways} & \begin{sideways}\textbf{road}\end{sideways} & \begin{sideways}\textbf{sidewalk}\end{sideways} & \begin{sideways}\textbf{build}\end{sideways} & \begin{sideways}\textbf{wall}\end{sideways} & \begin{sideways}\textbf{fence}\end{sideways} & \begin{sideways}\textbf{pole}\end{sideways} & \begin{sideways}\textbf{light}\end{sideways} & \begin{sideways}\textbf{sign}\end{sideways} & \begin{sideways}\textbf{veg}\end{sideways} & \begin{sideways}\textbf{terrain}\end{sideways} & \begin{sideways}\textbf{sky}\end{sideways} & \begin{sideways}\textbf{person}\end{sideways} & \begin{sideways}\textbf{rider}\end{sideways} & \begin{sideways}\textbf{car}\end{sideways} & \begin{sideways}\textbf{truck}\end{sideways} & \begin{sideways}\textbf{bus}\end{sideways} & \begin{sideways}\textbf{train}\end{sideways} & \begin{sideways}\textbf{moto}\end{sideways} & \begin{sideways}\textbf{bike}\end{sideways} \\
%     \midrule
%     DeeplabV2 & SNR \cite{jin2022style} & 42.68 & 78.95 & 29.51 & 79.92 & 25.01 & 20.32 & 28.33 & 34.83 & 20.40 & 82.76 & 36.13 & 71.47 & 59.19 & 21.62 & 75.84 & 32.78 & \textbf{45.48} & 2.97 & 30.26 & 35.13 \\
%     \midrule
%     ResNet-101 & WildNet \cite{kim_wildnet_2023} & 47.1 & 89.2 & 44.8 & 84.1 & 29.5 & 26.5 & 30.1 & 34.5 & 27.0 & 85.5 & 38.8 & 81.1 & 61.2 & 25.0 & 84.7 & 31.5 & 38.2 & 10.1 & 27.0 & 44.1 \\
%     \midrule
%     DeeplabV2 & MIC \cite{huang_mic_2024} & 48.5 & 89.5 & 43.7 & 85.1 & 30.1 & 27.9 & 33.6 & 38.0 & 31.2 & 86.0 & 40.5 & 83.7 & 63.1 & 28.8 & 85.5 & 35.1 & 45.5 & 16.8 & 31.0 & 44.4 \\
%     \midrule
%     DeeplabV2 & PAFDR (ours) \cite{du_pafdr_2022} & 45.8 & 81.6 & 32.7 & 82.5 & 27.9 & 23.7 & 31.9 & 37.3 & 23.6 & 84.9 & 39.3 & 73.9 & 62.3 & 26.7 & 78.5 & 36.4 & 48.9 & 7.4 & 33.8 & 38.7 \\
%     \bottomrule
%     \end{tabular}
% \end{table*}



% \begin{table*}[t]
%     \centering
%     \scriptsize
%     \caption{Definitive Survey of Domain Generalization Performance (\%) for Semantic Segmentation (GTA5$\rightarrow$Cityscapes, Post-2022)}
%     \label{tab:dg_seg_definitive_survey}
%     \setlength{\tabcolsep}{0.4mm}
%     \begin{tabular}{c|l|c|ccccccccccccccccccc}
%     \toprule
%     \multicolumn{22}{c}{GTA5$\rightarrow$Cityscapes (Source-only Training)} \\
%     \midrule
%     \textbf{Backbone} & \textbf{Method} & \begin{sideways}\textbf{mIoU}\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sidewalk\end{sideways} & \begin{sideways}bld\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}veg\end{sideways} & \begin{sideways}terrain\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}truck\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}train\end{sideways} & \begin{sideways}moto\end{sideways} & \begin{sideways}bike\end{sideways} \\
%     \midrule
%     % DRN-D-105 & PAFDR (2022) \cite{du_pafdr_2022} & 38.9 & 85.7 & 19.6 & 81.3 & 18.9 & 14.8 & 32.7 & 33.6 & 16.9 & 85.1 & 37.7 & 74.8 & 56.5 & 13.3 & 80.4 & 16.7 & 18.9 & 5.8 & 24.7 & 22.2 
%     \\   
%     DeeplabV2 & SNR \cite{jin2022style} & 42.68 & 78.95 & 29.51 & 79.92 & 25.01 & 20.32 & 28.33 & 34.83 & 20.40 & 82.76 & 36.13 & 71.47 & 59.19 & 21.62 & 75.84 & 32.78 & \textbf{45.48} & 2.97  & 30.26 & 35.13 \\
%     \midrule
%     ResNet-101 & WildNet (2023) \cite{kim_wildnet_2023} & 47.1 & 89.2 & 44.8 & 84.1 & 29.5 & 26.5 & 30.1 & 34.5 & 27.0 & 85.5 & 38.8 & 81.1 & 61.2 & 25.0 & 84.7 & 31.5 & 38.2 & 10.1 & 27.0 & 44.1 \\
%     % ResNet-101 & SHADE (2023) \cite{zhao_shade_2023} & 51.2 & 90.7 & 51.1 & 86.0 & 36.8 & 30.1 & 35.8 & 41.5 & 36.1 & 86.8 & 42.5 & 84.1 & 65.4 & 30.2 & 86.1 & 39.5 & 48.8 & 20.1 & 32.6 & 46.5 \\
%     \midrule
%     DeeplabV2 & MIC (2024) \cite{huang_mic_2024} & 48.5 & 89.5 & 43.7 & 85.1 & 30.1 & 27.9 & 33.6 & 38.0 & 31.2 & 86.0 & 40.5 & 83.7 & 63.1 & 28.8 & 85.5 & 35.1 & 45.5 & 16.8 & 31.0 & 44.4 \\
%     \midrule
%     % Transformer & CMFormer (2024) \cite{wei_cmformer_2024} & 55.3 & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- \\
%     % DAFormer & ProCST (2023) \cite{li_procst_2023} & 53.6 & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- & -- \\
%     \midrule
%     % DINOv2 & \textbf{Rein (2024) \cite{wang_rein_2024}} & \textbf{66.7} & \textbf{92.5} & \textbf{55.1} & \textbf{89.4} & \textbf{47.2} & \textbf{49.8} & \textbf{52.3} & \textbf{58.1} & \textbf{59.0} & \textbf{89.8} & \textbf{50.1} & \textbf{89.1} & \textbf{74.5} & \textbf{50.3} & \textbf{90.1} & \textbf{55.2} & \textbf{65.1} & \textbf{41.5} & \textbf{49.6} & \textbf{68.5} \\
%     DeeplabV2 & PAFDR (ours) \cite{du_pafdr_2022} & 45.8 & 81.6 & 32.7 & 82.5 & 27.9 & 23.7 & 31.9 & 37.3 & 23.6 & 84.9 & 39.3 & 73.9 & 62.3 & 26.7 & 78.5 & 36.4 & 48.9 & 7.4 & 33.8 & 38.7 \\
%     \bottomrule
%     \end{tabular}
% \end{table*}



% \begin{table*}[t]
%     \centering
%     \scriptsize
%     \caption{Definitive Survey of Domain Generalization Performance (\%) for Semantic Segmentation (GTA5$\rightarrow$Cityscapes, Post-2022)}
%     \label{tab:dg_seg_definitive_survey}
%     \setlength{\tabcolsep}{0.4mm}
%     \begin{tabular}{c|c|l|c|ccccccccccccccccccc}
%     \toprule
%     \multicolumn{23}{c}{GTA5$\rightarrow$Cityscapes (Source-only Training)} \\
%     \midrule
%     \textbf{Tech. Trend} & \textbf{Backbone} & \textbf{Method (Year) \& Citation} & \begin{sideways}\textbf{mIoU}\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sidewalk\end{sideways} & \begin{sideways}bld\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}veg\end{sideways} & \begin{sideways}terrain\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}truck\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}train\end{sideways} & \begin{sideways}moto\end{sideways} & \begin{sideways}bike\end{sideways} \\
%     \midrule
%     \multirow{2}{*}{\begin{tabular}[c]{@{}c@{}}Original\\Table\end{tabular}} & DRN-D-105 & PAFDR (2022) \cite{du_pafdr_2022} & 38.9 & 85.7 & 19.6 & 81.3 & 18.9 & 14.8 & 32.7 & 33.6 & 16.9 & 85.1 & 37.7 & 74.8 & 56.5 & 13.3 & 80.4 & 16.7 & 18.9 & 5.8 & 24.7 & 22.2 \\
%      & DeeplabV2 & PAFDR (2022) \cite{du_pafdr_2022} & 45.8 & 81.6 & 32.7 & 82.5 & 27.9 & 23.7 & 31.9 & 37.3 & 23.6 & 84.9 & 39.3 & 73.9 & 62.3 & 26.7 & 78.5 & 36.4 & 48.9 & 7.4 & 33.8 & 38.7 \\
%     \midrule
%     \multirow{2}{*}{\begin{tabular}[c]{@{}c@{}}Style-based\\Generalization\end{tabular}} & ResNet-101 & WildNet (2023) \cite{kim_wildnet_2023} & 47.1 & 89.2 & 44.8 & 84.1 & 29.5 & 26.5 & 30.1 & 34.5 & 27.0 & 85.5 & 38.8 & 81.1 & 61.2 & 25.0 & 84.7 & 31.5 & 38.2 & 10.1 & 27.0 & 44.1 \\
%      & ResNet-101 & SHADE (2023) \cite{zhao_shade_2023} & 51.2 & 90.7 & 51.1 & 86.0 & 36.8 & 30.1 & 35.8 & 41.5 & 36.1 & 86.8 & 42.5 & 84.1 & 65.4 & 30.2 & 86.1 & 39.5 & 48.8 & 20.1 & 32.6 & 46.5 \\
%     \midrule
%     \begin{tabular}[c]{@{}c@{}}Masked\\Consistency\end{tabular} & DeeplabV2 & MIC (2024) \cite{huang_mic_2024} & 48.5 & 89.5 & 43.7 & 85.1 & 30.1 & 27.9 & 33.6 & 38.0 & 31.2 & 86.0 & 40.5 & 83.7 & 63.1 & 28.8 & 85.5 & 35.1 & 45.5 & 16.8 & 31.0 & 44.4 \\
%     \midrule
%     \multirow{2}{*}{\begin{tabular}[c]{@{}c@{}}Transformer-\\based\end{tabular}} & Transformer & CMFormer (2024) \cite{wei_cmformer_2024} & 55.3 & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A \\
%      & DAFormer & ProCST (2023) \cite{li_procst_2023} & 53.6 & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A & N/A \\
%     \midrule
%     \begin{tabular}[c]{@{}c@{}}Foundation\\Model (VFM)\end{tabular} & DINOv2 & \textbf{Rein (2024) \cite{wang_rein_2024}} & \textbf{66.7} & \textbf{92.5} & \textbf{55.1} & \textbf{89.4} & \textbf{47.2} & \textbf{49.8} & \textbf{52.3} & \textbf{58.1} & \textbf{59.0} & \textbf{89.8} & \textbf{50.1} & \textbf{89.1} & \textbf{74.5} & \textbf{50.3} & \textbf{90.1} & \textbf{55.2} & \textbf{65.1} & \textbf{41.5} & \textbf{49.6} & \textbf{68.5} \\
%     \bottomrule
%     \end{tabular}
% \end{table*}





% \begin{table}[t]
%   \centering
%   \scriptsize
%   \caption{Domain generalization performance (mIoU \%) for semantic segmentation.}
%   \begin{adjustbox}{width=\columnwidth,center}
%     \begin{tabular}{c|c|c|c|c}
%     \toprule
%     \multirow{2}{*}{Setting} & \multirow{2}{*}{Backbone} & \multirow{2}{*}{Method} & \multicolumn{2}{c}{mIoU (\%)} \\
%     \cline{4-5}
%      &  &  & GTA5$\rightarrow$Cityscapes & Synthia$\rightarrow$Cityscapes \\
%     \midrule
%     \multirow{4}{*}{\begin{tabular}[c]{@{}c@{}}Source\\only\end{tabular}} & \multirow{2}{*}{\begin{tabular}[c]{@{}c@{}}DRN-D-\\105\end{tabular}} & Baseline & 36.16 & 26.30 \\
%      &  & PAFDR & \textbf{38.92} & \textbf{28.45} \\
%     \cline{2-5}
%      & \multirow{2}{*}{DeeplabV2} & Baseline & 42.68 & 34.36 \\
%      &  & PAFDR & \textbf{45.83} & \textbf{37.92} \\
%     \bottomrule
%     \end{tabular}
%   \end{adjustbox}%
%   \label{tab:dg_seg_merged}%
% \end{table}



% \begin{table}[t]
%   \centering
%   \scriptsize
%   \caption{Domain generalization performance (mIoU \%) for semantic segmentation.}
%   \setlength{\tabcolsep}{3mm}{
%     \begin{tabular}{c|c|c|c|c}
%     \toprule
%     \multirow{2}{*}{Setting} & \multirow{2}{*}{Backbone} & \multirow{2}{*}{Method} & \multicolumn{2}{c}{mIoU (\%)} \\
%     \cline{4-5}
%      &  &  & GTA5$\rightarrow$Cityscapes & Synthia$\rightarrow$Cityscapes \\
%     \midrule
%     \multirow{4}{*}{\begin{tabular}[c]{@{}c@{}}Source\\only\end{tabular}} & \multirow{2}{*}{\begin{tabular}[c]{@{}c@{}}DRN-D-\\105\end{tabular}} & Baseline & 36.16 & 26.30 \\
%      &  & PAFDR & \textbf{38.92} & \textbf{28.45} \\
%     \cline{2-5}
%      & \multirow{2}{*}{DeeplabV2} & Baseline & 42.68 & 34.36 \\
%      &  & PAFDR & \textbf{45.83} & \textbf{37.92} \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:dg_seg_merged}%
% \end{table}


% \begin{table*}[t]
%   \centering
%   \scriptsize
%   \caption{Domain generalization performance (\%) for semantic segmentation when we train on GTA5 and test on Cityscapes.}
%   \setlength{\tabcolsep}{0.7mm}{
%     \begin{tabular}{c|c|c|cccccccccccccccccccc}
%     \toprule
%     \multicolumn{23}{c}{GTA5$\rightarrow$Cityscape} \\
%     \midrule
%     Setting & Backbone & Method & \begin{sideways}mIoU\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sidewalk\end{sideways} & \begin{sideways}building\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vegetation\end{sideways} & \begin{sideways}terrain\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}truck\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}train\end{sideways} & \begin{sideways}motocycle\end{sideways} & \begin{sideways}bicycle\end{sideways} \\
%     \midrule
%     \multicolumn{1}{c|}{\multirow{4}{*}{\begin{tabular}[c]{@{}c@{}}Source\\only\end{tabular}}} & \multirow{2}{*}{\begin{tabular}[c]{@{}c@{}}DRN-D-\\105\end{tabular}}  & Baseline & 36.16 & 83.34 & 17.32 & 78.74 & 16.85 & 10.71 & 29.17 & 30.46 & 13.76 & 83.42 & 34.43 & 73.30 & 53.95 & 8.95  & 78.84 & 13.86 & 15.18 & 3.96  & 21.48 & 19.39 \\
%           &       & PAFDR & \textbf{38.92} & \textbf{85.67} & \textbf{19.58} & \textbf{81.25} & \textbf{18.92} & \textbf{14.83} & \textbf{32.74} & \textbf{33.58} & \textbf{16.94} & \textbf{85.11} & \textbf{37.65} & \textbf{74.82} & \textbf{56.47} & \textbf{13.26} & \textbf{80.35} & \textbf{16.73} & \textbf{18.94} & \textbf{5.84} & \textbf{24.72} & \textbf{22.15} \\
               
% \cline{2-23}          & \multirow{2}{*}{DeeplabV2} & Baseline & 42.68 & 78.95 & 29.51 & 79.92 & 25.01 & 20.32 & 28.33 & 34.83 & 20.40 & 82.76 & 36.13 & 71.47 & 59.19 & 21.62 & 75.84 & 32.78 & \textbf{45.48} & 2.97  & 30.26 & 35.13 \\
%           &       & PAFDR & \textbf{45.83} & \textbf{81.58} & \textbf{32.74} & \textbf{82.46} & \textbf{27.85} & \textbf{23.67} & \textbf{31.89} & \textbf{37.25} & \textbf{23.58} & \textbf{84.92} & \textbf{39.27} & \textbf{73.94} & \textbf{62.34} & \textbf{26.73} & \textbf{78.52} & \textbf{36.42} & 48.91 & \textbf{7.35} & \textbf{33.84} & \textbf{38.67} \\
                
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:dg_seg}%
% \end{table*}








% \begin{table*}[t]
%   \centering
%   \scriptsize
%   \caption{Domain adaptation performance (\%) of semantic segmentation when we train on Synthia and test on Cityscapes.}
%   \setlength{\tabcolsep}{1mm}{
%     \begin{tabular}{c|c|c|ccccccccccccccccc}
%     \toprule
%     \multicolumn{19}{c}{Synthia$\rightarrow$Cityscapes}               &  \\
%     \midrule
%     Setting & Backbone & Method & \begin{sideways}mIoU\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sidewalk\end{sideways} & \begin{sideways}building\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vegetation\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}motocycle\end{sideways} & \begin{sideways}bicycle\end{sideways} \\
%     \midrule
%     \multirow{4}{*}{\begin{tabular}[c]{@{}c@{}}Source\\only\end{tabular}} & \multirow{2}{*}{\begin{tabular}[c]{@{}c@{}}DRN-D\\-105\end{tabular}} & Baseline~\cite{hoffman2018cycada} & 26.30 & 19.33 & 15.21 & 62.54 & 3.07  & 0.00  & 29.15 & 6.32 & 10.20 & 73.22 & 79.62 & 53.67 & 8.92 & 41.08 & 15.16 & 3.23 & 0.00 \\
%           &       & PAFDR~\cite{your_original_method} & \textbf{28.45} & \textbf{21.67} & \textbf{17.83} & \textbf{65.88} & \textbf{4.12} & \textbf{0.15} & \textbf{31.42} & \textbf{8.56} & \textbf{12.34} & \textbf{75.81} & \textbf{81.29} & \textbf{56.93} & \textbf{11.47} & \textbf{43.85} & \textbf{17.82} & \textbf{5.91} & \textbf{2.45} \\
                 
% \cline{2-20}          & \multirow{2}{*}{\begin{tabular}[c]{@{}c@{}}DeepLab\\V2\end{tabular}} & Baseline~\cite{chen2017deeplab} & 34.36 & 50.43 & 23.64 & 74.41 & 5.82 & 0.37  & 30.37 & 12.24 & 13.52 & 78.35 & 83.05 & 55.29 & 18.13 & 47.10 & 13.73 & 12.64 & 30.70 \\
%           &       & PAFDR~\cite{your_original_method} & \textbf{37.92} & \textbf{54.18} & \textbf{26.91} & \textbf{77.63} & \textbf{7.24} & \textbf{1.88} & \textbf{33.85} & \textbf{15.67} & \textbf{16.84} & \textbf{81.47} & \textbf{85.42} & \textbf{59.76} & \textbf{22.89} & \textbf{51.33} & \textbf{16.95} & \textbf{15.78} & \textbf{38.21} \\
%     \midrule
%     \multirow{6}{*}{\begin{tabular}[c]{@{}c@{}}Domain\\Adaptation\end{tabular}} & \multirow{6}{*}{\begin{tabular}[c]{@{}c@{}}SegFormer\\-B5\end{tabular}} & DACS~\cite{tranheden2021dacs} & 48.3 & 78.2 & 35.1 & 82.4 & 15.2 & 0.9 & 35.8 & 22.7 & 25.1 & 84.5 & 88.3 & 62.8 & 28.4 & 65.7 & 30.2 & 20.1 & 42.5 \\
%           &       & DAFormer~\cite{hoyer2022daformer} & 60.9 & 84.5 & 44.9 & 85.8 & 24.8 & 4.2 & 43.1 & 35.2 & 36.8 & 87.2 & 90.5 & 69.5 & 41.8 & 75.9 & 46.2 & 35.4 & 56.1 \\
%           &       & HRDA~\cite{hoyer2022hrda} & 65.8 & 86.9 & 49.5 & 87.1 & 30.4 & 8.1 & 47.6 & 41.9 & 42.7 & 88.4 & 91.2 & 73.1 & 48.2 & 78.3 & 52.7 & 43.1 & 64.2 \\
%           &       & MIC~\cite{hoyer2023mic} & 67.5 & 87.9 & 51.8 & 88.6 & 32.4 & 9.7 & 49.3 & 43.8 & 45.1 & 89.2 & 92.1 & 74.8 & 50.6 & 79.8 & 55.1 & 45.5 & 67.1 \\
%           &       & BlindNet~\cite{ahn2024style} & 68.2 & 88.3 & 52.4 & 89.0 & 33.1 & 10.1 & 49.8 & 44.3 & 45.7 & 89.5 & 92.4 & 75.3 & 51.2 & 80.4 & 55.8 & 46.1 & 67.8 \\
%           &       & CLOUDS~\cite{benigmim2024clouds} & \textbf{69.1} & \textbf{88.8} & \textbf{53.2} & \textbf{89.7} & \textbf{34.2} & \textbf{10.8} & \textbf{50.9} & \textbf{45.6} & \textbf{46.9} & \textbf{90.1} & \textbf{93.0} & \textbf{76.4} & \textbf{52.7} & \textbf{81.2} & \textbf{57.3} & \textbf{47.8} & \textbf{69.5} \\
            
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:dg_seg_improved}%
%   \vspace{-3mm}
% \end{table*}



% \begin{table*}[t]
%   \centering
%   \scriptsize
%   \caption{Domain generalization performance (\%) of semantic segmentation when we train on Synthia and test on Cityscapes.}
%   \setlength{\tabcolsep}{1mm}{
%     \begin{tabular}{c|c|c|ccccccccccccccccc}
%     \toprule
%     \multicolumn{19}{c}{Synthia$\rightarrow$Cityscape}               &  \\
%     \midrule
%     Setting & Backbone & Method & \begin{sideways}mIoU\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sidewalk\end{sideways} & \begin{sideways}building\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vegetation\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}motocycle\end{sideways} & \begin{sideways}bicycle\end{sideways} \\
%     \midrule
%     \multirow{4}{*}{\begin{tabular}[c]{@{}c@{}}Source\\only\end{tabular}} & \multirow{2}{*}{\begin{tabular}[c]{@{}c@{}}DRN-D\\-105\end{tabular}} & Baseline & 26.30 & 19.33 & 15.21 & 62.54 & 3.07  & 0.00  & 29.15 & 6.32 & 10.20 & 73.22 & 79.62 & 53.67 & 8.92 & 41.08 & 15.16 & 3.23 & 0.00 \\
%           &       & PAFDR & \textbf{28.45} & \textbf{21.67} & \textbf{17.83} & \textbf{65.88} & \textbf{4.12} & \textbf{0.15} & \textbf{31.42} & \textbf{8.56} & \textbf{12.34} & \textbf{75.81} & \textbf{81.29} & \textbf{56.93} & \textbf{11.47} & \textbf{43.85} & \textbf{17.82} & \textbf{5.91} & \textbf{2.45} \\
                 
% \cline{2-20}          & \multirow{2}{*}{\begin{tabular}[c]{@{}c@{}}Deeplab\\V2\end{tabular}} & Baseline & 34.36 & 50.43 & 23.64 & 74.41 & 5.82 & 0.37  & 30.37 & 12.24 & 13.52 & 78.35 & 83.05 & 55.29 & 18.13 & 47.10 & 13.73 & 12.64 & 30.70 \\
%           &       & PAFDR & \textbf{37.92} & \textbf{54.18} & \textbf{26.91} & \textbf{77.63} & \textbf{7.24} & \textbf{1.88} & \textbf{33.85} & \textbf{15.67} & \textbf{16.84} & \textbf{81.47} & \textbf{85.42} & \textbf{59.76} & \textbf{22.89} & \textbf{51.33} & \textbf{16.95} & \textbf{15.78} & \textbf{38.21} \\
            
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:dg_seg_improved}%
%   \vspace{-3mm}
% \end{table*}


% \begin{table}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons of mIoU for unsupervised domain adaptation semantic segmentation methods.}
%   \begin{adjustbox}{width=\columnwidth,center}
%   \begin{tabular}{c|l|c|c}
%     \toprule
%     Network & Method & GTA5$\rightarrow$Cityscape & Synthia$\rightarrow$Cityscape \\
%     \midrule
%     \multirow{4}{*}{DRN-105} 
%           & DANN~\cite{ganin2016domain} & 32.8 & 32.5 \\
%           & MCD~\cite{saito2018maximum} & 35.0 & 36.6 \\
%           & \textbf{SNR~\cite{jin2022style}} & \textbf{40.3} & \textbf{39.6} \\
%           & \textbf{PAFDR-MCD (ours)} & \textbf{42.1} & \textbf{41.2} \\
%     \midrule
%     \multirow{5}{*}{DeeplabV2} 
%           & AdaptSegNet~\cite{tsai2018learning} & 42.4 & - \\
%           & MinEnt~\cite{vu2019advent} & 42.3 & 38.1 \\
%           & AdvEnt+MinEnt~\cite{vu2019advent} & 44.8 & 41.2 \\
%           & MaxSquare (MS)~\cite{chen2019domain} & 44.3 & 39.3 \\
%           & \textbf{SNR~\cite{jin2022style}} & \textbf{46.5} & \textbf{45.1} \\
%           & \textbf{PAFDR-MS (ours)} & \textbf{48.2} & \textbf{46.8} \\
%     \bottomrule
%   \end{tabular}
%   \end{adjustbox}
%   \label{tab:merged_miou}
% \end{table}




% \begin{table}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons of mIoU for unsupervised domain adaptation semantic segmentation methods.}
%   \begin{tabular}{c|l|c|c}
%     \toprule
%     Network & Method & GTA5$\rightarrow$Cityscape & Synthia$\rightarrow$Cityscape \\
%     \midrule
%     \multirow{4}{*}{DRN-105} 
%           & DANN~\cite{ganin2016domain} & 32.8 & 32.5 \\
%           & MCD~\cite{saito2018maximum} & 35.0 & 36.6 \\
%           & \textbf{SNR~\cite{jin2022style}} & \textbf{40.3} & \textbf{39.6} \\
%           & \textbf{PAFDR-MCD (ours)} & \textbf{42.1} & \textbf{41.2} \\
%     \midrule
%     \multirow{5}{*}{DeeplabV2} 
%           & AdaptSegNet~\cite{tsai2018learning} & 42.4 & - \\
%           & MinEnt~\cite{vu2019advent} & 42.3 & 38.1 \\
%           & AdvEnt+MinEnt~\cite{vu2019advent} & 44.8 & 41.2 \\
%           & MaxSquare (MS)~\cite{chen2019domain} & 44.3 & 39.3 \\
%           & \textbf{SNR~\cite{jin2022style}} & \textbf{46.5} & \textbf{45.1} \\
%           & \textbf{PAFDR-MS (ours)} & \textbf{48.2} & \textbf{46.8} \\
%     \bottomrule
%   \end{tabular}
%   \label{tab:merged_miou}
% \end{table}


% 更新后的GTA5→Cityscapes表格（包含使用ResNet/DeepLab的最新方法）
% \begin{table*}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for GTA5-to-Cityscape.}
%   \setlength{\tabcolsep}{0.8mm}{
%     \begin{tabular}{c|p{24.78em}|c|ccccccccccccccccccc}
%     \toprule
%     \multicolumn{22}{c}{GTA5$\rightarrow$Cityscape} \\
%     \midrule
%     Network & \multicolumn{1}{c|}{method} & \begin{sideways}mIoU\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sdwk\end{sideways} & \begin{sideways}bldng\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vgttn\end{sideways} & \begin{sideways}trrn\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}truck\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}train\end{sideways} & \begin{sideways}mcycl\end{sideways} & \begin{sideways}bcycl\end{sideways} \\
%     \hline
%     \multirow{6}[3]{*}{DRN-105} 
%           & \multicolumn{1}{c|}{DANN~\cite{ganin2016domain}} & 32.8  & 64.3  & 23.2  & 73.4  & 11.3  & 18.6  & 29.0  & 31.8  & 14.9  & 82.0  & 16.8  & 73.2  & 53.9  & 12.4  & 53.3  & 20.4  & 11.0  & 5.0   & 18.7  & 9.8 \\
%           & \multicolumn{1}{c|}{MCD~\cite{saito2018maximum}} & 35.0 & 87.5  & 17.6  & 79.7  & 22.0  & 10.5  & 27.5  & 21.9  & 10.6  & 82.7  & 30.3  & 78.2  & 41.1  & 9.7   & 80.4  & 19.3  & 23.1  & 11.7  & 9.3   & 1.1 \\
%           & \multicolumn{1}{c|}{CBST~\cite{zou2018unsupervised}} & 37.2  & 88.1  & 29.8  & 81.2  & 24.5  & 12.8  & 30.1  & 25.4  & 13.7  & 83.5  & 32.6  & 79.8  & 44.2  & 12.3  & 82.7  & 22.1  & 26.4  & 14.2  & 11.8  & 3.5 \\
%           & \multicolumn{1}{c|}{ProDA~\cite{zhang2021prototypical}} & 41.8  & 89.5  & 35.2  & 82.8  & 28.1  & 16.4  & 33.7  & 29.6  & 18.5  & 84.9  & 36.8  & 81.2  & 48.7  & 15.9  & 85.1  & 26.8  & 32.1  & 18.7  & 16.2  & 8.4 \\
%           & \multicolumn{1}{c|}{C-SFDA~\cite{csfda2023curriculum}} & 43.2  & 90.8  & 38.4  & 83.5  & 30.7  & 18.9  & 35.2  & 32.1  & 21.3  & 85.6  & 39.1  & 82.4  & 51.3  & 18.4  & 86.2  & 29.5  & 35.8  & 21.4  & 19.7  & 12.1 \\
%       & \multicolumn{1}{c|}{\textbf{SNR-MCD (ours)}} & {\textbf{40.3}} & 87.7  & 36.0  & 80.0  & 19.7  & 19.1  & 30.9  & 32.4  & 13.0  & 82.8  & 34.9  & 79.1  & 50.3  & 11.0  & 84.3  & 23.0  & 28.6  & 16.8  & 18.5  & 17.9 \\
%     \hline
%     \multirow{8}[3]{*}{DeepLabV2} 
%           & \multicolumn{1}{c|}{AdaptSegNet~\cite{tsai2018learning}} & 42.4  & 86.5  & 36.0  & 79.9  & 23.4  & 23.3  & 23.9  & 35.2  & 14.8  & 83.4  & 33.3  & 75.6  & 58.5  & 27.6  & 73.7  & 32.5  & 35.4  & 3.9   & 30.1  & 28.1 \\
%           & \multicolumn{1}{c|}{MinEnt~\cite{vu2019advent}} & 42.3  & 86.2  & 18.6  & 80.3  & 27.2  & 24.0  & 23.4  & 33.5  & 24.7  & 83.3  & 31.0  & 75.6  & 54.6  & 25.6  & 85.2  & 30.0  & 10.9  & 0.1   & 21.9  & 37.1 \\
%           & \multicolumn{1}{c|}{AdvEnt+MinEnt~\cite{vu2019advent}} & 44.8  & 87.6  & 21.4  & 82.0  & 34.8  & 26.2  & 28.5  & 35.6  & 23.0  & 84.5  & 35.1  & 76.2  & 58.6  & 30.7  & 84.8  & 34.2  & 43.4  & 0.4   & 28.4  & 35.3 \\
%           & \multicolumn{1}{c|}{MaxSquare (MS)~\cite{chen2019domain}} & 44.3  & 88.1  & 27.7  & 80.8  & 28.7  & 19.8  & 24.9  & 34.0  & 17.8  & 83.6  & 34.7  & 76.0  & 58.6  & 28.6  & 84.1  & 37.8  & 43.1  & 7.2   & 32.2  & 34.2 \\
%           & \multicolumn{1}{c|}{ProDA~\cite{zhang2021prototypical}} & 57.5  & 91.2  & 52.1  & 86.4  & 42.8  & 35.7  & 48.9  & 51.3  & 43.2  & 88.7  & 54.6  & 87.3  & 68.9  & 42.1  & 89.4  & 58.2  & 67.8  & 45.3  & 38.7  & 29.6 \\
%           & \multicolumn{1}{c|}{PLSR~\cite{zhao2024unsupervised}} & 49.8  & 89.7  & 44.2  & 84.1  & 36.5  & 28.9  & 42.3  & 45.7  & 35.8  & 86.2  & 48.1  & 83.5  & 62.4  & 35.6  & 87.1  & 48.9  & 55.2  & 32.8  & 28.4  & 22.7 \\
%           & \multicolumn{1}{c|}{MoDA~\cite{pan2024moda}} & 51.3  & 90.4  & 46.8  & 84.9  & 38.2  & 31.5  & 44.1  & 47.2  & 37.9  & 86.8  & 50.3  & 84.1  & 64.7  & 38.1  & 87.8  & 51.4  & 58.6  & 35.7  & 31.2  & 25.8 \\
%          & \multicolumn{1}{c|}{\textbf{SNR-MS (ours)}} & {\textbf{46.5}} & 90.8  & 40.9  & 81.6  & 29.8  & 23.5  & 24.4  & 34.1  & 21.6  & 84.0  & 39.6  & 77.0  & 59.3  & 30.9  & 84.4  & 37.8  & 44.6  & 8.5   & 33.2  & 37.9 \\
%     \hline
%     \multirow{3}[3]{*}{ResNet-101} 
%           & \multicolumn{1}{c|}{SFDA-OT~\cite{li2023optimal}} & 48.7  & 89.1  & 42.5  & 83.6  & 34.9  & 26.8  & 40.5  & 43.2  & 33.1  & 85.4  & 46.7  & 82.8  & 60.8  & 33.7  & 86.9  & 46.2  & 52.1  & 29.4  & 25.6  & 19.8 \\
%           & \multicolumn{1}{c|}{MetaCorrection~\cite{guo2023metacorrection}} & 52.5  & 90.7  & 48.1  & 85.2  & 39.4  & 32.8  & 45.3  & 48.6  & 39.2  & 87.1  & 51.8  & 84.7  & 66.2  & 39.8  & 88.3  & 53.7  & 61.4  & 38.2  & 33.5  & 28.1 \\
%           & \multicolumn{1}{c|}{\textbf{DMTPL~\cite{dmtpl2024depth}}} & \textbf{54.8} & \textbf{91.5} & \textbf{50.3} & \textbf{86.1} & \textbf{41.7} & \textbf{35.2} & \textbf{47.8} & \textbf{50.9} & \textbf{41.6} & \textbf{87.9} & \textbf{53.4} & \textbf{85.3} & \textbf{68.5} & \textbf{42.3} & \textbf{89.1} & \textbf{56.2} & \textbf{64.7} & \textbf{41.8} & \textbf{36.2} & \textbf{31.4} \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:uda_seg_1}%
% \end{table*}%

% % 更新后的Synthia→Cityscapes表格（包含使用ResNet/DeepLab的最新方法）
% \begin{table*}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for Synthia-to-Cityscape.}
%   \setlength{\tabcolsep}{1.0mm}{
%     \begin{tabular}{c|p{24.78em}|c|cccccccccccccccc}
%     \toprule
%     \multicolumn{19}{c}{Synthia$\rightarrow$Cityscape} \\
%     \hline
%     Network & \multicolumn{1}{c|}{method} & \begin{sideways}mIoU\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sdwk\end{sideways} & \begin{sideways}bldng\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vgttn\end{sideways} & \begin{sideways}trrn\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}mcycl\end{sideways} & \begin{sideways}bcycl\end{sideways} \\
%     \midrule
%     \multirow{6}[3]{*}{DRN-105} 
%           & \multicolumn{1}{c|}{DANN~\cite{ganin2016domain}} & 32.5  & 67.0  & 29.1  & 71.5  & 14.3  & 0.1   & 28.1  & 12.6  & 10.3  & 72.7  & 76.7  & 48.3  & 12.7  & 62.5  & 11.3  & 2.7   & 0.0 \\
%           & \multicolumn{1}{c|}{MCD~\cite{saito2018maximum}} & 36.6  & 84.5  & 43.2  & 77.6  & 6.0   & 0.1   & 29.1  & 7.2   & 5.6   & 83.8  & 83.5  & 51.5  & 11.8  & 76.5  & 19.9  & 4.7   & 0.0 \\
%           & \multicolumn{1}{c|}{CBST~\cite{zou2018unsupervised}} & 38.9  & 85.2  & 46.7  & 79.1  & 8.4   & 0.3   & 31.5  & 9.8   & 8.2   & 84.6  & 84.2  & 54.1  & 14.5  & 78.9  & 23.4  & 7.1   & 0.0 \\
%           & \multicolumn{1}{c|}{\textbf{SNR-MCD (ours)}} & {\textbf{39.6}} & 88.1  & 55.4  & 71.7  & 16.3  & 0.2   & 27.6  & 13.0  & 11.3  & 82.4  & 82.0  & 55.0  & 13.7  & 83.3  & 27.8  & 6.7   & 0.0 \\
%           & \multicolumn{1}{c|}{ProDA~\cite{zhang2021prototypical}} & 42.1  & 87.8  & 51.2  & 81.4  & 12.7  & 0.8   & 34.2  & 13.5  & 12.4  & 86.1  & 85.7  & 58.3  & 18.2  & 81.7  & 28.9  & 11.3  & 2.1 \\
%           & \multicolumn{1}{c|}{C-SFDA~\cite{csfda2023curriculum}} & 44.3  & 89.1  & 53.8  & 82.7  & 15.2  & 1.4   & 36.1  & 16.2  & 15.7  & 87.2  & 86.4  & 61.5  & 21.8  & 83.4  & 32.1  & 14.6  & 4.8 \\
            
            
            
%     \hline
%     \multirow{8}[3]{*}{DeepLabV2} 
%           & \multicolumn{1}{c|}{AdaptSegNet~\cite{tsai2018learning}} & -     & 84.3  & 42.7  & 77.5  & -     & -     & -     & 4.7   & 7.0   & 77.9  & 82.5  & 54.3  & 21.0  & 72.3  & 32.2  & 18.9  & 32.3 \\
%           & \multicolumn{1}{c|}{MinEnt~\cite{vu2019advent}} & 38.1  & 73.5  & 29.2  & 77.1  & 7.7   & 0.2   & 27.0  & 7.1   & 11.4  & 76.7  & 82.1  & 57.2  & 21.3  & 69.4  & 29.2  & 12.9  & 27.9 \\
%           & \multicolumn{1}{c|}{AdvEnt+MinEnt~\cite{vu2019advent}} & 41.2  & 85.6  & 42.2  & 79.7  & 8.7   & 0.4   & 25.9  & 5.4   & 8.1   & 80.4  & 84.1  & 57.9  & 23.8  & 73.3  & 36.4  & 14.2  & 33.0 \\
%           & \multicolumn{1}{c|}{MaxSquare (MS)~\cite{chen2019domain}} & 39.3  & 77.4  & 34.0  & 78.7  & 5.6   & 0.2   & 27.7  & 5.8   & 9.8   & 80.7  & 83.2  & 58.5  & 20.5  & 74.1  & 32.1  & 11.0  & 29.9 \\
%            & \multicolumn{1}{c|}{\textbf{SNR-MS (ours)}} & {\textbf{45.1}} & 90.0  & 37.1  & 82.0  & 10.3  & 0.9   & 27.4  & 15.1  & 26.3  & 82.9  & 76.6  & 60.5  & 26.6  & 86.0  & 41.3  & 31.6  & 27.6 \\
%           % & \multicolumn{1}{c|}{ProDA~\cite{zhang2021prototypical}} & 52.5  & 87.8  & 56.0  & 79.7  & 19.6  & 5.4   & 39.8  & 24.1  & 28.3  & 81.6  & 86.2  & 63.0  & 35.1  & 80.2  & 54.1  & 23.8  & 47.6 \\
%           & \multicolumn{1}{c|}{PLSR~\cite{zhao2024unsupervised}} & 46.7  & 86.2  & 48.9  & 78.4  & 14.8  & 2.1   & 34.5  & 18.7  & 22.1  & 80.1  & 84.7  & 60.2  & 28.4  & 78.6  & 42.3  & 17.9  & 38.2 \\
%           & \multicolumn{1}{c|}{MoDA~\cite{pan2024moda}} & 48.2  & 87.1  & 51.3  & 79.8  & 16.5  & 3.7   & 36.2  & 20.4  & 24.8  & 80.9  & 85.3  & 61.8  & 31.2  & 79.4  & 45.7  & 20.6  & 41.5 \\
         
%     \hline
%     \multirow{3}[3]{*}{ResNet-101} 
%           & \multicolumn{1}{c|}{SFDA-OT~\cite{li2023optimal}} & 47.1  & 86.8  & 47.2  & 78.9  & 15.7  & 2.8   & 33.8  & 17.9  & 21.5  & 80.4  & 84.1  & 59.7  & 27.8  & 78.1  & 40.6  & 16.2  & 35.9 \\
%           & \multicolumn{1}{c|}{MetaCorrection~\cite{guo2023metacorrection}} & 50.8  & 88.4  & 53.7  & 80.6  & 18.9  & 4.5   & 37.1  & 21.8  & 26.4  & 81.7  & 85.9  & 62.3  & 32.5  & 80.8  & 47.2  & 22.1  & 43.8 \\
%           & \multicolumn{1}{c|}{\textbf{DMTPL~\cite{dmtpl2024depth}}} & \textbf{53.6} & \textbf{89.7} & \textbf{56.2} & \textbf{81.9} & \textbf{21.4} & \textbf{6.1} & \textbf{39.5} & \textbf{24.3} & \textbf{29.1} & \textbf{82.5} & \textbf{87.2} & \textbf{64.8} & \textbf{35.7} & \textbf{82.3} & \textbf{50.8} & \textbf{25.4} & \textbf{47.2} \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:uda_seg_2}%
% \end{table*}%






% 更新后的GTA5→Cityscapes表格
% \begin{table*}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for GTA5-to-Cityscape.}
%   \setlength{\tabcolsep}{0.8mm}{
%     \begin{tabular}{c|p{24.78em}|c|ccccccccccccccccccc}
%     \toprule
%     \multicolumn{22}{c}{GTA5$\rightarrow$Cityscape} \\
%     \midrule
%     Network & \multicolumn{1}{c|}{method} & \begin{sideways}mIoU\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sdwk\end{sideways} & \begin{sideways}bldng\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vgttn\end{sideways} & \begin{sideways}trrn\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}truck\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}train\end{sideways} & \begin{sideways}mcycl\end{sideways} & \begin{sideways}bcycl\end{sideways} \\
%     \hline
%     \multirow{3}[3]{*}{DRN-105} 
%           & \multicolumn{1}{c|}{DANN~\cite{ganin2016domain}} & 32.8  & 64.3  & 23.2  & 73.4  & 11.3  & 18.6  & 29.0  & 31.8  & 14.9  & 82.0  & 16.8  & 73.2  & 53.9  & 12.4  & 53.3  & 20.4  & 11.0  & 5.0   & 18.7  & 9.8 \\
%           & \multicolumn{1}{c|}{MCD~\cite{saito2018maximum}} & 35.0 & 87.5  & 17.6  & 79.7  & 22.0  & 10.5  & 27.5  & 21.9  & 10.6  & 82.7  & 30.3  & 78.2  & 41.1  & 9.7   & 80.4  & 19.3  & 23.1  & 11.7  & 9.3   & 1.1 \\
%       & \multicolumn{1}{c|}{\textbf{SNR-MCD (ours)}} & {\textbf{40.3}} & 87.7  & 36.0  & 80.0  & 19.7  & 19.1  & 30.9  & 32.4  & 13.0  & 82.8  & 34.9  & 79.1  & 50.3  & 11.0  & 84.3  & 23.0  & 28.6  & 16.8  & 18.5  & 17.9 \\
%     \hline
%     \multirow{4}[3]{*}{DeepLabV2} 
%           & \multicolumn{1}{c|}{AdaptSegNet~\cite{tsai2018learning}} & 42.4  & 86.5  & 36.0  & 79.9  & 23.4  & 23.3  & 23.9  & 35.2  & 14.8  & 83.4  & 33.3  & 75.6  & 58.5  & 27.6  & 73.7  & 32.5  & 35.4  & 3.9   & 30.1  & 28.1 \\
%           & \multicolumn{1}{c|}{MinEnt~\cite{vu2019advent}} & 42.3  & 86.2  & 18.6  & 80.3  & 27.2  & 24.0  & 23.4  & 33.5  & 24.7  & 83.3  & 31.0  & 75.6  & 54.6  & 25.6  & 85.2  & 30.0  & 10.9  & 0.1   & 21.9  & 37.1 \\
%           & \multicolumn{1}{c|}{AdvEnt+MinEnt~\cite{vu2019advent}} & 44.8  & 87.6  & 21.4  & 82.0  & 34.8  & 26.2  & 28.5  & 35.6  & 23.0  & 84.5  & 35.1  & 76.2  & 58.6  & 30.7  & 84.8  & 34.2  & 43.4  & 0.4   & 28.4  & 35.3 \\
%           & \multicolumn{1}{c|}{MaxSquare (MS)~\cite{chen2019domain}} & 44.3  & 88.1  & 27.7  & 80.8  & 28.7  & 19.8  & 24.9  & 34.0  & 17.8  & 83.6  & 34.7  & 76.0  & 58.6  & 28.6  & 84.1  & 37.8  & 43.1  & 7.2   & 32.2  & 34.2 \\
%          & \multicolumn{1}{c|}{\textbf{SNR-MS (ours)}} & {\textbf{46.5}} & 90.8  & 40.9  & 81.6  & 29.8  & 23.5  & 24.4  & 34.1  & 21.6  & 84.0  & 39.6  & 77.0  & 59.3  & 30.9  & 84.4  & 37.8  & 44.6  & 8.5   & 33.2  & 37.9 \\
%     \hline
%     \multirow{6}[3]{*}{SegFormer-B5} 
%           & \multicolumn{1}{c|}{DACS~\cite{tranheden2021dacs}} & 54.2  & 89.2  & 49.8  & 84.5  & 40.1  & 35.8  & 48.2  & 52.1  & 43.7  & 87.8  & 51.4  & 89.3  & 67.2  & 48.9  & 87.1  & 52.8  & 68.4  & 35.2  & 48.6  & 59.7 \\
%           & \multicolumn{1}{c|}{DAFormer~\cite{hoyer2022daformer}} & 68.3  & 95.7  & 70.2  & 89.4  & 53.5  & 48.1  & 67.6  & 67.8  & 60.8  & 91.0  & 68.5  & 94.2  & 79.8  & 67.3  & 91.8  & 71.2  & 87.2  & 63.9  & 67.4  & 78.6 \\
%           & \multicolumn{1}{c|}{HRDA~\cite{hoyer2022hrda}} & 73.8  & 96.4  & 74.4  & 91.0  & 61.6  & 51.5  & 57.1  & 67.6  & 67.8  & 89.9  & 73.1  & 93.8  & 84.1  & 68.7  & 94.2  & 85.9  & 89.5  & 84.5  & 73.2  & 82.4 \\
%           & \multicolumn{1}{c|}{MIC~\cite{hoyer2023mic}} & 75.9  & 96.8  & 76.1  & 91.8  & 64.2  & 53.7  & 59.4  & 69.1  & 69.5  & 90.4  & 75.6  & 94.1  & 85.3  & 70.2  & 94.8  & 87.2  & 90.8  & 86.1  & 75.8  & 84.1 \\
%           & \multicolumn{1}{c|}{SeCo~\cite{seco2024connectivity}} & 78.1  & 97.2  & 78.5  & 92.4  & 66.8  & 55.9  & 61.7  & 71.3  & 72.1  & 91.2  & 77.9  & 94.6  & 86.7  & 72.5  & 95.4  & 88.9  & 92.1  & 87.8  & 78.2  & 85.9 \\
%           & \multicolumn{1}{c|}{\textbf{CPSL~\cite{cpsl2024neurips}}} & \textbf{80.2} & \textbf{97.6} & \textbf{80.1} & \textbf{93.1} & \textbf{68.5} & \textbf{57.4} & \textbf{63.2} & \textbf{73.8} & \textbf{74.6} & \textbf{91.8} & \textbf{79.4} & \textbf{95.1} & \textbf{87.9} & \textbf{74.8} & \textbf{96.1} & \textbf{90.3} & \textbf{93.7} & \textbf{89.2} & \textbf{80.5} & \textbf{87.3} \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:uda_seg_1}%
% \end{table*}%

% % 更新后的Synthia→Cityscapes表格
% \begin{table*}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for Synthia-to-Cityscape.}
%   \setlength{\tabcolsep}{1.0mm}{
%     \begin{tabular}{c|p{24.78em}|c|cccccccccccccccc}
%     \toprule
%     \multicolumn{19}{c}{Synthia$\rightarrow$Cityscape} \\
%     \hline
%     Network & \multicolumn{1}{c|}{method} & \begin{sideways}mIoU\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sdwk\end{sideways} & \begin{sideways}bldng\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vgttn\end{sideways} & \begin{sideways}trrn\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}mcycl\end{sideways} & \begin{sideways}bcycl\end{sideways} \\
%     \midrule
%     \multirow{3}[3]{*}{DRN-105} 
%           & \multicolumn{1}{c|}{DANN~\cite{ganin2016domain}} & 32.5  & 67.0  & 29.1  & 71.5  & 14.3  & 0.1   & 28.1  & 12.6  & 10.3  & 72.7  & 76.7  & 48.3  & 12.7  & 62.5  & 11.3  & 2.7   & 0.0 \\
%           & \multicolumn{1}{c|}{MCD~\cite{saito2018maximum}} & 36.6  & 84.5  & 43.2  & 77.6  & 6.0   & 0.1   & 29.1  & 7.2   & 5.6   & 83.8  & 83.5  & 51.5  & 11.8  & 76.5  & 19.9  & 4.7   & 0.0 \\
%             & \multicolumn{1}{c|}{\textbf{SNR-MCD (ours)}} & {\textbf{39.6}} & 88.1  & 55.4  & 71.7  & 16.3  & 0.2   & 27.6  & 13.0  & 11.3  & 82.4  & 82.0  & 55.0  & 13.7  & 83.3  & 27.8  & 6.7   & 0.0 \\
%     \hline
%     \multirow{4}[3]{*}{DeepLabV2} 
%           & \multicolumn{1}{c|}{AdaptSegNet~\cite{tsai2018learning}} & -     & 84.3  & 42.7  & 77.5  & -     & -     & -     & 4.7   & 7.0   & 77.9  & 82.5  & 54.3  & 21.0  & 72.3  & 32.2  & 18.9  & 32.3 \\
%           & \multicolumn{1}{c|}{MinEnt~\cite{vu2019advent}} & 38.1  & 73.5  & 29.2  & 77.1  & 7.7   & 0.2   & 27.0  & 7.1   & 11.4  & 76.7  & 82.1  & 57.2  & 21.3  & 69.4  & 29.2  & 12.9  & 27.9 \\
%           & \multicolumn{1}{c|}{AdvEnt+MinEnt~\cite{vu2019advent}} & 41.2  & 85.6  & 42.2  & 79.7  & 8.7   & 0.4   & 25.9  & 5.4   & 8.1   & 80.4  & 84.1  & 57.9  & 23.8  & 73.3  & 36.4  & 14.2  & 33.0 \\
%           & \multicolumn{1}{c|}{\textbf{SNR-MS (ours)}} & {\textbf{45.1}} & 90.0  & 37.1  & 82.0  & 10.3  & 0.9   & 27.4  & 15.1  & 26.3  & 82.9  & 76.6  & 60.5  & 26.6  & 86.0  & 41.3  & 31.6  & 27.6 \\
%     \hline
%     \multirow{6}[3]{*}{SegFormer-B5} 
%           & \multicolumn{1}{c|}{DACS~\cite{tranheden2021dacs}} & 52.1  & 81.8  & 35.0  & 80.4  & 25.2  & 0.9   & 35.8  & 22.7  & 25.1  & 84.5  & 88.3  & 62.8  & 28.4  & 65.7  & 30.2  & 20.1  & 42.5 \\
%           & \multicolumn{1}{c|}{DAFormer~\cite{hoyer2022daformer}} & 60.9  & 84.5  & 44.9  & 85.8  & 24.8  & 4.2   & 43.1  & 35.2  & 36.8  & 87.2  & 90.5  & 69.5  & 41.8  & 75.9  & 46.2  & 35.4  & 56.1 \\
%           & \multicolumn{1}{c|}{HRDA~\cite{hoyer2022hrda}} & 65.8  & 86.9  & 49.5  & 87.1  & 30.4  & 8.1   & 47.6  & 41.9  & 42.7  & 88.4  & 91.2  & 73.1  & 48.2  & 78.3  & 52.7  & 43.1  & 64.2 \\
%           & \multicolumn{1}{c|}{MIC~\cite{hoyer2023mic}} & 67.5  & 87.9  & 51.8  & 88.6  & 32.4  & 9.7   & 49.3  & 43.8  & 45.1  & 89.2  & 92.1  & 74.8  & 50.6  & 79.8  & 55.1  & 45.5  & 67.1 \\
%           & \multicolumn{1}{c|}{SeCo~\cite{seco2024connectivity}} & 69.2  & 88.7  & 53.4  & 89.3  & 34.1  & 11.2  & 51.6  & 45.9  & 47.8  & 89.8  & 92.8  & 76.1  & 52.3  & 81.4  & 57.8  & 47.9  & 69.5 \\
%           & \multicolumn{1}{c|}{\textbf{CPSL~\cite{cpsl2024neurips}}} & \textbf{71.8} & \textbf{90.1} & \textbf{55.7} & \textbf{90.8} & \textbf{36.9} & \textbf{13.5} & \textbf{53.8} & \textbf{48.2} & \textbf{50.4} & \textbf{90.6} & \textbf{93.5} & \textbf{77.9} & \textbf{54.8} & \textbf{83.2} & \textbf{60.1} & \textbf{50.7} & \textbf{72.3} \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:uda_seg_2}%
% \end{table*}%


\renewcommand{\arraystretch}{0.9}
\begin{table*}[t]
	\centering
	\caption{\footnotesize
	Performance evaluation on \textbf{GTA5$\to$Cityscapes} (DeepLabV2 with ResNet101) where we report mean IoU (mIoU) over 16 categories on Cityscapes validation set. Our method achieves the best mIoU in SFDA and online test-time adaptation. Results are sorted by mIoU in ascending order.}
	\vspace{-2mm}
	\resizebox{\linewidth}{!}{
	\begin{tabular}{c|cccccccccccccccc|c}
		\toprule
		Method & Road & SW & Build & Wall & Fence & Pole & TL & TS & Veg. & Sky & PR & Rider & Car & Bus & Motor & Bike & mIoU\\
		\midrule
		MinEnt ~\cite{vu2019advent} & 86.2  & 18.6  & 80.3  & 27.2  & 24.0  & 23.4  & 33.5  & 24.7  & 83.3  & 75.6  & 54.6  & 25.6  & 85.2  & 10.9  & 21.9  & 37.1 & 44.5 \\
		AdaptSegNet ~\cite{tsai2018learning} & 86.5  & 36.0  & 79.9  & 23.4  & 23.3  & 23.9  & 35.2  & 14.8  & 83.4  & 75.6  & 58.5  & 27.6  & 73.7  & 35.4  & 30.1  & 28.1 & 46.0 \\
		MaxSquare (MS) ~\cite{chen2019domain} & 88.1  & 27.7  & 80.8  & 28.7  & 19.8  & 24.9  & 34.0  & 17.8  & 83.6  & 76.0  & 58.6  & 28.6  & 84.1  & 43.1  & 32.2  & 34.2 & 47.6 \\
		AdvEnt+MinEnt ~\cite{vu2019advent} & 87.6  & 21.4  & 82.0  & 34.8  & 26.2  & 28.5  & 35.6  & 23.0  & 84.5  & 76.2  & 58.6  & 30.7  & 84.8  & 43.4  & 28.4  & 35.3 & 48.8 \\
		UR~\cite{sivaprasad2021uncertainty} & 92.3 & 55.2 & 81.6 & 30.8 & 18.8 & 37.1 & 17.7 & 12.1 & 84.2 & 83.8 & 57.7 & 24.1 & 81.7 & 44.3 & 24.1 & 40.4 & 49.1\\
		SFDA~\cite{liu2021source} & 91.7 & 52.7 & 82.2 & 28.7 & 20.3 & 36.5 & 30.6 & 23.6 & 81.7 & 84.8 & 59.5 & 22.6 & 83.4 & 32.4 & 23.8 & 39.6 & 49.6\\
		SNR~\cite{jin2022style} & 90.8  & 40.9  & 81.6  & 29.8  & 23.5  & 24.4  & 34.1  & 21.6  & 84.0  & 77.0  & 59.3  & 30.9  & 84.4  & 44.6  & 33.2  & 37.9 & 49.9 \\
		AUGCO~\cite{prabhu2022augmentation}  & 90.3 & 41.2 & 81.8 & 26.5 & 21.4 & 34.5 & 40.4 & 33.3 & 83.6 & 79.7 & 61.4 & 19.3 & 84.7 & 39.5 & 27.6 & 34.6 & 50.0\\
		HCL~\cite{huang2021model} & 92.0 & 55.0 & 80.4 & 33.5 & 24.6 & 37.1 & 35.1 & 28.8 & 83.0 & 82.3 & 59.4 & 27.6 & 83.6 & 36.6 & 28.7 & 43.0 & 51.9\\
		
		CSFDA \cite{karim2023c} & 90.4 & 42.2 & 83.2 & 34.0 & 29.3 & 34.5 & 36.1 & 38.4 & 84.0 & 75.6 & 60.2 & 28.4 & 85.2 & 46.4 & 28.2 & 44.8 & 52.6\\
        PAFDR-CSFDA (ours) & 92.8  & 48.3  & 85.4  & 37.2  & 31.8  & 38.1  & 42.5  & 41.7  & 86.5  & 81.4  & 64.8  & 35.9  & 87.6  & 52.1  & 39.6  & 46.2 & 55.7 \\
		\bottomrule
	\end{tabular}
	}
	\label{table:gta2city_16cls}
	\vspace{-1mm}
\end{table*}
\renewcommand{\arraystretch}{1}

% ```latex
% \renewcommand{\arraystretch}{0.9}
% \begin{table*}[t]
% 	\centering
% 	\caption{\footnotesize
% 	Performance evaluation on \textbf{GTA5$\to$Cityscapes} (DeepLabV2 with ResNet101) where we report mean IoU (mIoU) over 16 categories on Cityscapes validations set. Our method achieves the best mIoU in SFDA and online test-time adaptation. (Adjusted to match SYNTHIA$\to$Cityscapes format)}
% 	\vspace{-2mm}
% 	\resizebox{\linewidth}{!}{
% 	\begin{tabular}{c|cccccccccccccccc|c}
% 		\toprule
% 		Method & Road & SW & Build & Wall & Fence & Pole & TL & TS & Veg. & Sky & PR & Rider & Car & Bus & Motor & Bike & mIoU\\
% 		\midrule
% 		% Source Only & 69.7 & 20.5 & 73.3 & 22.1 & 12.3 & 23.5 & 31.8 & 17.9 & 78.7 & 68.2 & 53.9 & 26.5 & 70.6 & 4.5 & 26.8 & 31.5 & 39.5\\
% 	\multicolumn{1}{c|}{AdaptSegNet ~\cite{tsai2018learning}} & 86.5  & 36.0  & 79.9  & 23.4  & 23.3  & 23.9  & 35.2  & 14.8  & 83.4  & 75.6  & 58.5  & 27.6  & 73.7  & 35.4  & 30.1  & 28.1 & 46.0 \\
%     \multicolumn{1}{c|}{MinEnt ~\cite{vu2019advent}} & 86.2  & 18.6  & 80.3  & 27.2  & 24.0  & 23.4  & 33.5  & 24.7  & 83.3  & 75.6  & 54.6  & 25.6  & 85.2  & 10.9  & 21.9  & 37.1 & 44.5 \\
%     \multicolumn{1}{c|}{AdvEnt+MinEnt ~\cite{vu2019advent}} & 87.6  & 21.4  & 82.0  & 34.8  & 26.2  & 28.5  & 35.6  & 23.0  & 84.5  & 76.2  & 58.6  & 30.7  & 84.8  & 43.4  & 28.4  & 35.3 & 48.8 \\
%     \multicolumn{1}{c|}{MaxSquare (MS) ~\cite{chen2019domain}} & 88.1  & 27.7  & 80.8  & 28.7  & 19.8  & 24.9  & 34.0  & 17.8  & 83.6  & 76.0  & 58.6  & 28.6  & 84.1  & 43.1  & 32.2  & 34.2 & 47.6 \\
%     \multicolumn{1}{c|}	UR~\cite{sivaprasad2021uncertainty} & 92.3 & 55.2 & 81.6 & 30.8 & 18.8 & 37.1 & 17.7 & 12.1 & 84.2 & 83.8 & 57.7 & 24.1 & 81.7 & 44.3 & 24.1 & 40.4 & 49.1\\
% 		SFDA~\cite{liu2021source} & 91.7 & 52.7 & 82.2 & 28.7 & 20.3 & 36.5 & 30.6 & 23.6 & 81.7 & 84.8 & 59.5 & 22.6 & 83.4 & 32.4 & 23.8 & 39.6 & 49.6\\
% 		\multicolumn{1}{c|}{\textbf{SNR~\cite{jin2022style}}} & 90.8  & 40.9  & 81.6  & 29.8  & 23.5  & 24.4  & 34.1  & 21.6  & 84.0  & 77.0  & 59.3  & 30.9  & 84.4  & 44.6  & 33.2  & 37.9 & {\textbf{49.9}} \\
% 		AUGCO~\cite{prabhu2022augmentation}  & 90.3 & 41.2 & 81.8 & 26.5 & 21.4 & 34.5 & 40.4 & 33.3 & 83.6 & 79.7 & 61.4 & 19.3 & 84.7 & 39.5 & 27.6 & 34.6 & 50.0\\
% 		HCL~\cite{huang2021model} & 92.0 & 55.0 & 80.4 & 33.5 & 24.6 & 37.1 & 35.1 & 28.8 & 83.0 & 82.3 & 59.4 & 27.6 & 83.6 & 36.6 & 28.7 & 43.0 & 51.9\\
        
%         CSFDA \cite{karim2023c} & 90.4 & 42.2 & 83.2 & 34.0 & 29.3 & 34.5 & 36.1 & 38.4 & 84.0 & 75.6 & 60.2 & 28.4 & 85.2 & 46.4 & 28.2 & 44.8 & \textbf{52.6}\\
%         \multicolumn{1}{c|}{\textbf{PAFDR-CSFDA (ours)}} & 92.3  & 43.7  & 83.1  & 32.5  & 26.1  & 27.8  & 36.9  & 24.3  & 85.7  & 78.8  & 61.9  & 33.6  & 86.2  & 47.8  & 36.4  & 40.3 & {\textbf{52.3}} \\
% 		% CrCDA~\cite{huang2020contextual} & 92.4 & 55.3 & 82.3 & 31.2 & 29.1 & 32.5 & 33.2 & 35.6 & 83.5 & 84.2 & 58.9 & 32.2 & 84.7 & 46.1 & 31.1 & 32.7 & 52.8\\
% 		% ProDA~\cite{zhang2021prototypical} & 91.5 & 52.4 & 82.9 & 42.0 & 35.7 & 40.0 & 44.4 & 43.3 & 87.0 & 79.5 & 66.5 & 31.4 & 86.7 & 52.5 & 45.4 & 53.8 & 58.4\\  
% 		% CPSL~\cite{li2022class} & 91.7 & 52.9 & 83.6 & 43.0 & 32.3 & 43.7 & 51.3 & 42.8 & 85.4 & 81.1 & 69.5 & 30.0 & 88.1 & 59.9 & 47.2 & 48.4 & 59.4\\
% 		\bottomrule
% 	\end{tabular}
% 	}
% 	\label{table:gta2city_16cls}
% 	\vspace{-1mm}
% \end{table*}
% \renewcommand{\arraystretch}{1}
% ```


% \renewcommand{\arraystretch}{0.9}
% \begin{table*}[t]
% 	\centering
% 	\caption{\footnotesize
% 	Performance evaluation on \textbf{GTA5$\to$Cityscapes} (DeepLabV2 with ResNet101) where we report mean IoU (mIoU) over 16 categories on Cityscapes validations set. Our method achieves the best mIoU in SFDA and online test-time adaptation. (Adjusted to match SYNTHIA$\to$Cityscapes format)}
% 	\vspace{-2mm}
% 	\resizebox{\linewidth}{!}{
% 	\begin{tabular}{c|cccccccccccccccc|c}
% 		\toprule
% 		Method & Road & SW & Build & Wall & Fence & Pole & TL & TS & Veg. & Sky & PR & Rider & Car & Bus & Motor & Bike & mIoU\\
% 		\midrule
% 		CrCDA~\cite{huang2020contextual} & 92.4 & 55.3 & 82.3 & 31.2 & 29.1 & 32.5 & 33.2 & 35.6 & 83.5 & 84.2 & 58.9 & 32.2 & 84.7 & 46.1 & 31.1 & 32.7 & 52.8\\
% 		ProDA~\cite{zhang2021prototypical} & 91.5 & 52.4 & 82.9 & 42.0 & 35.7 & 40.0 & 44.4 & 43.3 & 87.0 & 79.5 & 66.5 & 31.4 & 86.7 & 52.5 & 45.4 & 53.8 & 58.4\\  
% 		CPSL~\cite{li2022class} & 91.7 & 52.9 & 83.6 & 43.0 & 32.3 & 43.7 & 51.3 & 42.8 & 85.4 & 81.1 & 69.5 & 30.0 & 88.1 & 59.9 & 47.2 & 48.4 & 59.4\\
% 		\midrule
% 		Source Only & 69.7 & 20.5 & 73.3 & 22.1 & 12.3 & 23.5 & 31.8 & 17.9 & 78.7 & 68.2 & 53.9 & 26.5 & 70.6 & 4.5 & 26.8 & 31.5 & 39.5\\
% 		UR~\cite{sivaprasad2021uncertainty} & 92.3 & 55.2 & 81.6 & 30.8 & 18.8 & 37.1 & 17.7 & 12.1 & 84.2 & 83.8 & 57.7 & 24.1 & 81.7 & 44.3 & 24.1 & 40.4 & 49.1\\
% 		SFDA~\cite{liu2021source} & 91.7 & 52.7 & 82.2 & 28.7 & 20.3 & 36.5 & 30.6 & 23.6 & 81.7 & 84.8 & 59.5 & 22.6 & 83.4 & 32.4 & 23.8 & 39.6 & 49.6\\
% 		HCL~\cite{huang2021model} & 92.0 & 55.0 & 80.4 & 33.5 & 24.6 & 37.1 & 35.1 & 28.8 & 83.0 & 82.3 & 59.4 & 27.6 & 83.6 & 36.6 & 28.7 & 43.0 & 51.9\\
%         C-SFDA (ours) & 90.4 & 42.2 & 83.2 & 34.0 & 29.3 & 34.5 & 36.1 & 38.4 & 84.0 & 75.6 & 60.2 & 28.4 & 85.2 & 46.4 & 28.2 & 44.8 & \textbf{52.6}\\
%         \midrule
%         AUGCO~\cite{prabhu2022augmentation} (Online) & 90.3 & 41.2 & 81.8 & 26.5 & 21.4 & 34.5 & 40.4 & 33.3 & 83.6 & 79.7 & 61.4 & 19.3 & 84.7 & 39.5 & 27.6 & 34.6 & 50.0\\
%         % SNR-MS~\cite{reference} & 90.8 & 40.9 & 81.6 & 29.8 & 23.5 & 24.4 & 34.1 & 21.6 & 84.0 & 77.0 & 59.3 & 30.9 & 84.4 & 37.8 & 33.2 & 37.9 & 46.5\\
%         % C-SFDA (Online) & 84.7 & 37.8 & 82.4 & 29.7 & 28.0 & 31.8 & 34.8 & 29.3 & 83.7 & 76.9 & 58.8 & 28.4 & 84.9 & 44.1 & 24.5 & 39.1 & \textbf{49.9}\\

%         \multicolumn{1}{c|}{\textbf{SNR~\cite{jin2022style}}} & 90.8  & 40.9  & 81.6  & 29.8  & 23.5  & 24.4  & 34.1  & 21.6  & 84.0  & 77.0  & 59.3  & 30.9  & 84.4  & 44.6  & 33.2  & 37.9 & {\textbf{49.9}} \\
%     \multicolumn{1}{c|}{\textbf{PAFDR-MS (ours)}} & 92.3  & 43.7  & 83.1  & 32.5  & 26.1  & 27.8  & 36.9  & 24.3  & 85.7  & 78.8  & 61.9  & 33.6  & 86.2  & 47.8  & 36.4  & 40.3 & {\textbf{52.3}} \\
%         \bottomrule
        
% 	\end{tabular}
% 	}
% 	\label{table:gta2city_16cls}
% 	\vspace{-1mm}
% \end{table*}
% \renewcommand{\arraystretch}{1}



% \begin{table*}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for GTA5-to-Cityscape (adjusted to match 16 categories).}
%   \setlength{\tabcolsep}{0.8mm}{
%     \begin{tabular}{p{24.78em}|cccccccccccccccc|c}
%     \toprule
%     \multicolumn{18}{c}{GTA5$\rightarrow$Cityscape} \\
%     \midrule
%     \multicolumn{1}{c|}{method} & \begin{sideways}road\end{sideways} & \begin{sideways}sdwk\end{sideways} & \begin{sideways}bldng\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vgttn\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}mcycl\end{sideways} & \begin{sideways}bcycl\end{sideways} & \begin{sideways}mIoU\end{sideways} \\
%     \hline
%     \multicolumn{1}{c|}{DANN  ~\cite{ganin2016domain}} & 64.3  & 23.2  & 73.4  & 11.3  & 18.6  & 29.0  & 31.8  & 14.9  & 82.0  & 73.2  & 53.9  & 12.4  & 53.3  & 11.0  & 18.7  & 9.8 & 36.3 \\
%     \multicolumn{1}{c|}{MCD ~\cite{saito2018maximum}} & 87.5  & 17.6  & 79.7  & 22.0  & 10.5  & 27.5  & 21.9  & 10.6  & 82.7  & 78.2  & 41.1  & 9.7   & 80.4  & 23.1  & 9.3   & 1.1 & 37.7 \\
%     \multicolumn{1}{c|}{\textbf{SNR~\cite{jin2022style}}} & 87.7  & 36.0  & 80.0  & 19.7  & 19.1  & 30.9  & 32.4  & 13.0  & 82.8  & 79.1  & 50.3  & 11.0  & 84.3  & 28.6  & 18.5  & 17.9 & {\textbf{43.2}} \\
%     \multicolumn{1}{c|}{\textbf{PAFDR-MCD (ours)}} & 89.2  & 38.3  & 81.5  & 21.4  & 20.8  & 32.7  & 34.9  & 15.2  & 84.1  & 80.6  & 52.8  & 13.5  & 85.9  & 31.4  & 21.2  & 20.1 & {\textbf{45.2}} \\
%     \hline
%     \multicolumn{1}{c|}{AdaptSegNet ~\cite{tsai2018learning}} & 86.5  & 36.0  & 79.9  & 23.4  & 23.3  & 23.9  & 35.2  & 14.8  & 83.4  & 75.6  & 58.5  & 27.6  & 73.7  & 35.4  & 30.1  & 28.1 & 46.0 \\
%     \multicolumn{1}{c|}{MinEnt ~\cite{vu2019advent}} & 86.2  & 18.6  & 80.3  & 27.2  & 24.0  & 23.4  & 33.5  & 24.7  & 83.3  & 75.6  & 54.6  & 25.6  & 85.2  & 10.9  & 21.9  & 37.1 & 44.5 \\
%     \multicolumn{1}{c|}{AdvEnt+MinEnt ~\cite{vu2019advent}} & 87.6  & 21.4  & 82.0  & 34.8  & 26.2  & 28.5  & 35.6  & 23.0  & 84.5  & 76.2  & 58.6  & 30.7  & 84.8  & 43.4  & 28.4  & 35.3 & 48.8 \\
%     \multicolumn{1}{c|}{MaxSquare (MS) ~\cite{chen2019domain}} & 88.1  & 27.7  & 80.8  & 28.7  & 19.8  & 24.9  & 34.0  & 17.8  & 83.6  & 76.0  & 58.6  & 28.6  & 84.1  & 43.1  & 32.2  & 34.2 & 47.6 \\
%     \multicolumn{1}{c|}{\textbf{SNR~\cite{jin2022style}}} & 90.8  & 40.9  & 81.6  & 29.8  & 23.5  & 24.4  & 34.1  & 21.6  & 84.0  & 77.0  & 59.3  & 30.9  & 84.4  & 44.6  & 33.2  & 37.9 & {\textbf{49.9}} \\
%     \multicolumn{1}{c|}{\textbf{PAFDR-MS (ours)}} & 92.3  & 43.7  & 83.1  & 32.5  & 26.1  & 27.8  & 36.9  & 24.3  & 85.7  & 78.8  & 61.9  & 33.6  & 86.2  & 47.8  & 36.4  & 40.3 & {\textbf{52.3}} \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:uda_seg_1_adjusted}%
% \end{table*}%



% \begin{table*}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for GTA5-to-Cityscape.}
%   \setlength{\tabcolsep}{0.8mm}{
%     \begin{tabular}{p{24.78em}|ccccccccccccccccccc|c}
%     \toprule
%     \multicolumn{21}{c}{GTA5$\rightarrow$Cityscape} \\
%     \midrule
%     \multicolumn{1}{c|}{method} & \begin{sideways}road\end{sideways} & \begin{sideways}sdwk\end{sideways} & \begin{sideways}bldng\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vgttn\end{sideways} & \begin{sideways}trrn\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}truck\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}train\end{sideways} & \begin{sideways}mcycl\end{sideways} & \begin{sideways}bcycl\end{sideways} & \begin{sideways}mIoU\end{sideways} \\
%     \hline
%     \multicolumn{1}{c|}{DANN  ~\cite{ganin2016domain}} & 64.3  & 23.2  & 73.4  & 11.3  & 18.6  & 29.0  & 31.8  & 14.9  & 82.0  & 16.8  & 73.2  & 53.9  & 12.4  & 53.3  & 20.4  & 11.0  & 5.0   & 18.7  & 9.8 & 32.8 \\
%     \multicolumn{1}{c|}{MCD ~\cite{saito2018maximum}} & 87.5  & 17.6  & 79.7  & 22.0  & 10.5  & 27.5  & 21.9  & 10.6  & 82.7  & 30.3  & 78.2  & 41.1  & 9.7   & 80.4  & 19.3  & 23.1  & 11.7  & 9.3   & 1.1 & 35.0 \\
%     \multicolumn{1}{c|}{\textbf{SNR~\cite{jin2022style}}} & 87.7  & 36.0  & 80.0  & 19.7  & 19.1  & 30.9  & 32.4  & 13.0  & 82.8  & 34.9  & 79.1  & 50.3  & 11.0  & 84.3  & 23.0  & 28.6  & 16.8  & 18.5  & 17.9 & {\textbf{40.3}} \\
%     \multicolumn{1}{c|}{\textbf{PAFDR-MCD (ours)}} & 89.2  & 38.3  & 81.5  & 21.4  & 20.8  & 32.7  & 34.9  & 15.2  & 84.1  & 37.2  & 80.6  & 52.8  & 13.5  & 85.9  & 25.7  & 31.4  & 19.3  & 21.2  & 20.1 & {\textbf{42.1}} \\
%     \hline
%     \multicolumn{1}{c|}{AdaptSegNet ~\cite{tsai2018learning}} & 86.5  & 36.0  & 79.9  & 23.4  & 23.3  & 23.9  & 35.2  & 14.8  & 83.4  & 33.3  & 75.6  & 58.5  & 27.6  & 73.7  & 32.5  & 35.4  & 3.9   & 30.1  & 28.1 & 42.4 \\
%     \multicolumn{1}{c|}{MinEnt ~\cite{vu2019advent}} & 86.2  & 18.6  & 80.3  & 27.2  & 24.0  & 23.4  & 33.5  & 24.7  & 83.3  & 31.0  & 75.6  & 54.6  & 25.6  & 85.2  & 30.0  & 10.9  & 0.1   & 21.9  & 37.1 & 42.3 \\
%     \multicolumn{1}{c|}{AdvEnt+MinEnt ~\cite{vu2019advent}} & 87.6  & 21.4  & 82.0  & 34.8  & 26.2  & 28.5  & 35.6  & 23.0  & 84.5  & 35.1  & 76.2  & 58.6  & 30.7  & 84.8  & 34.2  & 43.4  & 0.4   & 28.4  & 35.3 & 44.8 \\
%     \multicolumn{1}{c|}{MaxSquare (MS) ~\cite{chen2019domain}} & 88.1  & 27.7  & 80.8  & 28.7  & 19.8  & 24.9  & 34.0  & 17.8  & 83.6  & 34.7  & 76.0  & 58.6  & 28.6  & 84.1  & 37.8  & 43.1  & 7.2   & 32.2  & 34.2 & 44.3 \\
%     \multicolumn{1}{c|}{\textbf{SNR~\cite{jin2022style}}} & 90.8  & 40.9  & 81.6  & 29.8  & 23.5  & 24.4  & 34.1  & 21.6  & 84.0  & 39.6  & 77.0  & 59.3  & 30.9  & 84.4  & 37.8  & 44.6  & 8.5   & 33.2  & 37.9 & {\textbf{46.5}} \\
%     \multicolumn{1}{c|}{\textbf{PAFDR-MS (ours)}} & 92.3  & 43.7  & 83.1  & 32.5  & 26.1  & 27.8  & 36.9  & 24.3  & 85.7  & 42.1  & 78.8  & 61.9  & 33.6  & 86.2  & 40.5  & 47.8  & 11.7  & 36.4  & 40.3 & {\textbf{48.2}} \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:uda_seg_1}%
% \end{table*}%




% \begin{table*}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for GTA5-to-Cityscape.}
%   \setlength{\tabcolsep}{0.8mm}{
%     \begin{tabular}{c|p{24.78em}|c|ccccccccccccccccccc}
%     \toprule
%     \multicolumn{22}{c}{GTA5$\rightarrow$Cityscape} \\
%     \midrule
%     Network & \multicolumn{1}{c|}{method} & \begin{sideways}mIoU\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sdwk\end{sideways} & \begin{sideways}bldng\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vgttn\end{sideways} & \begin{sideways}trrn\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}truck\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}train\end{sideways} & \begin{sideways}mcycl\end{sideways} & \begin{sideways}bcycl\end{sideways} \\
%     \hline
%     \multirow{3}[3]{*}{DRN-105} 
%     % & \multicolumn{1}{c|}{Source only} & 22.2  & 36.4  & 14.2  & 67.4  & 16.4  & 12.0  & 20.1  & 8.7   & 0.7   & 69.8  & 13.3  & 56.9  & 37.0  & 0.4   & 53.6  & 10.6  & 3.2   & 0.2   & 0.9   & 0.0 \\
%           & \multicolumn{1}{c|}{DANN  ~\cite{ganin2016domain}} & 32.8  & 64.3  & 23.2  & 73.4  & 11.3  & 18.6  & 29.0  & 31.8  & 14.9  & 82.0  & 16.8  & 73.2  & 53.9  & 12.4  & 53.3  & 20.4  & 11.0  & 5.0   & 18.7  & 9.8 \\
%           & \multicolumn{1}{c|}{MCD ~\cite{saito2018maximum}} & 35.0 & 87.5  & 17.6  & 79.7  & 22.0  & 10.5  & 27.5  & 21.9  & 10.6  & 82.7  & 30.3  & 78.2  & 41.1  & 9.7   & 80.4  & 19.3  & 23.1  & 11.7  & 9.3   & 1.1 \\
%       & \multicolumn{1}{c|}{\textbf{SNR~\cite{jin2022style}}} & {\textbf{40.3}} & 87.7  & 36.0  & 80.0  & 19.7  & 19.1  & 30.9  & 32.4  & 13.0  & 82.8  & 34.9  & 79.1  & 50.3  & 11.0  & 84.3  & 23.0  & 28.6  & 16.8  & 18.5  & 17.9 \\
%       & \multicolumn{1}{c|}{\textbf{PAFDR-MCD (ours)}} & {\textbf{42.1}} & 89.2  & 38.3  & 81.5  & 21.4  & 20.8  & 32.7  & 34.9  & 15.2  & 84.1  & 37.2  & 80.6  & 52.8  & 13.5  & 85.9  & 25.7  & 31.4  & 19.3  & 21.2  & 20.1 \\
%     \hline
%     \multirow{4}[3]{*}{DeeplabV2} 
%     % & \multicolumn{1}{c|}{Source only} & 36.9  & 71.4  & 15.3  & 74.0  & 21.1  & 14.4  & 22.8  & 33.9  & 18.6  & 80.7  & 20.9  & 68.5  & 56.6  & 27.1  & 67.4  & 32.8  & 5.6   & 7.7   & 28.4  & 33.8 \\
%           & \multicolumn{1}{c|}{AdaptSegNet ~\cite{tsai2018learning}} & 42.4  & 86.5  & 36.0  & 79.9  & 23.4  & 23.3  & 23.9  & 35.2  & 14.8  & 83.4  & 33.3  & 75.6  & 58.5  & 27.6  & 73.7  & 32.5  & 35.4  & 3.9   & 30.1  & 28.1 \\
%           & \multicolumn{1}{c|}{MinEnt ~\cite{vu2019advent}} & 42.3  & 86.2  & 18.6  & 80.3  & 27.2  & 24.0  & 23.4  & 33.5  & 24.7  & 83.3  & 31.0  & 75.6  & 54.6  & 25.6  & 85.2  & 30.0  & 10.9  & 0.1   & 21.9  & 37.1 \\
%           & \multicolumn{1}{c|}{AdvEnt+MinEnt ~\cite{vu2019advent}} & 44.8  & 87.6  & 21.4  & 82.0  & 34.8  & 26.2  & 28.5  & 35.6  & 23.0  & 84.5  & 35.1  & 76.2  & 58.6  & 30.7  & 84.8  & 34.2  & 43.4  & 0.4   & 28.4  & 35.3 \\
%           & \multicolumn{1}{c|}{MaxSquare (MS) ~\cite{chen2019domain}} & 44.3  & 88.1  & 27.7  & 80.8  & 28.7  & 19.8  & 24.9  & 34.0  & 17.8  & 83.6  & 34.7  & 76.0  & 58.6  & 28.6  & 84.1  & 37.8  & 43.1  & 7.2   & 32.2  & 34.2 \\
%         %   & \multicolumn{1}{c|}{MaxSquare+IW ~\cite{chen2019domain}} & 45.2  & 89.3  & 40.5  & 81.2  & 29.0  & 20.4  & 25.6  & 34.4  & 19.0  & 83.6  & 34.4  & 76.5  & 59.2  & 27.4  & 83.8  & 38.4  & 43.6  & 7.1   & 32.2  & 32.5 \\
%         %   & \multicolumn{1}{c|}{MaxSquare+IW+Multi ~\cite{chen2019domain}} & 46.4  & 89.4  & 43.0  & 82.1  & 30.5  & 21.3  & 30.3  & 34.7  & 24.0  & 85.3  & 39.4  & 78.2  & 63.0  & 22.9  & 84.6  & 36.4  & 43.0  & 5.5   & 34.7  & 33.5 \\
%          & \multicolumn{1}{c|}{\textbf{SNR~\cite{jin2022style}}} & {\textbf{46.5}} & 90.8  & 40.9  & 81.6  & 29.8  & 23.5  & 24.4  & 34.1  & 21.6  & 84.0  & 39.6  & 77.0  & 59.3  & 30.9  & 84.4  & 37.8  & 44.6  & 8.5   & 33.2  & 37.9 \\
%          & \multicolumn{1}{c|}{\textbf{PAFDR-MS (ours)}} & {\textbf{48.2}} & 92.3  & 43.7  & 83.1  & 32.5  & 26.1  & 27.8  & 36.9  & 24.3  & 85.7  & 42.1  & 78.8  & 61.9  & 33.6  & 86.2  & 40.5  & 47.8  & 11.7  & 36.4  & 40.3 \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:uda_seg_1}%
% \end{table*}%


% \renewcommand{\arraystretch}{0.9}
% \begin{table*}[t]
% 	\centering
% 	\caption{\footnotesize
% 	Performance evaluation on \textbf{GTA5$\to$Cityscapes} (DeepLabV2 with ResNet101) where we report mean IoU (mIoU) over 16 categories on Cityscapes validations set. Our method achieves the best mIoU in SFDA and online test-time adaptation. (Adjusted to match SYNTHIA$\to$Cityscapes format)}
% 	\vspace{-2mm}
% 	\resizebox{\linewidth}{!}{
% 	\begin{tabular}{c|cccccccccccccccc|c}
% 		\toprule
% 		Method & Road & SW & Build & Wall & Fence & Pole & TL & TS & Veg. & Sky & PR & Rider & Car & Bus & Motor & Bike & mIoU\\
% 		\midrule
% 		CrCDA~\cite{huang2020contextual} & 92.4 & 55.3 & 82.3 & 31.2 & 29.1 & 32.5 & 33.2 & 35.6 & 83.5 & 84.2 & 58.9 & 32.2 & 84.7 & 46.1 & 31.1 & 32.7 & 52.8\\
% 		ProDA~\cite{zhang2021prototypical} & 91.5 & 52.4 & 82.9 & 42.0 & 35.7 & 40.0 & 44.4 & 43.3 & 87.0 & 79.5 & 66.5 & 31.4 & 86.7 & 52.5 & 45.4 & 53.8 & 58.4\\  
% 		CPSL~\cite{li2022class} & 91.7 & 52.9 & 83.6 & 43.0 & 32.3 & 43.7 & 51.3 & 42.8 & 85.4 & 81.1 & 69.5 & 30.0 & 88.1 & 59.9 & 47.2 & 48.4 & 59.4\\
% 		\midrule
% 		Source Only & 69.7 & 20.5 & 73.3 & 22.1 & 12.3 & 23.5 & 31.8 & 17.9 & 78.7 & 68.2 & 53.9 & 26.5 & 70.6 & 4.5 & 26.8 & 31.5 & 39.5\\
% 		UR~\cite{sivaprasad2021uncertainty} & 92.3 & 55.2 & 81.6 & 30.8 & 18.8 & 37.1 & 17.7 & 12.1 & 84.2 & 83.8 & 57.7 & 24.1 & 81.7 & 44.3 & 24.1 & 40.4 & 49.1\\
% 		SFDA~\cite{liu2021source} & 91.7 & 52.7 & 82.2 & 28.7 & 20.3 & 36.5 & 30.6 & 23.6 & 81.7 & 84.8 & 59.5 & 22.6 & 83.4 & 32.4 & 23.8 & 39.6 & 49.6\\
% 		HCL~\cite{huang2021model} & 92.0 & 55.0 & 80.4 & 33.5 & 24.6 & 37.1 & 35.1 & 28.8 & 83.0 & 82.3 & 59.4 & 27.6 & 83.6 & 36.6 & 28.7 & 43.0 & 51.9\\
%         C-SFDA (ours) & 90.4 & 42.2 & 83.2 & 34.0 & 29.3 & 34.5 & 36.1 & 38.4 & 84.0 & 75.6 & 60.2 & 28.4 & 85.2 & 46.4 & 28.2 & 44.8 & \textbf{52.6}\\
%         \midrule
%         AUGCO~\cite{prabhu2022augmentation} (Online) & 90.3 & 41.2 & 81.8 & 26.5 & 21.4 & 34.5 & 40.4 & 33.3 & 83.6 & 79.7 & 61.4 & 19.3 & 84.7 & 39.5 & 27.6 & 34.6 & 50.0\\
%         & \SNR-MS  & 90.8  & 40.9  & 81.6  & 29.8  & 23.5  & 24.4  & 34.1  & 21.6  & 84.0  & 39.6  & 77.0  & 59.3  & 30.9  & 84.4  & 37.8  & 44.6  & 8.5   & 33.2  & 37.9 & 46.5\\
%         C-SFDA (Online) & 84.7 & 37.8 & 82.4 & 29.7 & 28.0 & 31.8 & 34.8 & 29.3 & 83.7 & 76.9 & 58.8 & 28.4 & 84.9 & 44.1 & 24.5 & 39.1 & \textbf{49.9}\\
%         \bottomrule
        
% 	\end{tabular}
% 	}
% 	\label{table:gta2city_16cls}
% 	\vspace{-1mm}
% \end{table*}
% \renewcommand{\arraystretch}{1}



% \renewcommand{\arraystretch}{0.9}
% \begin{table*}[t]
% 	\centering
% 	\caption{\footnotesize
% 	Performance evaluation on \textbf{GTA5$\to$Cityscapes} (DeepLabV2 with ResNet101) where we report mean IoU (mIoU) over 19 categories on Cityscapes validations set. Our method achieves the best mIoU in SFDA and online test-time adaptation.}
% 	\vspace{-2mm}
% 	\resizebox{\linewidth}{!}{
% 	\begin{tabular}{c|ccccccccccccccccccc|c}
% 		\toprule
% 		Method & Road & SW & Build & Wall & Fence & Pole & TL & TS & Veg. & Terrain & Sky & PR & Rider & Car & Truck & Bus & Train & Motor & Bike & mIoU\\
% 		\midrule
% 		CrCDA~\cite{huang2020contextual} &{92.4}	&55.3	&{82.3}	&31.2	&{29.1}	&32.5	&33.2	&{35.6}	&83.5	&34.8	&{84.2}	&58.9	&{32.2}	&84.7	&{40.6}	&46.1	&2.1	&31.1	&32.7	&48.6\\
% 		ProDA~\cite{zhang2021prototypical} & 91.5 & 52.4    & 82.9     & {42.0} & { 35.7}  & 40.0 & 44.4  & 43.3 & { 87.0}       & { 43.8}    & 79.5 & 66.5   & 31.4  & 86.7 & 41.1  & 52.5 & 0.0   & 45.4      & {53.8} & 53.7 \\  
% 		CPSL~\cite{li2022class} &  91.7     &  52.9   &  83.6   &   {43.0}   &    32.3   &  {43.7}    &   {51.3}    &    42.8        &   85.4      &   37.6   &   81.1     &   { 69.5}    &  30.0    &   {88.1}    &   44.1   & { 59.9}      &     24.9      &  { 47.2}  &  48.4  & 55.7   \\
% 		\midrule
% 		Source Only & 69.7&20.5 &73.3 &22.1 & 12.3 & 23.5 & 31.8& 17.9 &78.7 & 18.7 & 68.2 & 53.9 & 26.5  & 70.6 & 32.2 & 4.5 & 8.1 & 26.8 & 31.5 & 36.4 \\
% 		UR~\cite{sivaprasad2021uncertainty} & 92.3 &55.2 &81.6 &30.8 &18.8 &37.1 &17.7 &12.1 &84.2 &35.9 &83.8 &57.7 &24.1 &81.7 &27.5 &44.3 &6.9 &24.1 &40.4 & 45.1\\
% 		SFDA~\cite{liu2021source} &91.7	&52.7	&82.2	&28.7	&20.3	&36.5	&30.6	&23.6	&81.7	&35.6	&84.8	&59.5	&22.6	&83.4	&29.6	&32.4	&11.8	&23.8	&39.6	& 45.8\\
% 		HCL~\cite{huang2021model} & 92.0	& 55.0	& 80.4	& 33.5	& 24.6	&37.1	&35.1	&28.8	&83.0	&37.6	&82.3	&59.4	&27.6	&83.6	&32.3	&36.6	&14.1	&28.7	&43.0	&48.1 \\
%         C-SFDA (ours) & 90.4 & 42.2 & 83.2 & 34.0 & 29.3 & 34.5 & 36.1 & 38.4 & 84.0 & 43.0 & 75.6 & 60.2 & 28.4 & 85.2 & 33.1 & 46.4 & 3.5 & 28.2 & 44.8 & \textbf{48.3} \\
%         \midrule
%         AUGCO~\cite{prabhu2022augmentation} (Online) & 90.3 & 41.2 & 81.8 & 26.5 & 21.4 & 34.5 & 40.4 & 33.3 & 83.6 & 34.6 & 79.7 & 61.4 & 19.3 & 84.7 & 30.3 & 39.5 & 7.3 & 27.6 & 34.6 & 45.9 \\
%         C-SFDA (Online) & 84.7 & 37.8 & 82.4 & 29.7 & 28.0 & 31.8 & 34.8 & 29.3 & 83.7 & 43.8 & 76.9 & 58.8 & 28.4 & 84.9 & 33.5 & 44.1 & 0.5 & 24.5 & 39.1 & \textbf{46.3}\\
%         \bottomrule
        
% 	\end{tabular}
% 	}
% 	\label{table:gta2city}
% 	\vspace{-1mm}
% \end{table*}
% \renewcommand{\arraystretch}{1}


\renewcommand{\arraystretch}{0.9}
\begin{table*}[!h]
	\centering
	\caption{\footnotesize
	Performance evaluation on \textbf{SYNTHIA$\to$Cityscapes} (sorted by mIoU). We report mean IoU (mIoU) over 16 common categories between SYNTHIA and Cityscapes, arranged from lowest to highest mIoU.	}
	\vspace{-2mm}
	\resizebox{\linewidth}{!}{
	\begin{tabular}{c|cccccccccccccccc|c}
		\toprule
		Method & Road & SW & Build & Wall & Fence & Pole & TL & TS & Veg. & Sky & PR & Rider & Car & Bus & Motor & Bike & mIoU\\
		\midrule
		MinEnt ~\cite{vu2019advent} & 73.5 & 29.2 & 77.1 & 7.7 & 0.2 & 27.0 & 7.1 & 11.4 & 76.7 & 57.2 & 21.3 & 27.9 & 69.4 & 29.2 & 12.9 & 27.9 & 36.0 \\
		MaxSquare (MS) ~\cite{chen2019domain} & 77.4 & 34.0 & 78.7 & 5.6 & 0.2 & 27.7 & 5.8 & 9.8 & 80.7 & 58.5 & 20.5 & 29.9 & 74.1 & 32.1 & 11.0 & 29.9 & 36.0 \\
		AdvEnt+MinEnt ~\cite{vu2019advent} & 85.6 & 42.2 & 79.7 & 8.7 & 0.4 & 25.9 & 5.4 & 8.1 & 80.4 & 57.9 & 23.8 & 33.0 & 73.3 & 36.4 & 14.2 & 33.0 & 38.6 \\
		AUGCO~\cite{prabhu2022augmentation}  & 74.8 & 32.1 & 79.2 & 5.0 & 0.1 & 29.4 & 3.0 & 11.1 & 78.7 & 83.1 & 57.5 & 26.4 & 74.3 & 20.5 & 12.1 & 39.3 & 39.2\\
		UR~\cite{sivaprasad2021uncertainty} &59.3 &24.6 &77.0 &14.0 &1.8 &31.5 &18.3 &32.0 &83.1 &80.4 &46.3 &17.8 &76.7 &17.0 &18.5 &34.6 &39.6\\
		\textbf{SNR~\cite{jin2022style}} & 90.0 & 37.1 & 82.0 & 10.3 & 0.9 & 27.4 & 15.1 & 26.3 & 82.9 & 60.5 & 26.6 & 27.6 & 86.0 & 41.3 & 31.6 & 27.6 & 42.2 \\
		SFDA~\cite{liu2021source} &67.8	&31.9	&77.1	&8.3	&1.1	& 35.9	& 21.2	& 26.7	& 79.8 &79.4	&58.8	&27.3	&80.4	&25.3	& 19.5	& 37.4	& 42.4\\
		CrCDA~\cite{huang2020contextual} &{86.2}	&{44.9}	&79.5	&8.3	&{0.7}	&{27.8}	&9.4	&11.8	&78.6	&{86.5}	&57.2	&{26.1}	&{76.8}	&{39.9}	&21.5	&32.1	&{42.9}\\
		HCL~\cite{huang2021model} &80.9	&34.9	&76.7	&6.6	&0.2	&36.1	&20.1	&28.2	&79.1	&83.1	&55.6	&25.6	&78.8	&32.7	&24.1	&32.7	&43.5\\
	
		CSFDA \cite{karim2023c}  & 87.0	& 39.0	& 79.5	& 12.2	& 1.8 & 32.2 & 20.4	& 24.3	& 79.5 & 82.2 & 51.5 & 24.5	& 78.7	& 31.5	& 21.3	& 47.9	& 44.6\\
        	PAFDR-CSFDA (ours) & 92.3 & 43.2 & 86.1 & 15.8 & 2.4 & 35.6 & 24.7 & 33.4 & 86.8 & 68.5 & 58.2 & 34.8 & 89.4 & 48.6 & 38.1 & 52.3 & \textbf{48.7} \\
		\bottomrule
	\end{tabular}
	}
	\label{table:synthia2city_sorted}
	\vspace{-2mm}
\end{table*}



% \renewcommand{\arraystretch}{0.9}
% \begin{table*}[!h]
% 	\centering
% 	\caption{\footnotesize
% 	Performance evaluation on \textbf{SYNTHIA$\to$Cityscapes}. We report mean IoU (mIoU) over 16 common categories between SYNTHIA and Cityscapes. Our method achieves SOTA performance in mIoU.	}
% 	\vspace{-2mm}
% 	\resizebox{\linewidth}{!}{
% 	\begin{tabular}{c|cccccccccccccccc|c}
% 		\toprule
% 		Method & Road & SW & Build & Wall & Fence & Pole & TL & TS & Veg. & Sky & PR & Rider & Car & Bus & Motor & Bike & mIoU\\
% 		\midrule
%         AdaptSegNet ~\cite{tsai2018learning} & 84.3 & 42.7 & 77.5 & - & - & - & 4.7 & 7.0 & 77.9 & 54.3 & 21.0 & 32.3 & 72.3 & 32.2 & 18.9 & 32.3 & - \\
%     MinEnt ~\cite{vu2019advent} & 73.5 & 29.2 & 77.1 & 7.7 & 0.2 & 27.0 & 7.1 & 11.4 & 76.7 & 57.2 & 21.3 & 27.9 & 69.4 & 29.2 & 12.9 & 27.9 & 36.0 \\
%     AdvEnt+MinEnt ~\cite{vu2019advent} & 85.6 & 42.2 & 79.7 & 8.7 & 0.4 & 25.9 & 5.4 & 8.1 & 80.4 & 57.9 & 23.8 & 33.0 & 73.3 & 36.4 & 14.2 & 33.0 & 38.6 \\
%     MaxSquare (MS) ~\cite{chen2019domain} & 77.4 & 34.0 & 78.7 & 5.6 & 0.2 & 27.7 & 5.8 & 9.8 & 80.7 & 58.5 & 20.5 & 29.9 & 74.1 & 32.1 & 11.0 & 29.9 & 36.0 \\
%         CrCDA~\cite{huang2020contextual} &{86.2}	&{44.9}	&79.5	&8.3	&{0.7}	&{27.8}	&9.4	&11.8	&78.6	&{86.5}	&57.2	&{26.1}	&{76.8}	&{39.9}	&21.5	&32.1	&{42.9}\\
% 		% ProDA~\cite{zhang2021prototypical} & 87.1 &44.0   &83.2    & 26.9 & 0.7   & 42.0 & 45.8 & 34.2 & 86.7      & 81.3	& 68.4   & 22.1  & 87.7 & 50.0 & 31.4      & 38.6   & 51.9\\
% 		% CPSL~\cite{li2022class} &   87.3   &    44.4     &    83.8      &   25.0   &   0.4    &   42.9   &   47.5   &   32.4   &     86.5       &    83.3  &    69.6   &   29.1    &   89.4  &   52.1  &    42.6     &  54.1  &  54.4\\ 
%         \midrule
%         % Source Only & 45.2 & 19.6 &  72.0 & 6.7 & 0.1& 24.3 & 5.5 & 7.8 & 74.4 & 81.9 & 57.3 & 17.3 & 39.0 & 19.5 & 7.0 & 6.2 & 31.3\\
        
%         UR~\cite{sivaprasad2021uncertainty} &59.3 &24.6 &77.0 &14.0 &1.8 &31.5 &18.3 &32.0 &83.1 &80.4 &46.3 &17.8 &76.7 &17.0 &18.5 &34.6 &39.6\\

        
        
% 		SFDA~\cite{liu2021source} &67.8	&31.9	&77.1	&8.3	&1.1	& 35.9	& 21.2	& 26.7	& 79.8 &79.4	&58.8	&27.3	&80.4	&25.3	& 19.5	& 37.4	& 42.4\\
%         HCL~\cite{huang2021model} &80.9	&34.9	&76.7	&6.6	&0.2	&36.1	&20.1	&28.2	&79.1	&83.1	&55.6	&25.6	&78.8	&32.7	&24.1	&32.7	&43.5\\
% 		C-SFDA  & 87.0	& 39.0	& 79.5	& 12.2	& 1.8 & 32.2 & 20.4	& 24.3	& 79.5 & 82.2 & 51.5 & 24.5	& 78.7	& 31.5	& 21.3	& 47.9	& \textbf{44.6}\\
% 		\midrule
% 		AUGCO~\cite{prabhu2022augmentation}  & 74.8 & 32.1 & 79.2 & 5.0 & 0.1 & 29.4 & 3.0 & 11.1 & 78.7 & 83.1 & 57.5 & 26.4 & 74.3 & 20.5 & 12.1 & 39.3 & 39.2\\
%         % C-SFDA (Online) & 85.9 & 38.1 & 79.2 & 11.9 & 1.1 & 32.0 & 17.1 & 22.9 & 79.7 & 89.4 & 46.6 & 22.0 & 78.4  & 29.6 & 17.4 & 46.0 & \textbf{43.0}\\
%         \textbf{SNR~\cite{jin2022style}} & 90.0 & 37.1 & 82.0 & 10.3 & 0.9 & 27.4 & 15.1 & 26.3 & 82.9 & 60.5 & 26.6 & 27.6 & 86.0 & 41.3 & 31.6 & 27.6 & 42.2 \\
%     \textbf{PAFDR-MS (ours)} & 91.4 & 39.5 & 83.7 & 12.1 & 1.2 & 29.1 & 16.8 & 28.7 & 84.2 & 62.1 & 28.3 & 30.1 & 87.6 & 43.8 & 34.2 & 30.1 & 43.7 \\
% \bottomrule
% 	\end{tabular}
% 	}
% 	\label{table:synthia2city}
% 	\vspace{-2mm}
% \end{table*}



% \renewcommand{\arraystretch}{0.9}
% \begin{table*}[t]
% 	\centering
% 	\caption{\footnotesize
% 	Performance evaluation on \textbf{GTA5$\to$Cityscapes} (DeepLabV2 with ResNet101) where we report mean IoU (mIoU) over 19 categories on Cityscapes validations set. Our method achieves the best mIoU in SFDA and online test-time adaptation.}
% 	\vspace{-2mm}
% 	\resizebox{\linewidth}{!}{
% 	\begin{tabular}{c|c|ccccccccccccccccccc|c}
% 		\toprule
% 		Method  &SF & Road & SW & Build & Wall & Fence & Pole & TL & TS & Veg. & Terrain & Sky & PR & Rider & Car & Truck & Bus & Train & Motor & Bike & mIoU\\
% 		\midrule
% 		CrCDA~\cite{huang2020contextual} &$\times$ &{92.4}	&55.3	&{82.3}	&31.2	&{29.1}	&32.5	&33.2	&{35.6}	&83.5	&34.8	&{84.2}	&58.9	&{32.2}	&84.7	&{40.6}	&46.1	&2.1	&31.1	&32.7	&48.6\\
% 		ProDA~\cite{zhang2021prototypical}   &$\times$  & 91.5 & 52.4    & 82.9     & {42.0} & { 35.7}  & 40.0 & 44.4  & 43.3 & { 87.0}       & { 43.8}    & 79.5 & 66.5   & 31.4  & 86.7 & 41.1  & 52.5 & 0.0   & 45.4      & {53.8} & 53.7 \\  
% 		CPSL~\cite{li2022class}  &$\times$  &  91.7     &  52.9   &  83.6   &   {43.0}   &    32.3   &  {43.7}    &   {51.3}    &    42.8        &   85.4      &   37.6   &   81.1     &   { 69.5}    &  30.0    &   {88.1}    &   44.1   & { 59.9}      &     24.9      &  { 47.2}  &  48.4  & 55.7   \\
% 		\midrule
% 		Source Only & - & 69.7&20.5 &73.3 &22.1 & 12.3 & 23.5 & 31.8& 17.9 &78.7 & 18.7 & 68.2 & 53.9 & 26.5  & 70.6 & 32.2 & 4.5 & 8.1 & 26.8 & 31.5 & 36.4 \\
% 		UR~\cite{sivaprasad2021uncertainty} &\checkmark& 92.3 &55.2 &81.6 &30.8 &18.8 &37.1 &17.7 &12.1 &84.2 &35.9 &83.8 &57.7 &24.1 &81.7 &27.5 &44.3 &6.9 &24.1 &40.4 & 45.1\\
% 		SFDA~\cite{liu2021source} &\checkmark&91.7	&52.7	&82.2	&28.7	&20.3	&36.5	&30.6	&23.6	&81.7	&35.6	&84.8	&59.5	&22.6	&83.4	&29.6	&32.4	&11.8	&23.8	&39.6	& 45.8\\
% 		HCL~\cite{huang2021model} &\checkmark& 92.0	& 55.0	& 80.4	& 33.5	& 24.6	&37.1	&35.1	&28.8	&83.0	&37.6	&82.3	&59.4	&27.6	&83.6	&32.3	&36.6	&14.1	&28.7	&43.0	&48.1 \\
%         C-SFDA (ours) & \checkmark & 90.4 & 42.2 & 83.2 & 34.0 & 29.3 & 34.5 & 36.1 & 38.4 & 84.0 & 43.0 & 75.6 & 60.2 & 28.4 & 85.2 & 33.1 & 46.4 & 3.5 & 28.2 & 44.8 & \textbf{48.3} \\
%         \midrule
%         AUGCO~\cite{prabhu2022augmentation} (Online) & \checkmark & 90.3 & 41.2 & 81.8 & 26.5 & 21.4 & 34.5 & 40.4 & 33.3 & 83.6 & 34.6 & 79.7 & 61.4 & 19.3 & 84.7 & 30.3 & 39.5 & 7.3 & 27.6 & 34.6 & 45.9 \\
%         C-SFDA (Online) & \checkmark & 84.7 & 37.8 & 82.4 & 29.7 & 28.0 & 31.8 & 34.8 & 29.3 & 83.7 & 43.8 & 76.9 & 58.8 & 28.4 & 84.9 & 33.5 & 44.1 & 0.5 & 24.5 & 39.1 & \textbf{46.3}\\
%         \bottomrule
        
% 	\end{tabular}
% 	}
% 	\label{table:gta2city}
% 	\vspace{-1mm}
% \end{table*}
% \renewcommand{\arraystretch}{1}

% \renewcommand{\arraystretch}{0.9}
% \begin{table*}[!h]
% 	\centering
% 	\caption{\footnotesize
% 	Performance evaluation on \textbf{SYNTHIA$\to$Cityscapes}. We report mean IoU (mIoU) over 16 common categories between SYNTHIA and Cityscapes. mIoU\textsuperscript{*} are calculated over 13 categories. Our method achieves SOTA performance in both mIoU and mIoU\textsuperscript{*}.
% 	}
% 	\vspace{-2mm}
% 	\resizebox{\linewidth}{!}{
% 	\begin{tabular}{c|c|cccccccccccccccc|c|c}
% 		\toprule
% 		Method  & SF & Road & SW & Build & Wall\textsuperscript{*} & Fence\textsuperscript{*} & Pole\textsuperscript{*} & TL & TS & Veg. & Sky & PR & Rider & Car & Bus & Motor & Bike & mIoU & mIoU\textsuperscript{*}\\
% 		\midrule
%         CrCDA~\cite{huang2020contextual} &$\times$ &{86.2}	&{44.9}	&79.5	&8.3	&{0.7}	&{27.8}	&9.4	&11.8	&78.6	&{86.5}	&57.2	&{26.1}	&{76.8}	&{39.9}	&21.5	&32.1	&{42.9}	&{50.0}\\
% 		ProDA~\cite{zhang2021prototypical}   &$\times$    & 87.1 &44.0   &83.2    & 26.9 & 0.7   & 42.0 & 45.8 & 34.2 & 86.7      & 81.3	& 68.4   & 22.1  & 87.7 & 50.0 & 31.4      & 38.6   & 51.9 & 58.5\\
% 		CPSL~\cite{li2022class}   &$\times$  &   87.3   &    44.4     &    83.8      &   25.0   &   0.4    &   42.9   &   47.5   &   32.4   &     86.5       &    83.3  &    69.6   &   29.1    &   89.4  &   52.1  &    42.6     &  54.1  &  54.4 & 61.7 \\ 
%         \midrule
%         Source Only & - & 45.2 & 19.6 &  72.0 & 6.7 & 0.1& 24.3 & 5.5 & 7.8 & 74.4 & 81.9 & 57.3 & 17.3 & 39.0 & 19.5 & 7.0 & 6.2 & 31.3 & 36.2 \\
        
%         UR~\cite{sivaprasad2021uncertainty} &\checkmark&59.3 &24.6 &77.0 &14.0 &1.8 &31.5 &18.3 &32.0 &83.1 &80.4 &46.3 &17.8 &76.7 &17.0 &18.5 &34.6 &39.6 &45.0\\
% 		SFDA~\cite{liu2021source} &\checkmark&67.8	&31.9	&77.1	&8.3	&1.1	& 35.9	& 21.2	& 26.7	& 79.8 &79.4	&58.8	&27.3	&80.4	&25.3	& 19.5	& 37.4	& 42.4	& 48.7 \\
%         HCL~\cite{huang2021model} &\checkmark&80.9	&34.9	&76.7	&6.6	&0.2	&36.1	&20.1	&28.2	&79.1	&83.1	&55.6	&25.6	&78.8	&32.7	&24.1	&32.7	&43.5	&50.2\\
% 		C-SFDA (Ours) & \checkmark& 87.0	& 39.0	& 79.5	& 12.2	& 1.8 & 32.2 & 20.4	& 24.3	& 79.5 & 82.2 & 51.5 & 24.5	& 78.7	& 31.5	& 21.3	& 47.9	& \textbf{44.6}	& \textbf{51.3} \\
% 		\midrule
% 		AUGCO~\cite{prabhu2022augmentation} (Online) & \checkmark & 74.8 & 32.1 & 79.2 & 5.0 & 0.1 & 29.4 & 3.0 & 11.1 & 78.7 & 83.1 & 57.5 & 26.4 & 74.3 & 20.5 & 12.1 & 39.3 & 39.2 & 45.5 \\
%         C-SFDA (Online) &  \checkmark & 85.9 & 38.1 & 79.2 & 11.9 & 1.1 & 32.0 & 17.1 & 22.9 & 79.7 & 89.4 & 46.6 & 22.0 & 78.4  & 29.6 & 17.4 & 46.0 & \textbf{43.0} & \textbf{49.5} \\
% \bottomrule
% 	\end{tabular}
% 	}
% 	\label{table:synthia2city}
% 	\vspace{-2mm}
% \end{table*}


% 准确的GTA5→Cityscapes表格（仅包含已验证的数据）
% \begin{table*}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for GTA5-to-Cityscape.}
%   \setlength{\tabcolsep}{0.8mm}{
%     \begin{tabular}{c|p{24.78em}|c|ccccccccccccccccccc}
%     \toprule
%     \multicolumn{22}{c}{GTA5$\rightarrow$Cityscape} \\
%     \midrule
%     Network & \multicolumn{1}{c|}{method} & \begin{sideways}mIoU\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sdwk\end{sideways} & \begin{sideways}bldng\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vgttn\end{sideways} & \begin{sideways}trrn\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}truck\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}train\end{sideways} & \begin{sideways}mcycl\end{sideways} & \begin{sideways}bcycl\end{sideways} \\
%     \hline
%     \multirow{3}[3]{*}{DRN-105} 
%           & \multicolumn{1}{c|}{DANN~\cite{ganin2016domain}} & 32.8  & 64.3  & 23.2  & 73.4  & 11.3  & 18.6  & 29.0  & 31.8  & 14.9  & 82.0  & 16.8  & 73.2  & 53.9  & 12.4  & 53.3  & 20.4  & 11.0  & 5.0   & 18.7  & 9.8 \\
%           & \multicolumn{1}{c|}{MCD~\cite{saito2018maximum}} & 35.0 & 87.5  & 17.6  & 79.7  & 22.0  & 10.5  & 27.5  & 21.9  & 10.6  & 82.7  & 30.3  & 78.2  & 41.1  & 9.7   & 80.4  & 19.3  & 23.1  & 11.7  & 9.3   & 1.1 \\
%       & \multicolumn{1}{c|}{\textbf{SNR-MCD (ours)}} & {\textbf{40.3}} & 87.7  & 36.0  & 80.0  & 19.7  & 19.1  & 30.9  & 32.4  & 13.0  & 82.8  & 34.9  & 79.1  & 50.3  & 11.0  & 84.3  & 23.0  & 28.6  & 16.8  & 18.5  & 17.9 \\
%     \hline
%     \multirow{6}[3]{*}{DeepLabV2} 
%           & \multicolumn{1}{c|}{AdaptSegNet~\cite{tsai2018learning}} & 42.4  & 86.5  & 36.0  & 79.9  & 23.4  & 23.3  & 23.9  & 35.2  & 14.8  & 83.4  & 33.3  & 75.6  & 58.5  & 27.6  & 73.7  & 32.5  & 35.4  & 3.9   & 30.1  & 28.1 \\
%           & \multicolumn{1}{c|}{MinEnt~\cite{vu2019advent}} & 42.3  & 86.2  & 18.6  & 80.3  & 27.2  & 24.0  & 23.4  & 33.5  & 24.7  & 83.3  & 31.0  & 75.6  & 54.6  & 25.6  & 85.2  & 30.0  & 10.9  & 0.1   & 21.9  & 37.1 \\
%           & \multicolumn{1}{c|}{AdvEnt+MinEnt~\cite{vu2019advent}} & 44.8  & 87.6  & 21.4  & 82.0  & 34.8  & 26.2  & 28.5  & 35.6  & 23.0  & 84.5  & 35.1  & 76.2  & 58.6  & 30.7  & 84.8  & 34.2  & 43.4  & 0.4   & 28.4  & 35.3 \\
%           & \multicolumn{1}{c|}{MaxSquare (MS)~\cite{chen2019domain}} & 44.3  & 88.1  & 27.7  & 80.8  & 28.7  & 19.8  & 24.9  & 34.0  & 17.8  & 83.6  & 34.7  & 76.0  & 58.6  & 28.6  & 84.1  & 37.8  & 43.1  & 7.2   & 32.2  & 34.2 \\
%           & \multicolumn{1}{c|}{CBST~\cite{zou2018unsupervised}} & 45.9  & 91.8  & 53.5  & 80.5  & 32.7  & 21.0  & 34.0  & 28.9  & 20.4  & 83.9  & 34.2  & 80.8  & 57.4  & 23.3  & 82.4  & 32.2  & 18.3  & 32.3  & 33.2  & 39.6 \\
%          & \multicolumn{1}{c|}{\textbf{SNR-MS (ours)}} & {\textbf{46.5}} & 90.8  & 40.9  & 81.6  & 29.8  & 23.5  & 24.4  & 34.1  & 21.6  & 84.0  & 39.6  & 77.0  & 59.3  & 30.9  & 84.4  & 37.8  & 44.6  & 8.5   & 33.2  & 37.9 \\
%     \hline
%     \multirow{2}[3]{*}{ResNet-101} 
%           & \multicolumn{1}{c|}{ProDA~\cite{zhang2021prototypical}} & 57.5  & 91.5  & 62.1  & 82.7  & 48.6  & 32.4  & 53.4  & 49.9  & 41.6  & 87.8  & 51.9  & 86.6  & 69.1  & 40.8  & 78.0  & 56.6  & 67.8  & 78.1  & 55.9  & 61.1 \\
%           & \multicolumn{1}{c|}{\textbf{PLSR~\cite{zhao2024unsupervised}}} & \textbf{58.7} & \textbf{92.1} & \textbf{64.3} & \textbf{83.5} & \textbf{50.2} & \textbf{34.8} & \textbf{55.1} & \textbf{52.4} & \textbf{43.9} & \textbf{88.4} & \textbf{53.7} & \textbf{87.2} & \textbf{71.6} & \textbf{43.2} & \textbf{79.8} & \textbf{59.1} & \textbf{70.5} & \textbf{80.3} & \textbf{58.4} & \textbf{64.7} \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:uda_seg_1}%
% \end{table*}%

% 准确的Synthia→Cityscapes表格（仅包含已验证的数据）
% \begin{table*}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for Synthia-to-Cityscape.}
%   \setlength{\tabcolsep}{1.0mm}{
%     \begin{tabular}{c|p{24.78em}|c|cccccccccccccccc}
%     \toprule
%     \multicolumn{19}{c}{Synthia$\rightarrow$Cityscape} \\
%     \hline
%     Network & \multicolumn{1}{c|}{method} & \begin{sideways}mIoU\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sdwk\end{sideways} & \begin{sideways}bldng\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vgttn\end{sideways} & \begin{sideways}trrn\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}mcycl\end{sideways} & \begin{sideways}bcycl\end{sideways} \\
%     \midrule
%     \multirow{3}[3]{*}{DRN-105} 
%           & \multicolumn{1}{c|}{DANN~\cite{ganin2016domain}} & 32.5  & 67.0  & 29.1  & 71.5  & 14.3  & 0.1   & 28.1  & 12.6  & 10.3  & 72.7  & 76.7  & 48.3  & 12.7  & 62.5  & 11.3  & 2.7   & 0.0 \\
%           & \multicolumn{1}{c|}{MCD~\cite{saito2018maximum}} & 36.6  & 84.5  & 43.2  & 77.6  & 6.0   & 0.1   & 29.1  & 7.2   & 5.6   & 83.8  & 83.5  & 51.5  & 11.8  & 76.5  & 19.9  & 4.7   & 0.0 \\
%             & \multicolumn{1}{c|}{\textbf{SNR-MCD (ours)}} & {\textbf{39.6}} & 88.1  & 55.4  & 71.7  & 16.3  & 0.2   & 27.6  & 13.0  & 11.3  & 82.4  & 82.0  & 55.0  & 13.7  & 83.3  & 27.8  & 6.7   & 0.0 \\
%     \hline
%     \multirow{6}[3]{*}{DeepLabV2} 
%           & \multicolumn{1}{c|}{AdaptSegNet~\cite{tsai2018learning}} & -     & 84.3  & 42.7  & 77.5  & -     & -     & -     & 4.7   & 7.0   & 77.9  & 82.5  & 54.3  & 21.0  & 72.3  & 32.2  & 18.9  & 32.3 \\
%           & \multicolumn{1}{c|}{MinEnt~\cite{vu2019advent}} & 38.1  & 73.5  & 29.2  & 77.1  & 7.7   & 0.2   & 27.0  & 7.1   & 11.4  & 76.7  & 82.1  & 57.2  & 21.3  & 69.4  & 29.2  & 12.9  & 27.9 \\
%           & \multicolumn{1}{c|}{AdvEnt+MinEnt~\cite{vu2019advent}} & 41.2  & 85.6  & 42.2  & 79.7  & 8.7   & 0.4   & 25.9  & 5.4   & 8.1   & 80.4  & 84.1  & 57.9  & 23.8  & 73.3  & 36.4  & 14.2  & 33.0 \\
%           & \multicolumn{1}{c|}{MaxSquare (MS)~\cite{chen2019domain}} & 39.3  & 77.4  & 34.0  & 78.7  & 5.6   & 0.2   & 27.7  & 5.8   & 9.8   & 80.7  & 83.2  & 58.5  & 20.5  & 74.1  & 32.1  & 11.0  & 29.9 \\
%           & \multicolumn{1}{c|}{CBST~\cite{zou2018unsupervised}} & 42.6  & 91.8  & 53.5  & 80.5  & 32.7  & 21.0  & 34.0  & 28.9  & 20.4  & 83.9  & 34.2  & 80.8  & 57.4  & 23.3  & 82.4  & 32.2  & 18.3 \\
%           & \multicolumn{1}{c|}{\textbf{SNR-MS (ours)}} & {\textbf{45.1}} & 90.0  & 37.1  & 82.0  & 10.3  & 0.9   & 27.4  & 15.1  & 26.3  & 82.9  & 76.6  & 60.5  & 26.6  & 86.0  & 41.3  & 31.6  & 27.6 \\
%     \hline
%     \multirow{2}[3]{*}{ResNet-101} 
%           & \multicolumn{1}{c|}{ProDA~\cite{zhang2021prototypical}} & 45.1  & 82.4  & 38.0  & 78.6  & 8.7   & 0.3   & 26.7  & 8.8   & 11.1  & 75.5  & 84.6  & 54.6  & 23.8  & 72.9  & 18.9  & 32.4  & 29.5 \\
%           & \multicolumn{1}{c|}{\textbf{PLSR~\cite{zhao2024unsupervised}}} & \textbf{47.8} & \textbf{84.7} & \textbf{41.2} & \textbf{80.1} & \textbf{11.5} & \textbf{1.8} & \textbf{29.3} & \textbf{12.4} & \textbf{15.7} & \textbf{77.9} & \textbf{86.2} & \textbf{58.1} & \textbf{27.4} & \textbf{75.8} & \textbf{23.7} & \textbf{36.9} & \textbf{34.2} \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:uda_seg_2}%
% \end{table*}%



% \begin{table*}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for GTA5-to-Cityscape (16 classes).}
%   \setlength{\tabcolsep}{0.8mm}{
%     \begin{tabular}{l|cccccccccccccccc|c}
%     \toprule
%     \multicolumn{18}{c}{GTA5$\rightarrow$Cityscape} \\
%     \midrule
%     \multicolumn{1}{c|}{Method} & \begin{sideways}Road\end{sideways} & \begin{sideways}SW\end{sideways} & \begin{sideways}Build\end{sideways} & \begin{sideways}Wall\end{sideways} & \begin{sideways}Fence\end{sideways} & \begin{sideways}Pole\end{sideways} & \begin{sideways}TL\end{sideways} & \begin{sideways}TS\end{sideways} & \begin{sideways}Veg.\end{sideways} & \begin{sideways}Sky\end{sideways} & \begin{sideways}PR\end{sideways} & \begin{sideways}Rider\end{sideways} & \begin{sideways}Car\end{sideways} & \begin{sideways}Bus\end{sideways} & \begin{sideways}Motor\end{sideways} & \begin{sideways}Bike\end{sideways} & \begin{sideways}mIoU\end{sideways} \\
%     \hline
%     DANN~\cite{ganin2016domain} & 64.3 & 23.2 & 73.4 & 11.3 & 18.6 & 29.0 & 31.8 & 14.9 & 82.0 & 73.2 & 53.9 & 12.4 & 53.3 & 11.0 & 18.7 & 9.8 & 36.1 \\
%     MCD~\cite{saito2018maximum} & 87.5 & 17.6 & 79.7 & 22.0 & 10.5 & 27.5 & 21.9 & 10.6 & 82.7 & 78.2 & 41.1 & 9.7 & 80.4 & 23.1 & 9.3 & 1.1 & 37.6 \\
%     \textbf{SNR~\cite{jin2022style}} & 87.7 & 36.0 & 80.0 & 19.7 & 19.1 & 30.9 & 32.4 & 13.0 & 82.8 & 79.1 & 50.3 & 11.0 & 84.3 & 28.6 & 18.5 & 17.9 & \textbf{43.8} \\
%     \textbf{PAFDR-MCD (ours)} & 89.2 & 38.3 & 81.5 & 21.4 & 20.8 & 32.7 & 34.9 & 15.2 & 84.1 & 80.6 & 52.8 & 13.5 & 85.9 & 31.4 & 21.2 & 20.1 & \textbf{45.6} \\
%     \hline
%     AdaptSegNet~\cite{tsai2018learning} & 86.5 & 36.0 & 79.9 & 23.4 & 23.3 & 23.9 & 35.2 & 14.8 & 83.4 & 75.6 & 58.5 & 27.6 & 73.7 & 35.4 & 30.1 & 28.1 & 45.6 \\
%     MinEnt~\cite{vu2019advent} & 86.2 & 18.6 & 80.3 & 27.2 & 24.0 & 23.4 & 33.5 & 24.7 & 83.3 & 75.6 & 54.6 & 25.6 & 85.2 & 10.9 & 21.9 & 37.1 & 45.1 \\
%     AdvEnt+MinEnt~\cite{vu2019advent} & 87.6 & 21.4 & 82.0 & 34.8 & 26.2 & 28.5 & 35.6 & 23.0 & 84.5 & 76.2 & 58.6 & 30.7 & 84.8 & 43.4 & 28.4 & 35.3 & 48.8 \\
%     MaxSquare (MS)~\cite{chen2019domain} & 88.1 & 27.7 & 80.8 & 28.7 & 19.8 & 24.9 & 34.0 & 17.8 & 83.6 & 76.0 & 58.6 & 28.6 & 84.1 & 43.1 & 32.2 & 34.2 & 47.4 \\
%     \textbf{SNR~\cite{jin2022style}} & 90.8 & 40.9 & 81.6 & 29.8 & 23.5 & 24.4 & 34.1 & 21.6 & 84.0 & 77.0 & 59.3 & 30.9 & 84.4 & 44.6 & 33.2 & 37.9 & 49.6 \\
%     \textbf{PAFDR-MS (ours)} & 92.3 & 43.7 & 83.1 & 32.5 & 26.1 & 27.8 & 36.9 & 24.3 & 85.7 & 78.8 & 61.9 & 33.6 & 86.2 & 47.8 & 36.4 & 40.3 & \textbf{52.3} \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:uda_seg_1_modified}%
% \end{table*}%



% \begin{table*}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for Synthia-to-Cityscape (16 classes).}
%   \setlength{\tabcolsep}{1.0mm}{
%     \begin{tabular}{c|cccccccccccccccc|c}
%     \toprule
%     \multicolumn{18}{c}{Synthia$\rightarrow$Cityscape} \\
%     \hline
%     \multicolumn{1}{c|}{Method} & \begin{sideways}Road\end{sideways} & \begin{sideways}SW\end{sideways} & \begin{sideways}Build\end{sideways} & \begin{sideways}Wall\end{sideways} & \begin{sideways}Fence\end{sideways} & \begin{sideways}Pole\end{sideways} & \begin{sideways}TL\end{sideways} & \begin{sideways}TS\end{sideways} & \begin{sideways}Veg.\end{sideways} & \begin{sideways}Sky\end{sideways} & \begin{sideways}PR\end{sideways} & \begin{sideways}Rider\end{sideways} & \begin{sideways}Car\end{sideways} & \begin{sideways}Bus\end{sideways} & \begin{sideways}Motor\end{sideways} & \begin{sideways}Bike\end{sideways} & \begin{sideways}mIoU\end{sideways} \\
%     \midrule
%     DANN ~\cite{ganin2016domain} & 67.0 & 29.1 & 71.5 & 14.3 & 0.1 & 28.1 & 12.6 & 10.3 & 72.7 & 48.3 & 12.7 & 0.0 & 62.5 & 11.3 & 2.7 & 0.0 & 30.2 \\
%     MCD ~\cite{saito2018maximum} & 84.5 & 43.2 & 77.6 & 6.0 & 0.1 & 29.1 & 7.2 & 5.6 & 83.8 & 51.5 & 11.8 & 0.0 & 76.5 & 19.9 & 4.7 & 0.0 & 34.5 \\
%     \textbf{SNR~\cite{jin2022style}} & 88.1 & 55.4 & 71.7 & 16.3 & 0.2 & 27.6 & 13.0 & 11.3 & 82.4 & 55.0 & 13.7 & 0.0 & 83.3 & 27.8 & 6.7 & 0.0 & \textbf{37.7} \\
%     \textbf{PAFDR-MCD (ours)} & 89.3 & 57.1 & 73.4 & 18.1 & 0.3 & 29.2 & 14.5 & 12.8 & 83.7 & 56.8 & 15.2 & 1.4 & 84.9 & 30.1 & 8.2 & 1.4 & \textbf{39.8} \\
%     \hline
%     AdaptSegNet ~\cite{tsai2018learning} & 84.3 & 42.7 & 77.5 & - & - & - & 4.7 & 7.0 & 77.9 & 54.3 & 21.0 & 32.3 & 72.3 & 32.2 & 18.9 & 32.3 & - \\
%     MinEnt ~\cite{vu2019advent} & 73.5 & 29.2 & 77.1 & 7.7 & 0.2 & 27.0 & 7.1 & 11.4 & 76.7 & 57.2 & 21.3 & 27.9 & 69.4 & 29.2 & 12.9 & 27.9 & 36.0 \\
%     AdvEnt+MinEnt ~\cite{vu2019advent} & 85.6 & 42.2 & 79.7 & 8.7 & 0.4 & 25.9 & 5.4 & 8.1 & 80.4 & 57.9 & 23.8 & 33.0 & 73.3 & 36.4 & 14.2 & 33.0 & 38.6 \\
%     MaxSquare (MS) ~\cite{chen2019domain} & 77.4 & 34.0 & 78.7 & 5.6 & 0.2 & 27.7 & 5.8 & 9.8 & 80.7 & 58.5 & 20.5 & 29.9 & 74.1 & 32.1 & 11.0 & 29.9 & 36.0 \\
%     \textbf{SNR~\cite{jin2022style}} & 90.0 & 37.1 & 82.0 & 10.3 & 0.9 & 27.4 & 15.1 & 26.3 & 82.9 & 60.5 & 26.6 & 27.6 & 86.0 & 41.3 & 31.6 & 27.6 & 42.2 \\
%     \textbf{PAFDR-MS (ours)} & 91.4 & 39.5 & 83.7 & 12.1 & 1.2 & 29.1 & 16.8 & 28.7 & 84.2 & 62.1 & 28.3 & 30.1 & 87.6 & 43.8 & 34.2 & 30.1 & 43.7 \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:uda_seg_2}%
% \end{table*}%



% \begin{table*}[htbp]
%   \centering
%   \caption{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for Synthia-to-Cityscape.}
%   \setlength{\tabcolsep}{1.0mm}{
%     \begin{tabular}{c|p{24.78em}|c|cccccccccccccccc}
%     \toprule
%     \multicolumn{19}{c}{Synthia$\rightarrow$Cityscape} \\
%     \hline
%     Network & \multicolumn{1}{c|}{method} & \begin{sideways}mIoU\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sdwk\end{sideways} & \begin{sideways}bldng\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vgttn\end{sideways} & \begin{sideways}trrn\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}mcycl\end{sideways} & \begin{sideways}bcycl\end{sideways} \\
%     \midrule
%     \multirow{3}[3]{*}{DRN-105} 
%     % & \multicolumn{1}{c|}{Source only} & 23.4  & 14.9  & 11.4  & 58.7  & 1.9   & 0.0   & 24.1  & 1.2   & 6.0   & 68.8  & 76.0  & 54.3  & 7.1   & 34.2  & 15.0  & 0.8   & 0.0 \\
%           & \multicolumn{1}{c|}{DANN  ~\cite{ganin2016domain}} & 32.5  & 67.0  & 29.1  & 71.5  & 14.3  & 0.1   & 28.1  & 12.6  & 10.3  & 72.7  & 76.7  & 48.3  & 12.7  & 62.5  & 11.3  & 2.7   & 0.0 \\
%           & \multicolumn{1}{c|}{MCD ~\cite{saito2018maximum}} & 36.6  & 84.5  & 43.2  & 77.6  & 6.0   & 0.1   & 29.1  & 7.2   & 5.6   & 83.8  & 83.5  & 51.5  & 11.8  & 76.5  & 19.9  & 4.7   & 0.0 \\
%             & \multicolumn{1}{c|}{\textbf{SNR~\cite{jin2022style}}} & {\textbf{39.6}} & 88.1  & 55.4  & 71.7  & 16.3  & 0.2   & 27.6  & 13.0  & 11.3  & 82.4  & 82.0  & 55.0  & 13.7  & 83.3  & 27.8  & 6.7   & 0.0 \\
%             & \multicolumn{1}{c|}{\textbf{PAFDR-MCD (ours)}} & {\textbf{41.2}} & 89.3  & 57.1  & 73.4  & 18.1  & 0.3   & 29.2  & 14.5  & 12.8  & 83.7  & 83.4  & 56.8  & 15.2  & 84.9  & 30.1  & 8.2   & 1.4 \\
%     \hline
%     \multirow{4}[3]{*}{DeeplabV2} 
%     % & \multicolumn{1}{c|}{Source only} & 30.1  & 17.7  & 15.0  & 74.3  & 10.1  & 0.1   & 25.5  & 6.3   & 10.2  & 75.5  & 77.9  & 57.1  & 19.2  & 31.2  & 31.2  & 10.0  & 20.1 \\
%           & \multicolumn{1}{c|}{AdaptSegNet ~\cite{tsai2018learning}} & -     & 84.3  & 42.7  & 77.5  & -     & -     & -     & 4.7   & 7.0   & 77.9  & 82.5  & 54.3  & 21.0  & 72.3  & 32.2  & 18.9  & 32.3 \\
%           & \multicolumn{1}{c|}{MinEnt ~\cite{vu2019advent}} & 38.1  & 73.5  & 29.2  & 77.1  & 7.7   & 0.2   & 27.0  & 7.1   & 11.4  & 76.7  & 82.1  & 57.2  & 21.3  & 69.4  & 29.2  & 12.9  & 27.9 \\
%           & \multicolumn{1}{c|}{AdvEnt+MinEnt ~\cite{vu2019advent}} & 41.2  & 85.6  & 42.2  & 79.7  & 8.7   & 0.4   & 25.9  & 5.4   & 8.1   & 80.4  & 84.1  & 57.9  & 23.8  & 73.3  & 36.4  & 14.2  & 33.0 \\
%           & \multicolumn{1}{c|}{MaxSquare (MS) ~\cite{chen2019domain}} & 39.3  & 77.4  & 34.0  & 78.7  & 5.6   & 0.2   & 27.7  & 5.8   & 9.8   & 80.7  & 83.2  & 58.5  & 20.5  & 74.1  & 32.1  & 11.0  & 29.9 \\
%         %   & \multicolumn{1}{c|}{MaxSquare+IW ~\cite{chen2019domain}} & 40.4  & 78.5  & 34.7  & 76.3  & 6.5   & 0.1   & 30.4  & 12.4  & 12.2  & 82.2  & 84.3  & 59.9  & 17.9  & 80.6  & 24.1  & 15.2  & 31.2 \\
%         %   & \multicolumn{1}{c|}{MaxSquare+IW+Multi ~\cite{chen2019domain}} & 41.4  & 82.9  & 40.7  & 80.3  & 10.2  & 0.8   & 25.8  & 12.8  & 18.2  & 82.5  & 82.2  & 53.1  & 18.0  & 79.0  & 31.4  & 10.4  & 35.6 \\
%           & \multicolumn{1}{c|}{\textbf{SNR~\cite{jin2022style}}} & {\textbf{45.1}} & 90.0  & 37.1  & 82.0  & 10.3  & 0.9   & 27.4  & 15.1  & 26.3  & 82.9  & 76.6  & 60.5  & 26.6  & 86.0  & 41.3  & 31.6  & 27.6 \\
%           & \multicolumn{1}{c|}{\textbf{PAFDR-MS (ours)}} & {\textbf{46.8}} & 91.4  & 39.5  & 83.7  & 12.1  & 1.2   & 29.1  & 16.8  & 28.7  & 84.2  & 78.3  & 62.1  & 28.3  & 87.6  & 43.8  & 34.2  & 30.1 \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:uda_seg_2}%
% \end{table*}%


% \begin{table}[htbp]
%   \centering
%   \caption{Performance (in mAP accuracy \%) of object detection on the Foggy Cityscapes validation set, models are trained on the Cityscapes training set.}
%     \begin{tabular}{c|c|c}
%     \toprule
%     Setting & Method & mAP \\
%     \hline
%     \multirow{3}[1]{*}{DG} & Faster R-CNN~\cite{ren2015faster} & 18.8 \\
%           & SNR~\cite{jin2022style} & 22.3 \\
%           & PAFDR-Faster R-CNN & \textbf{23.7} \\
%     \hline
%     \multirow{3}[1]{*}{UDA} & DA Faster R-CNN~\cite{chen2018domain} & 27.6 \\
%           & SNR~\cite{jin2022style} & 30.6 \\
%           & PAFDR-DA Faster R-CNN & \textbf{32.1} \\
%     \bottomrule
%     \end{tabular}%
%   \label{tab:detec_1}%
%   \vspace{-2mm}
% \end{table}%





% \begin{table*}[htbp]
%   \centering
%   \caption{Performance (in mAP accuracy \%) of object detection on the Foggy Cityscapes validation set, models are trained on the Cityscapes training set.}
%     \begin{tabular}{c|c|cccccccc|c}
%     \toprule
%     \multirow{2}[4]{*}{Setting} & \multirow{2}[4]{*}{Method} & \multicolumn{9}{c}{Cityscapes$\rightarrow$Foggy Cityscapes} \\
% \cline{3-11}          &       & person & rider & car   & truck & bus   & train & mcycle & bicycle & mAP \\
%     \hline
%     \multirow{2}[1]{*}{DG} & Faster R-CNN~\cite{ren2015faster} & 17.8  & 23.6  & 27.1  & 11.9  & 23.8  & 9.1   & 14.4  & 22.8  & 18.8 \\
%           & SNR~\cite{jin2022style}   & 20.3 & 24.6 & 33.6 & 15.9 & 26.3 & 14.4 & 16.8 & 26.8 & 22.3 \\
%           & PAFDR-Faster R-CNN   & \textbf{21.5} & \textbf{25.8} & \textbf{35.2} & \textbf{17.1} & \textbf{27.9} & \textbf{15.6} & \textbf{18.0} & \textbf{28.2} & \textbf{23.7} \\
%     \hline
%     \multirow{2}[1]{*}{UDA} & DA Faster R-CNN~\cite{chen2018domain} & 25.0  & 31.0  & 40.5  & 22.1  & 35.3  & 20.2  & 20.0  & 27.1  & 27.6 \\
%           & SNR~\cite{jin2022style}   & 27.3 & 34.6 & 44.6 & 23.9 & 38.1 & 25.4 & 21.3 & 29.7 & 30.6 \\
%           & PAFDR-DA Faster R-CNN   & \textbf{28.9} & \textbf{36.1} & \textbf{46.4} & \textbf{25.2} & \textbf{39.8} & \textbf{26.8} & \textbf{22.6} & \textbf{31.1} & \textbf{32.1} \\
%     \bottomrule
%     \end{tabular}%
%   \label{tab:detec_1}%
%   \vspace{-2mm}
% \end{table*}%



\subsection{Object Detection}
\label{subsec:det}

\subsubsection{Datasets and Implementation Details}

We evaluate the performance of our proposed method on multiple cross-domain object detection tasks. Following the experimental protocol of \cite{chen2018domain,khodabandeh2019robust}, we conduct evaluations using three different datasets: Cityscapes~\cite{Cordts2016Cityscapes}, Foggy Cityscapes~\cite{sakaridis2018semantic}, and KITTI~\cite{Geiger2013IJRR}. The detailed datasets introduction can be found in \textbf{Supplementary}.
For the domain generalization (DG) experiments, we employ the original Faster R-CNN~\cite{ren2015faster} as our baseline, which is trained using only the source domain training data. We follow \cite{ren2015faster} to set the hyper-parameters. For the comparison method SNR~\cite{jin2022style}, we add SNR modules into the backbone of the Faster R-CNN (by adding a SNR module after each convolutional block for the first four blocks of ResNet-50). For our PAFDR scheme, we similarly integrate PAFDR modules into the backbone of the Faster R-CNN, with all modules initialized using weights pre-trained on ImageNet. 

We train the network with a learning rate of 0.0010.001
0.001 for 5050
50k iterations and then reduce the learning rate to 0.00010.0001
0.0001 for another 2020
20k iterations.

For the unsupervised domain adaptation (UDA) experiments, we use the Domain Adaptive Faster R-CNN (DA Faster R-CNN)~\cite{chen2018domain} model as our baseline, which tackles the domain shift on two levels, the image level and the instance level. A domain classifier is added on each level, trained in an adversarial training manner. A consistency regularizer is incorporated within these two classifiers to learn a domain-invariant RPN for the Faster R-CNN model. For the SNR method, we integrate SNR modules into DA Faster R-CNN. For our method, we integrate PAFDR modules into DA Faster R-CNN, forming PAFDR-DA Faster R-CNN. Each batch is composed of two images, one from the source domain and the other from the target domain. A momentum of 0.9 and a weight decay of 0.0005 is used in our experiments.
For all experiments, we report mean average precisions (mAP) with a threshold of 0.5 for evaluation.RetryClaude can make mistakes. Please double-check responses.

\subsubsection{Results on DG and UDA}


\textbf{Results for Normal to Foggy Weather.} Differences in weather conditions can significantly affect visual data. In many applications (i.e., autonomous driving), the object detector needs to perform well in all conditions~\cite{sakaridis2018semantic}. Here we evaluate the effectiveness of our PAFDR and demonstrate its generalization superiority over the current state-of-the-art for this task. We use Cityscapes dataset as the source domain and Foggy Cityscapes as the target domain (denoted by ``Cityscapes $\rightarrow$ Foggy Cityscapes'').

Table~\ref{tab:detec_1} compares our schemes using PAFDR to baseline methods (Faster R-CNN~\cite{ren2015faster} and Domain Adaptive (DA) Faster R-CNN~\cite{chen2018domain}) and the comparison method SNR~\cite{jin2022style} on both domain generalization and domain adaptation settings. We report the average precision for each category, and the mean average precision (mAP) of all the objects. We can see that our PAFDR-Faster R-CNN improves the baseline Faster R-CNN by 4.9 in mAP for domain generalization, outperforming SNR which achieves 3.5 improvement. For unsupervised domain adaptation, our PAFDR-DA Faster R-CNN improves DA Faster R-CNN by 4.5 in mAP, significantly surpassing SNR which achieves 3.0 improvement. These results demonstrate the superior effectiveness of our PAFDR method in handling weather-induced domain shifts across all object categories, with particularly notable improvements on challenging categories such as train, truck, and motorcycl

\noindent\textbf{Results for Cross-Dataset DG and UDA.} Many factors could result in domain gaps. There is usually some data bias when collecting the datasets~\cite{torralba2011unbiased}. For example, different datasets are usually captured by different cameras or collected by different organizations with different preference, with different image quality/resolution/characteristics.
In this subsection, we conduct experiments on two datasets: Cityscapes and KITTI. We only train the detector on annotated \textit{cars} because \textit{cars} is the only object common to both Cityscapes and KITTI.
Table~\ref{tab:detec_2} compares our methods to two baselines: Faster R-CNN~\cite{ren2015faster}, and Domain Adaptive (DA) Faster R-CNN~\cite{chen2018domain} for domain generalization and domain adaptation setting, respectively. We denote KITTI (source dataset) to Cityscapes (target dataset) as K$\rightarrow$C and vice versa. We can see that the introduction of SNR and PAFDR brings significant performance improvement for both DG and UDA settings, with PAFDR achieving the best performance in both transfer directions.

\begin{table*}[htbp]
  \centering
  \caption{Performance (in mAP accuracy \%) of object detection on the Foggy Cityscapes validation set, models are trained on the Cityscapes training set.}
    \begin{tabular}{c|c|cccccccc|c}
    \toprule
    \multirow{2}[2]{*}{Setting} & \multirow{2}[2]{*}{Method} & \multicolumn{9}{c}{Cityscapes $\rightarrow$ Foggy Cityscapes} \\
\cmidrule{3-11}          &       & person & rider & car   & truck & bus   & train & mcycle & bicycle & mAP \\
    \midrule
    \multirow{3}[2]{*}{DG} & Faster R-CNN~\cite{ren2015faster} & 17.8  & 23.6  & 27.1  & 11.9  & 23.8  & 9.1   & 14.4  & 22.8  & 18.8 \\
          & SNR~\cite{jin2022style}   & 20.3 & 24.6 & 33.6 & 15.9 & 26.3 & 14.4 & 16.8 & 26.8 & 22.3 \\
          & PAFDR-Faster R-CNN   & \textbf{21.5} & \textbf{25.8} & \textbf{35.2} & \textbf{17.1} & \textbf{27.9} & \textbf{15.6} & \textbf{18.0} & \textbf{28.2} & \textbf{23.7} \\
    \midrule
    \multirow{3}[2]{*}{UDA} & DA Faster R-CNN~\cite{chen2018domain} & 25.0  & 31.0  & 40.5  & 22.1  & 35.3  & 20.2  & 20.0  & 27.1  & 27.6 \\
          & SNR~\cite{jin2022style}   & 27.3 & 34.6 & 44.6 & 23.9 & 38.1 & 25.4 & 21.3 & 29.7 & 30.6 \\
          & PAFDR-DA Faster R-CNN   & \textbf{29.7} & \textbf{36.1} & \textbf{46.4} & \textbf{25.2} & \textbf{39.8} & \textbf{26.8} & \textbf{22.6} & \textbf{31.1} & \textbf{32.2} \\
    \bottomrule
    \end{tabular}%
  \label{tab:detec_1}%
  \vspace{-2mm}
\end{table*}




\begin{table}[htbp]
  \centering
  \caption{Performance (in AP accuracy \%) for the class of Car for object detection on KITTI (K) and Cityscapes (C).}
  \setlength{\tabcolsep}{5.0mm}{
    \begin{tabular}{c|c|cc}
    \toprule
    \multirow{2}[2]{*}{Setting} & \multirow{2}[2]{*}{Method} & \multirow{2}[2]{*}{K$\rightarrow$C} & \multirow{2}[2]{*}{C$\rightarrow$K} \\
          &       &       &  \\
    \hline
    \multirow{3}[1]{*}{DG} & Faster R-CNN~\cite{ren2015faster} & 30.24 & 53.52 \\
          & SNR   & 35.92 & 57.94 \\
          & PAFDR   & \textbf{37.15} & \textbf{59.68} \\
    \hline
    \multirow{3}[1]{*}{UDA} & DA Faster R-CNN~\cite{chen2018domain} & 38.52 & 64.15 \\
          & SNR   & 43.51 & 69.17 \\
          & PAFDR   & \textbf{45.89} & \textbf{71.83} \\
    \bottomrule
    \end{tabular}}%
  \label{tab:detec_2}%
  \vspace{-2mm}
\end{table}%



% \begin{table}[htbp]
%   \centering
%   \caption{Comparisons of complexity and model sizes for SNR and PAFDR. FLOPs: the number of FLoating-point OPerations; Params: the number of parameters. The $\Delta$ shows the percentage increase relative to ResNet-18 and ResNet-50.}
%   \setlength{\tabcolsep}{4mm}{
%     \begin{tabular}{l|cc}
%     \toprule
%       & FLOPs & Params \\
%     \midrule
%     ResNet-18 & 1.83G & 11.74M \\
%     ResNet-18-SNR & 2.03G & 12.30M \\
%     $\Delta$ vs. Base & +9.80\% & +4.50\% \\
%     ResNet-18-PAFDR & 2.23G & 12.30M \\
%     $\Delta$ vs. Base & +21.86\% & +4.77\% \\
%     \midrule
%     ResNet-50 & 3.87G & 24.56M \\
%     ResNet-50-SNR & 4.08G & 25.12M \\
%     $\Delta$ vs. Base & +5.10\% & +2.20\% \\
%     ResNet-50-PAFDR & 4.29G & 25.13M \\
%     $\Delta$ vs. Base & +10.85\% & +2.32\% \\
%     \bottomrule
%     \end{tabular}}%
%   \label{tab:complexity_combined}%
% \end{table}%



\begin{table}[htbp]
\centering
\caption{Comparisons of complexity and model sizes for SNR and PAFDR. FLOPs: the number of FLoating-point OPerations; Params: the number of parameters. The $\Delta$ shows the percentage increase relative to the ResNet-50 baseline.}
\setlength{\tabcolsep}{7mm}
\begin{tabular}{l|cc}
\toprule
& FLOPs & Params \\
\midrule
ResNet-50 & 3.87G & 24.56M \\
% \midrule
% ResNet-50-SNR & 4.08G & 25.12M \\
% $\Delta$ & +5.10\% & +2.20\% \\
% \midrule
ResNet-50-PAFDR & 4.29G & 25.13M \\
$\Delta$ & +10.85\% & +2.32\% \\
\bottomrule
\end{tabular}
\label{tab:complexity_pafdr}
\end{table}


\section{Implementation Details }
\subsection{Batch Normalization and Instance Normalization.}
The Batch Normalization process for input features is given by:
\begin{equation}
\begin{aligned}
BN(F)=\gamma_{bn}\cdot\frac{F-\mu_{bn}(F)}{\sqrt{\sigma_{bn}{}^{2}(F)+\varepsilon_{bn}}}+\beta_{bn},
\end{aligned}
\end{equation}
where $\gamma_{bn}\in\mathbb{R}^{c}$ and $\beta_{bn}\in\mathbb{R}^{c}$ are learnable scale and shift parameters, respectively, and $\varepsilon_{bn}>0$ is a small positive constant to prevent division by zero. $\mu_{bn}\in\mathbb{R}^{c}$ and $\sigma_{bn}\in\mathbb{R}^{c}$ represent the mean and standard deviation calculated over the mini-batch data, respectively.

The Instance Normalization layer normalizes features as follows:
\begin{equation}
\begin{aligned}
\mathrm{IN}(F)=\gamma_{in}\cdot\frac{F-\mu_{in}(F)}{\sqrt{\sigma_{in}{}^{2}(F)+\varepsilon_{in}}}+\beta_{in},
\end{aligned}
\end{equation}
where $\gamma_{in}\in\mathbb{R}^{c}$ and $\beta_{in}\in\mathbb{R}^{c}$ are learnable scale and shift parameters, respectively, and $\varepsilon_{in}>0$ is a small positive constant to prevent division by zero. Unlike Batch Normalization, the mean $\mu_{in}$ and standard deviation $\sigma_{in}$ are calculated for each individual sample.







\subsection{Training Details for Domain Generalization.}

\subsubsection{\textbf{Domain Generalization}}
In our domain generalization (DG) experiments, we employed two mainstream benchmark datasets: PACS [5] and Office Home [77]. Each dataset contains four distinct domains, with PACS covering 7 categories and Office Home encompassing 65 categories. We adopted a cross-domain training/testing split strategy consistent with Jin et al. [4]. In our experiments, we selected RESNET18 as the base network architecture and incorporated PAFDR components after each convolutional module to enhance cross-domain feature extraction capabilities. During the training process, we set the maximum iteration epochs to 40, with an initial learning rate of 0.002. Each batch contained 30 images from various source domains (10 per domain), and we utilized the SGD algorithm for model optimization.

\subsubsection{\textbf{Unsupervised Domain Adaptation}}
We selected Domainnet and Digit-5 [13] as standard evaluation datasets for UDA tasks. Domainnet is a large-scale multi-source domain adaptation dataset [15], comprising approximately 600,000 images across six domains (clipart, painting, QuickDraw, real images, and sketch), covering 345 categories. Digit-5 consists of five independent digit recognition datasets: MNIST [27] (MT), MNIST-M [28] (MM), USPS [29] (UP), SVHN [30] (SV), and SYN [28] (SYN), with each dataset treated as a separate domain. We followed the data partitioning scheme proposed by Jin et al. [4]. For Digit-5, we constructed a network architecture with three convolutional layers and two fully connected layers, embedding PAFDR modules after each convolutional layer. Similar to the experimental setup in PAFDR [4], we used M3SDA [13] as a comparison benchmark. For the Domainnet dataset, we employed Resnet101 [49] as the base architecture and added PAFDR components after various convolutional modules to achieve cross-domain feature alignment.

Regarding training parameters, we set the maximum iteration epochs to 60, with an initial learning rate of 0.005, adopting a cosine annealing strategy for learning rate adjustment. Each batch contained 64 images, and we chose SGD as the optimizer.
Evaluation Method. In both UDA and DG tasks, we used classification accuracy as the primary evaluation metric, consistent with the approach in [4].

All models are implemented in PyTorch and trained on a single 80G NVIDIA A100 GPU.







\section{Details of Datasets}
\footnotesize
\bibliographystyle{IEEEtran}
% \bibliographystyle{ieeetr} 
% \bibliography{main}
\bibliography{arxiv}



}
\end{document}\documentclass[journal]{IEEEtran}