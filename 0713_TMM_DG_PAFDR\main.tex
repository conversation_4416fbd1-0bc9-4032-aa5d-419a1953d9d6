\documentclass[journal]{IEEEtran}

\usepackage{amsmath,amsfonts}

\usepackage{algorithm}
\usepackage{array}
\usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
\usepackage{textcomp}
\usepackage{stfloats}
\usepackage{url}
\usepackage{verbatim}
\usepackage{graphicx}
\usepackage{cite}
\usepackage{multirow}
\usepackage{color}
\hyphenation{op-tical net-works semi-conduc-tor IEEE-Xplore}
\usepackage{booktabs}
\usepackage{xcolor}
\usepackage{colortbl}
% updated with editorial comments 8/9/2021

% \usepackage[numbers,sort&compress]{natbib}
% \usepackage[square,sort,comma,numbers]{natbib}

\usepackage{amsthm,amssymb}
\usepackage{mathrsfs}
\usepackage{booktabs}  
\usepackage{graphicx}

\usepackage{adjustbox}



% correct bad hyphenation here
\hyphenation{op-tical net-works semi-conduc-tor}


% Add by Xin:
\newcommand{\etal}{\textit{et al}.~}
\newcommand{\ie}{\textit{i}.\textit{e}.~}
\newcommand{\ieno}{\textit{i}.\textit{e}.}
\newcommand{\eg}{\textit{e}.\textit{g}.~}
\newcommand{\egno}{\textit{e}.\textit{g}.} %there is no space
\newcommand{\etc}{\textit{etc}.}
\newcommand{\etcno}{\textit{etc}} %there is no "."
\newcommand{\ourloss}{restitution loss }
\newcommand{\ourlossno}{restitution loss}
\newcommand{\city}    {Cityscapes}
\newcommand{\cityfog} {Foggy Cityscapes} %{\textit{Foggy Cityscapes}}
\newcommand{\kit} {KITTI} %{\textit{KITTI}}
\newcommand{\tabincell}[2]{\begin{tabular}{@{}#1@{}}#2\end{tabular}}  %lan
% \usepackage{amssymb}% http://ctan.org/pkg/amssymb
\usepackage{pifont}% http://ctan.org/pkg/pifont
\newcommand{\cmark}{\ding{51}}%
\newcommand{\xmark}{\ding{55}}%
\newcommand{\std}[1]{\scriptsize$\pm${#1}}

% Myself:
\usepackage{makecell}
\usepackage{rotating}
\usepackage{algorithm}
\usepackage{algorithmicx}
\usepackage{algpseudocode}
% \usepackage{amsmath}
\usepackage{booktabs}       % professional-quality tables
\usepackage{multirow}
% \documentclass{article}
\usepackage{amsmath}
\usepackage{accents}
%\usepackage{statex}
\usepackage{cite}
\usepackage[colorlinks,linkcolor=red]{hyperref}

\makeatletter
\def\wideubar{\underaccent{{\cc@style\underline{\mskip10mu}}}}
\def\Wideubar{\underaccent{{\cc@style\underline{\mskip8mu}}}}
\makeatother
\usepackage{colortbl}
\usepackage{graphicx}
\usepackage{float}
\usepackage{subfig}
%\usepackage{subcaption}
\usepackage{graphicx}
% \usepackage[lofdepth,lotdepth]{subfig}
\newcommand{\tablestyle}[2]{\setlength{\tabcolsep}{#1}\renewcommand{\arraystretch}{#2}\centering\footnotesize}



\begin{document}

{
\twocolumn

\title{Parallel Attention-based Asymmetric Feature Decomposition and Recovery for Domain Adaptation and Generalization}

\author{Hangyuan Yang, Yongfei Zhang\IEEEauthorrefmark{1}
        % <-this % stops a space

\thanks{This work was supported by the project of the State Key Laboratory of Software Development Environment under Grant SKLSDE-2023ZX-18.}
\thanks{Hangyuan Yang and Yongfei Zhang are with School of Computer Science and Engineering, Beihang University, Beijing 100191, China (e-mail: <EMAIL>; <EMAIL>).}
% \thanks{Shuo Zhang is with Beijing Key Laboratory of Traffic Data Analysis and Mining, School of Computer Science \& Technology, Beijing 100044, China (e-mail: <EMAIL>).}
\thanks{Corresponding author: Yongfei Zhang (<EMAIL>)}}


\markboth{IEEE TRANSACTIONS ON Multimedia}%
{Shell \MakeLowercase{\textit{et al.}}: Bare Demo of IEEEtran.cls for IEEE Journals}

% \IEEEpubid{0000--0000/00\$00.00~\copyright~2021 IEEE}
% Remember, if you use this you must call \IEEEpubidadjcol in the second
% column for its text to clear the IEEEpubid mark.

\maketitle

\begin{abstract}
% Deep learning models in computer vision frequently demonstrate excellent results in training environments, but their effectiveness substantially declines when implemented in the real world where the visual characteristics of images differ from those in the training data.
% Many computer vision models excel on training data, but their performance often degrades significantly when deployed in new environments with style discrepancies between training and testing images.
While computer vision models typically perform well on training data, their performance often degrades significantly when deployed to new environments with domain gaps between training and testing images.
% To address this issue, 
% we design a generalizable framework that is both generalizable and discriminative. In this framework, 
we propose a Parallel Attention-based Feature Decomposition and Recovery (PAFDR) module. PAFDR combines Batch Normalization (BN) and Instance Normalization (IN) to reduce the domain gap, but normalization inevitably removes discriminative information. We attempt to decompose task-relevant features from the removed information and add them back to the network to enhance discrimination. However, existing methods only focus on the channel aspect and ignore spatial decomposition, leading to incomplete spatial decomposition of task-relevant/irrelevant features. PAFDR employs parallel spatial and channel attention for a more thorough decomposition and recovery of task-relevant features.
Its parallel structure provides a regularization-like effect, improving generalization ability. 
Furthermore, existing loss functions use symmetric constraints, hindering thorough feature decomposition. We propose an Asymmetric task-relevant Feature Decomposition (ATFD) loss that applies asymmetric constraints to features to match appropriate comparison objects, promoting thorough decomposition of task-relevant/irrelevant features. 
Experimental results demonstrate that our PAFDR effectively enhances network performance in both Domain Generalization (DG) and Unsupervised Domain Adaptation (UDA).
% The decomposition-based domain generalization (DG)  methods address this problem by decomposing  features into task-relevant and task-irrelevant features. 
% However, existing decomposition-based DG  methods usually focus only on channel-based feature decomposition, neglecting spatial feature decomposition. 
% This results in incomplete decomposition of task-relevant and task-irrelevant features in space, such as the inability to fully separate  features from background features.
% To address this issue, we propose \textbf{Parallel Attention-based Feature Decomposition and Recovery (PAFDR)}, to more thoroughly decompose and recover task-relevant features, enhancing DG  performance. 
% First, we use Instance Normalization (IN) and Batch Normalization (BN) to eliminate style variations.
% However, such a process inevitably eliminates discriminative information.
% For better discriminability, we utilize \textbf{parallel spatial attention and channel attention} to jointly extract task-relevant features from the removed information, and recover them back to the normalized features to enhance the discriminability.
% To better promote the separation of task-relevant and task-irrelevant features, an \textbf{Asymmetric task-relevant Feature Decomposition (ATFD) Loss} is added to ensure that the features after task-relevant feature recovery are more discriminative than the \textbf{original features}, while the features after adding task-irrelevant features are less discriminative than the \textbf{normalized features}.
% Extensive experiments on  
% datasets at different scales demonstrate the superiority of PAFDR in generalization.
% Traditional domain generalization approaches that separate task-related from task-unrelated components typically emphasize channel-based decomposition while overlooking spatial aspects. 
% The decomposition-based domain generalization (DG) and Unsupervised domain adaptation (UDA) methods address this problem by decomposing features into task-relevant and task-irrelevant features. 
% Nevertheless, current decomposition-based DG and UDA techniques predominantly emphasize channel-wise feature decomposition, overlooking spatial feature decomposition.
% This limitation creates incomplete separation, particularly between subject and background elements.
% This limitation leads to incomplete separation, such as between the subject and background.
% Our research introduces Parallel Attention-based Feature Decomposition and Recovery (PAFDR) to comprehensively decompose and recover task-relevant features, thereby improving cross-domain performance. 
% We initially apply Batch Normalization (BN) and Instance Normalization (IN) to remove stylistic variations.
% However, this process also eliminates some discriminative information.
% To achieve more effective decomposition, we employ \textbf{parallel spatial attention and channel attention} to collaboratively extract task-related features from the removed data. Subsequently, task-related features are added to the normalized features, thereby boosting the discriminability.
% We further strengthen feature decomposition through our innovative Asymmetric task-relevant Feature Decomposition (ATFD) Loss function. It ensures that features after task-relevant recovery exhibit greater discriminability than \textbf{original features}, while features combined with task-irrelevant features demonstrate reduced discriminability compared to \textbf{normalized features}.
% The proposed Asymmetric task-relevant Feature Decomposition (ATFD) Loss function further strengthens feature decomposition by ensuring that features after task-relevant recovery exhibit greater discriminability than \textbf{original features}, while features combined with task-irrelevant features demonstrate reduced discriminability compared to \textbf{normalized features}.
% Comprehensive testing across multiple datasets of varying scales confirms PAFDR's effectiveness in achieving superior generalization performance.
% Experimental results demonstrate that our PAFDR effectively enhances network performance in both Domain Generalization (DG) and Unsupervised Domain Adaptation (UDA).



\end{abstract}

\begin{IEEEkeywords}
Discriminative and Generalizable Feature, Feature Decomposition and Recovery, Domain Generalization, Unsupervised Domain Adaptation.
\end{IEEEkeywords}

\section{Introduction}
\label{intro}
\IEEEPARstart{A}{lthough} deep learning models have exhibited excellent performance on the training data \cite{qian2018pose}, they often suffer severe performance degradation when deployed in real-world scenarios. 
This phenomenon is primarily caused by domain gaps, that is, the data discrepancy across different scenes.
Specifically, the images from different scenes show obvious differences in attributes (e.g., brightness, color, contrast). Furthermore, the complex backgrounds (e.g., buildings, vegetation and vehicles) pose additional challenges for accurately identifying the target.

\begin{figure}[t]
    \centering
    \includegraphics[width=1\columnwidth]{imgs/motivation-0625.pdf}
    \vspace{-20pt}
    \caption{
    \textbf{Observation and Motivation}. 
    %可以去掉我们的方法，baseline太low，改sota
    From left to right, input represents the input image, F represents the activation map of the output features from the backbone network (taking ResNet50 as an example), $\tilde{\mathrm{\textbf{F}}}$ represents the activation map of the features after IN and BN processing, baseline represents the output feature activation map of existing decomposition-based DG  method (taking SNR \cite{jin2021style} as an example), and PAFDR represents the output feature activation map of the PAFDR proposed in this paper.
    }
    \vspace{-17pt}
    \label{fig:feat_intro}
\end{figure}

To address the distribution discrepancies between source and target domains, commonly known as domain gap or domain shift issues, two main categories of methods have emerged in the research field: Domain Generalization (DG) and Unsupervised Domain Adaptation (UDA). Both approaches aim to overcome the challenges posed by inconsistent data distributions across domains. Specifically, Domain Generalization focuses on training a robust model using only labeled data from one or more source domains, enabling it to generalize well to unseen target domains \cite{Wang2022DGSurvey}. Unsupervised Domain Adaptation leverages both labeled source data and unlabeled target domain data to adapt the model for improved performance on the target domain \cite{Ganin2016DANN}. A common advantage of both pathways is that they eliminate the need for expensive target domain labels, which grants them significant potential for real-world applications.

% To tackle this issue, researchers have developed Unsupervised Domain Adaptation (UDA) techniques that transfer models from labeled source domains to unlabeled target domains. While UDA offers greater practicality compared to traditional supervised methods, it still relies on data collection in the target domain for model updates. Domain Generalization (DG) technology takes this a step further, showing greater potential in practical applications as the model training process is completely independent of target images.

% In response to this challenge, we focus on domain generalization  \cite{song2019generalizable,muandet2013domain,li2018learning,shankar2018generalizing}, aiming to deve`lop a method that does not require access to target scene data or model adjustment. 
% The research by \cite{zhou2019omni} demonstrates that adding an IN layer after the model's BN layer \cite{ioffe2015batch} can significantly enhance the model's domain generalization performance \cite{pan2018two}. 
The research by \cite{zhou2019omni} demonstrates that adding IN layer after the model's BN layer \cite{ioffe2015batch} can significantly reduce domain gaps while improving model generalization.
As illustrated by features $\tilde{F}$ after IN and BN processing in Figure 1, while normalization methods help reduce domain gaps, they also lead to the loss of some discriminative features \cite{choi2021meta,huang2017arbitrary,jin2021style,pan2018two,zhou2019osnet}, thereby impacting performance.

%%%%%%%%%%%%%%%%新老版本分界线
% Although supervised model performs excellently on splits of the same dataset, its performance drops sharply in unseen real-world scenarios due to domain gaps (e.g., variations in image attributes such as illumination, color, and background). An ideal ReID model needs to possess both generalization capability (to identify the same person in different environments) and discriminative capability (to distinguish different individuals with similar appearances).

% We focus on the more economical and practical Domain Generalization (DG) ReID, which aims to train a model that can generalize to unseen domains without requiring target domain data, labels, or model updates. As shown in Figure 1 (II) and (III), although the combination of Batch Normalization (BN) and Instance Normalization (IN) can significantly reduce domain gaps and improve model generalization \cite{zhou2019omni}, it inevitably leads to the loss of discriminative features \cite{choi2021meta,jin2020style,pan2018two}, thereby weakening ReID performance.

To address this issue, we attempt to extract task-relevant feature from the residual feature (i.e., the difference between the original features and the normalized features) and add it back to the network to enhance the discriminability.
However, existing decomposition methods \cite{jin2021style,zheng2021calibrated} are limited to channel-based feature decomposition.
Typically, the channel of features captures abstract semantic information such as style and category, while the spatial dimension captures details like position, shape, and structure \cite{zhou2016learning}. 
As illustrated in Figure 1 (IV), the lack of spatial decomposition in existing decomposition-based method (taking SNR \cite{jin2021style} as an example) leads to incomplete decomposition between task-relevant (such as foreground object) and task-irrelevant (such as background) features, thereby significantly degrading the model's accuracy and generalization.
% computer vision need to focus on the persons in images, yet the incomplete decomposition of person and background features significantly degrades the model's accuracy and generalization.

Furthermore, existing loss functions designed to promote the decomposition of task-relevant and task-irrelevant features often adopt a symmetric design. Specifically, existing loss functions compare features—both after the addition of task-relevant features and after the addition of task-irrelevant features—against normalized features to assess enhancements or reductions in discriminability. 
However, as shown by  Figure 1, due to the loss of discriminative information, the discriminability of the normalized feature $\tilde{F}$ (Figure 1 (III)) is weaker compared to the original feature $F$ (Figure 1 (II)).
Loss functions that rely on comparisons with weakly discriminative normalized features $\tilde{F}$ will not produce the most discriminative features, hindering the thorough decomposition of task-relevant and task-irrelevant features.

% In this paper, we are dedicated to designing a generalizable ReID framework that possesses both high generalization and discrimination capabilities. The key is to find a method to decompose task-relevant features from task-irrelevant features (such as image background and image style). 

\begin{figure*}[!t]
    \centering
    \includegraphics[width=0.98\textwidth]{imgs/overall_0614.pdf}
    \vspace{-8pt}
    \caption{
      \textbf{Overall flowchart.} 
       ($\textrm{I}$) ResNet-50 network with the integrated PAFDR module. ($\textrm{II}$) PAFDR module: Initially, BN and IN are utilized to reduce domain gaps, followed by the recovery of Task-Relevant features (indicated by the red solid line) through parallel spatial and channel attention (shown as red squares in the figure). The green dashed line branch is solely for calculating the Suppression loss and is discarded during inference. Letters $a$ to $i$ in the feature map denote spatial attention weights for different regions, and gradient yellow indicates channel attention weights. ($\textrm{III}$) ATFD Loss: (a) ATFD Loss ensures that features $\tilde{F}^{+}$ after recovering Task-Relevant features $R^{+}$ are more discriminative than original features $F$, while features $\tilde{F}^{-}$ after adding task-irrelevant features $R^{-}$ are less discriminative than normalized features $\tilde{F}$; (b) ATFD Loss consists of asymmetric Enhancement loss and Suppression loss.
            % ($\textrm{I}$) ResNet-50 with the proposed PAFDR module being plugged in after some convolutional blocks. ($\textrm{II}$) Proposed PAFDR module. The combination of BN and IN is used to reduce domain gaps, followed by the recovery of task-relevant features (marked with a solid green line). The branch with a red dashed line is only used for suppression loss and is discarded during inference. The letters $a$ - $i$ in the feature map represent the spatial attention weights of different parts, and the gradient colors represent the channel attention weights. E represents the einheits matrix ($\textrm{III}$) Asymmetric task-relevant Feature Decomposition Loss ensures that features $\tilde{F}^{+}$ (after recovering task-relevant features $R^{+}$) are more discriminative than original features $F$, while features $\tilde{F}^{-}$ (after adding task-irrelevant features $R^{-}$) are less discriminative than normalized features $\tilde{F}$. The ATFD Loss, Enhancement Loss and Suppression Loss marked with black circled letters are calculated within the PAFDR module, while the Total Loss, ID Loss and HardTriplet Loss marked with gray circled letters are calculated outside the PAFDR module.
            }
        \vspace{-12pt}
    \label{fig:intro}
\end{figure*}


In this paper, we introduce Parallel Attention-based Feature Decomposition and Recovery (PAFDR), a novel approach that simultaneously enhances network generalization and discriminative power for computer vision tasks.
Figure 2 shows our framework, in which the proposed PAFDR module is embedded. 
In PAFDR, we employ a combination of IN and BN to reduce the domain gap.
To address the loss of discriminative features caused by normalization, we aim to decompose discriminative (task-relevant) features from the residual features (the difference between original and normalized features) and reintegrate them into the normalized features, thereby significantly enhancing the discriminative power of the features (as shown by Figure 1 (V)).
To overcome the spatial limitations of existing decomposition methods in fully separating task-relevant features (e.g., the object) from task-irrelevant features (e.g., background), we employ parallel spatial and channel attention to cooperatively extract task-relevant information from residual features. Its parallel structure forces independent learning of spatial and channel attention, preventing excessive collaborative adaptation between them, thereby enabling the model to learn more generalizable feature.
To address the limitation of existing symmetric loss functions in promoting complete feature decomposition, we propose an Asymmetric task-relevant Feature Decomposition (ATFD) loss function. ATFD loss applies asymmetric constraints, ensuring that recovered task-relevant features are more discriminative than \textbf{original features}, while features combined with task-irrelevant features exhibit weaker discriminability than \textbf{normalized features}. By introducing asymmetric constraints, this function enables different features to match their most suitable comparison objects, thereby promoting the thorough decomposition of task-relevant and task-irrelevant features.
By thoroughly decomposing and recovering task-relevant features, the model's generalization and accuracy have been significantly improved.Extensive experiments demonstrate that the proposed PAFDR outperforms existing DG ReID approaches.



In summary, our main contributions are as follows:
% \vspace{-0.2cm}
\begin{itemize}

\item We innovatively reveal and address the critical issue of incomplete feature decomposition in existing decomposition-based DG and UDA methods, which is caused by their focus only on channel-wise decomposition and the use of symmetric loss functions.
\item We propose a PAFDR module, a simple yet effective plug-and-play component that enhances the generalization capability of existing neural networks.
PAFDR utilizes parallel spatial and channel attention to better decompose and recover task-relevant features from residual features back into the network. 
The parallel structure ensures independent learning of spatial and channel attention, mitigating their over-collaboration to learn more generalizable features.
\item We propose the ATFD loss, which introduces asymmetric constraints to allow different features to match their most suitable comparison targets, thereby promoting thorough decomposition of task-relevant and task-irrelevant features. 
% \item We validated the effectiveness of our proposed method by demonstrating its superior performance in DG ReID tasks compared to existing approaches.
\item The proposed PAFDR module is highly versatile and can be integrated into various network architectures to perform different visual tasks, thereby enhancing the model's generalization capability across tasks including object classification, object detection, semantic segmentation, and others. Furthermore, due to PAFDR's ability to improve both generalization and discriminative capabilities of networks, it can also effectively enhance the performance of existing Unsupervised Domain Adaptation (UDA) networks.
% We comprehensively evaluated the performance of the PAFDR method across multiple mainstream benchmarks and diverse experimental settings, with results consistently demonstrating that our approach significantly outperforms existing methods in both Domain Generalization (DG) and Unsupervised Domain Adaptation (UDA) tasks.

\end{itemize}





%%%%%%%%%%%%%%%%新老版本分界线


% \begin{figure}[t]
%  \centering
%   \includegraphics[width=0.9\linewidth]{DG-PAFDR/samples/pictures/motivation-0318.pdf}
%    \caption{Observation and Motivation. From left to right, input represents the input image, F represents the activation map of the output features from the backbone network (taking ResNet50 as an example), $\tilde{\mathrm{\textbf{F}}}$ represents the activation map of the features after IN and BN processing, baseline represents the output feature activation map of existing decomposition-based DG   method (taking PAFDR \cite{jin2021style} as an example), and PAFDR represents the output feature activation map of the PAFDR proposed in this paper.}
%    \label{fig:onecol}
% \end{figure}

% In response to this challenge, we focus on domain generalization  \cite{song2019generalizable,muandet2013domain,li2018learning,shankar2018generalizing}, aiming to develop a method that does not require access to target scene data or model adjustment. 
% % The research by \cite{zhou2019omni} demonstrates that adding an IN layer after the model's BN layer \cite{ioffe2015batch} can significantly enhance the model's domain generalization performance \cite{pan2018two}. 
% The research by \cite{zhou2019omni} demonstrates that adding an IN layer after the model's BN layer \cite{ioffe2015batch} can significantly reduce domain gaps while improving model generalization.
% As illustrated by features $\tilde{F}$ after IN and BN processing in Figure 1, while normalization methods help reduce domain gaps, they can also lead to the loss of some discriminative features \cite{huang2017arbitrary,jin2021style,pan2018two,zhou2019osnet}, thereby impacting performance.

% Decomposition-based methods aim to decompose features into task-relevant and task-irrelevant features \cite{jin2021style,eom2019learning,zheng2021calibrated}, which can address the loss of discriminative information caused by normalization during task-relevant feature extraction. 
% However, existing decomposition-based methods usually focus only on channel-based feature decomposition.
% Typically, the channel of features captures abstract semantic information such as style and category, while the spatial dimension captures details like position, shape, and structure \cite{zhou2016learning}. 
% The channel dimension of features encodes abstract semantic information like style and category, while the spatial dimensions preserve detailed characteristics such as position, shape, and structure \cite{zhou2016learning}.
% As shown by features Baseline (PAFDR \cite{jin2021style}) in Figure 1, neglecting spatial decomposition can result in incomplete decomposition of task-relevant and task-irrelevant features in space, such as the inability to fully separate  features from background features.
% We use PAFDR \cite{jin2021style}, a representative method in decomposition-based methods, as our baseline. As shown by the feature baseline (output features of PAFDR) in Figure 1, ignoring spatial decomposition leads to incomplete decomposition of task-relevant and task-irrelevant features in space, for example, the inability to fully separate features from background features.
%  needs to focus on the s in images, so the confusion between features and background features severely affects the accuracy and generalization of  models.
% The model needs to accurately identify and focus on foreground objects in images, thus incomplete separation between object and background features significantly impairs model accuracy and cross-domain generalization performance.
% Additionally, existing loss functions used for feature decomposition apply the same constraints to features with different discriminability, resulting in insufficient constraints on the feature decomposition process, which further exacerbates the incompleteness of feature decomposition.


% To address these issues, we propose PAFDR, which introduces parallel spatial attention and channel attention to achieve more thorough decomposition of  image features, and applies asymmetric constraints on features of different discriminability to promote better decomposition of task-relevant and task-irrelevant features. 
% To address these issues, we propose PAFDR, which introduces parallel spatial attention and channel attention, and applies asymmetric constraints on features of different discriminability to promote better decomposition of task-relevant and task-irrelevant features.
% Firstly, a combination of BN and IN is used to reduce domain gaps, thereby alleviating domain gaps between image features from different domains. 
% Specifically, a combination of BN and IN is used to reduce domain gaps. 
% However, this process inevitably removes some discriminative (task-relevant) features. 
% As shown by features PAFDR in Figure 1, we utilize parallel spatial attention and channel attention to jointly extract task-relevant features from the removed features and recover them to the normalized features to enhance the discriminability. 
% Because of the different discriminability of different features, we propose an asymmetric task-relevant feature decomposition loss, which imposes different constraints on features with different discriminability.
% It also requires that the features after adding task-relevant features have better discriminability, while the features after adding task-irrelevant features have worse discriminability. 
% Asymmetric task-relevant feature decomposition loss makes the decomposition of task-relevant features and task-irrelevant features more thorough.
% To better promote the separation of task-relevant and task-irrelevant features, an Asymmetric task-relevant Feature Decomposition Loss is added to ensure that the features after task-relevant feature recovery are more discriminative than the \textbf{original features}, while the features after adding task-irrelevant features are less discriminative than the \textbf{normalized features}.

% We have validated the generalization of the proposed PAFDR method on multiple widely used benchmarks and settings. Our method outperforms existing DG and UDA methods in both single-source and multi-source DG   experiments. 


% \begin{itemize}
% \item We propose a feature decomposition method based on parallel spatial and channel attention to address the problem of incomplete feature decomposition caused by lack of spatial attention. This method jointly extracts task-relevant features from the information lost during normalization and recovers them to the normalized features to enhance discriminability.
% To address the problem of incomplete decomposition caused by lack of spatial attention, we propose a feature decomposition method based on parallel spatial attention and channel attention, which jointly extracts task-relevant features from the information lost in the normalization process and recover them to the normalized features to enhance the discriminability.

% \item In view of the different discriminability of different features, we propose an asymmetric task-relevant feature decomposition loss, which imposes different constraints on features with different discriminability, making the decomposition of task-relevant features and task-irrelevant features more thorough.
% \item We propose an Asymmetric task-relevant Feature Decomposition Loss to better promote the separation of task-relevant and task-irrelevant features. This loss ensures that the features after task-relevant feature recovery are more discriminative than \textbf{the original features}, while the features after adding task-irrelevant features are less discriminative than \textbf{the normalized features}.
% To better promote the separation of task-relevant and task-irrelevant features, an Asymmetric task-relevant Feature Decomposition Loss is added to ensure that the features after task-relevant feature recovery are more discriminative than the \textbf{original features}, while the features after adding task-irrelevant features are less discriminative than the \textbf{normalized features}.
% \item We present PAFDR, a comprehensive framework for DG   that incorporates domain gap reduction through BN and IN, a dual attention mechanism combining parallel spatial and channel attention for thorough task-relevant feature extraction and recovery, and an asymmetric task-relevant feature decomposition loss to enhance feature discrimination. Extensive experiments demonstrate the framework's superior generalization capability across different scales of datasets.
% \item We propose PAFDR, a versatile module applicable to various network architectures across different vision tasks. By combining BN and IN to reduce domain gaps, employing parallel spatial and channel attention mechanisms for comprehensive feature extraction and recovery, and introducing asymmetric task-relevant feature decomposition loss to enhance feature discriminability, PAFDR not only significantly improves network generalization but also effectively boosts the performance of existing UDA networks. 
% Extensive experiments demonstrate its superior generalization capability across datasets of varying scales.

% We present PAFDR, an effective framework for domain-generalizable  that integrates BN and IN, parallel spatial and channel attention mechanisms, and an asymmetric task-related information decomposition loss. Experimental results validate the framework's superior generalization performance.
% \end{itemize}

% Extensive experimental results demonstrate that our PAFDR framework substantially enhances network generalization capability and improves upon existing unsupervised domain adaptation architectures. 

% While our previous conference paper [37] presented a method specifically designed for person re-identification, this work generalizes the approach and incorporates it into popular computer vision tasks including object classification, detection, and semantic segmentation. Additionally, we have developed asymmetric task-specific loss functions based on entropy comparison techniques that are customized for each of these visual understanding tasks.

\section{Related Work}
\label{related}
% In this section, we briefly review some Domain Generalization, Unsupervised Domain Adaptatio and eature Decomposition in vision.

\subsection{Domain Generalization (DG)}
Domain Generalization (DG) aims to learn an effective model without access to any target domain data during the training process. Mainstream DG approaches include domain alignment, meta-learning, ensemble learning, and normalization strategies. Domain alignment methods learn domain-invariant feature representations by minimizing distributional discrepancies between source domains \cite{li2018mmdaae}. Meta-learning exposes the model to simulated domain shifts during training~\cite{liu2020shape}. Ensemble learning combines predictions from multiple models to enhance generalization performance~\cite{cha2021domain}. Normalization methods improve model generalization by incorporating normalization layers into the network to reduce domain discrepancies \cite{pan2018two}. However, normalization operations are task-agnostic and inevitably lead to the loss of discriminative information, resulting in performance degradation \cite{huang2017arbitrary,pan2018two}.
In this paper, we propose a Parallel Attention-based Feature Decomposition and Recovery (PAFDR) module. First, we simultaneously apply Instance Normalization (IN) and Batch Normalization (BN) to features to enhance generalization capability. To ensure high discriminability of features, we further introduce a recovery step that adaptively extracts task-relevant features from the residual information (i.e., information removed during normalization) and reintegrates them into the network.

% \subsection{Domain Generalization (DG)}
% Domain generalization (DG) addresses generalization by enabling models trained on source domains to perform well on unseen target domains. 

% DG aims to learn an effective model without access to any target data during the training process.

% Key DG methods include domain alignment, meta-learning, data augmentation, ensemble learning, regularization, and feature decomposition.
% \textbf{Domain Alignment} learns domain-invariant representations by minimizing distribution divergence among source domains \cite{li2018mmdaae}. 
% \textbf{Meta-Learning} exposes models to simulated domain shifts during training. Li et al. \cite{li2018learning} introduced a framework dividing source domains into meta-source and meta-target sets. Extensions include meta-learning regularization parameters or normalization layers \cite{liu2020shape}.
% \textbf{Data Augmentation} enhances robustness by simulating domain shifts through transformations, such as adversarial augmentations \cite{volpi2018generalizing}, and style transfer \cite{zhou2021stylematch}.
% \textbf{Ensemble Learning} combines predictions from multiple models for improved generalization, including domain-specific networks \cite{zhou2020domain} and weight averaging \cite{cha2021domain}.
% \textbf{Regularization} introduces heuristics to promote generalizable features, such as suppressing local patch reliance \cite{wang2019learning} or masking dominant features \cite{huang2020self}, which can be combined with other DG methods \cite{zhou2020learning}.
% \textbf{Feature Decomposition} separates domain-specific and domain-agnostic features. Decomposition-based methods \cite{li2017deeper} and generative models like variational autoencoders \cite{ilse2019diva} are typical approaches, but require domain labels.
% However, existing decomposition methods typically focus only on channel-wise feature decomposition, neglecting the spatial dimension, which leads to incomplete feature decomposition.



% \subsection{Domain Generalization (DG)}
% Domain generalization (DG) addresses generalization by enabling models trained on source domains to perform well on unseen target domains. Key DG methods include domain alignment, meta-learning, data augmentation, ensemble learning, self-supervised learning, disentangled representation learning, regularization, and reinforcement learning.

% Domain Alignment learns domain-invariant representations by minimizing distribution divergence among source domains \cite{li2018mmdaae,li2018ciddg}, inspired by domain adaptation theory \cite{ben2010theory}. While effective, generalizing to unseen domains remains challenging.

% Meta-Learning exposes models to simulated domain shifts during training. Li et al. \cite{li2018learning} introduced a framework dividing source domains into meta-source and meta-target sets. Extensions include meta-learning regularization parameters or normalization layers \cite{balaji2018metareg,du2020learning,liu2020shape}. These methods primarily serve multi-source DG and rely on domain labels \cite{zhou2020learning}.

% Data Augmentation enhances robustness by simulating domain shifts through transformations, such as random flips and color changes \cite{otalora2019staining,chen2020improving}, adversarial augmentations \cite{volpi2018generalizing,shankar2018generalizing}, RandConv \cite{xu2021robust}, style transfer \cite{zhou2021stylematch}, and feature-level mixing \cite{zhou2021mixstyle,zhou2020learning}.

% Ensemble Learning combines predictions from multiple models for improved generalization. Approaches include exemplar-SVMs \cite{xu2014exploiting,malisiewicz2011ensemble}, domain-specific networks \cite{ding2017deep,zhou2020domain}, batch normalization layers \cite{seo2020learning,mancini2018robust}, and weight averaging \cite{cha2021domain}.

% Self-Supervised Learning uses pretext tasks for learning general features. Examples include Jigsaw puzzles \cite{cvpr19jigen}, rotation prediction \cite{bucci2020self}, and combinations thereof. These methods do not require domain labels but depend on the choice of pretext tasks \cite{jing2020self}.



% Regularization introduces heuristics to promote generalizable features, such as suppressing local patch reliance \cite{wang2019learning} or masking dominant features \cite{huang2020self}. These can be combined with other DG methods \cite{motiian2017unified,zhou2020learning}.

% Reinforcement Learning (RL) handles domain shift through data augmentation \cite{laskin2020reinforcement,tobin2017domain} and self-supervised losses \cite{yarats2021improving,laskin2020curl}, requiring specific designs for non-visual shifts \cite{kirk2021survey}

% Feature Decomposition separates domain-specific and domain-agnostic features. Decomposition-based methods \cite{khosla2012undoing,li2017deeper} and generative models like variational autoencoders \cite{ilse2019diva} are typical, but require domain labels.








% DG methods aim to apply source-trained models to unseen target domains without accessing target data or model modification. Current DG approaches mainly include ensemble-based, meta-learning-based, normalization-based, and feature decomposition-based methods. 
% Ensemble-based methods utilize multiple expert models for domain-specific feature extraction \cite{dai2021generalizable,lin2021domain,xu2022mimic}; meta-learning-based methods enhance generalization by simulating domain transfer during training \cite{choi2021meta,song2019generalizable,zhao2021learning}; normalization-based methods employ techniques like BN and IN to reduce domain discrepancy \cite{chen2023cluster,jiao2022dynamically}; feature decomposition-based methods \cite{eom2019learning,zhang2022learning,jin2021style} separate task-relevant and irrelevant features, but often only from the channel perspective, neglecting spatial decomposition and resulting in incomplete disentanglement.

% Domain alignment, a common DG strategy, learns domain-invariant representations by minimizing differences among source domains. Muandet et al.~\cite{muandet2013domain}. initiated this by aligning marginal distributions, followed by works aligning class-conditional ~\cite{li2018ciddg, yang2014learn} or posterior distributions [172] for richer data structure capture. Wang et al. [172] proposed hypothesis-invariant representations via intra-class posterior alignment using KL divergence. Other alignment techniques include statistical moment matching [165,167], adversarial learning [170,171], and kernel methods [19,176]. 

% Meta-learning simulates domain shifts to enable fast adaptation; Li et al. [33] applied MAML to DG, and Balaji et al. [34] introduced meta-regularization. Data augmentation increases training diversity and robustness, as in the domain-adversarial image generation of Zhou et al. [35] and cross-gradient training by Shankar et al. [36]. 

% Ensemble learning combines multiple models for better generalization, exemplified by Mancini et al. [208] with source-specific networks and gating. Self-supervised learning leverages auxiliary tasks for generalizable features, such as jigsaw puzzles \cite{49} and contrastive learning. Feature disentanglement separates domain-specific from domain-invariant features [189]. Invariant Risk Minimization (IRM) by Arjovsky et al. [127] aims to learn invariances across environments, representing another key DG direction.





\subsection{Unsupervised Domain Adaptation (UDA)}
% Unsupervised Domain Adaptation (UDA) aims to address performance degradation in object detection caused by distribution shifts between source and target domains. Recent approaches can be categorized into six main strategies.
Unsupervised Domain Adaptation (UDA) is a transfer learning approach for target domains lacking annotations, which utilizes labeled source domain data and unlabeled target domain data for model training.
Mainstream UDA approaches include Pseudo-Label Self-Training, Adversarial Feature Learning, and domain alignment.
Pseudo-Label Self-Training generates pseudo-labels on target domains for supervision \cite{khodabandeh2019robust}.
% Khodabandeh et al. \cite{khodabandeh2019robust} proposed robust learning with high-confidence pseudo-labels, while Kim et al. \cite{kim2019self} applied self-training to single-stage detectors. 
Adversarial Feature Learning aligns source and target domains in feature space \cite{chen2018domain}. 
% DA-Faster RCNN by Chen et al. \cite{chen2018domain} introduced domain classifiers with adversarial training, while SWDA by Saito et al. \cite{saito2019strong} enhanced alignment through global and local consistency. 
% Image-to-Image Translation reduces domain gaps through style transfer. Zhang et al. \cite{zhang2019cycle} used CycleGAN with pseudo-label training, and Chen et al. \cite{chen2020harmonizing} integrated translation with adversarial learning. These methods depend on translation quality and often combine with other strategies.
% Graph Reasoning and Domain Randomization provide alternative approaches. Xu et al. \cite{xu2020cross} used graph neural networks to model object relationships, while Kim et al. \cite{kim2019diversify} proposed source style randomization. Mean Teacher Training employs teacher-student frameworks, as demonstrated by Deng et al. \cite{deng2021unbiased}.
\textbf{Domain Alignment} learns domain-invariant representations by minimizing distribution divergence among source domains \cite{peng2019moment}. 
% \cite{li2018mmdaae}. M3SDA \cite{peng2019moment} minimizes the moment distance among the source and target domains and per-domain classifier is used and optimized as in MCD to enhance the alignment.

Our proposed DAFDR module is designed to improve generalization performance while maintaining discriminative power, thereby strengthening current UDA methods.



% \subsection{Unsupervised Domain Adaptation (UDA)}

% Unsupervised Domain Adaptation (UDA) aims to address performance degradation in object detection caused by distribution shifts between source and target domains. Recent years have seen significant progress in UDA for object detection, with six main approaches: adversarial feature learning, pseudo-label self-training, graph reasoning, image-to-image translation, domain randomization, and mean teacher training.

% Adversarial Feature Learning aligns source and target domain distributions in feature space. DA-Faster RCNN by Chen et al. \cite{chen2018domain} introduced domain classifiers into the Faster-RCNN framework, using adversarial training to learn domain-invariant features and improve target performance. SWDA by Saito et al. \cite{saito2019strong} enhanced feature alignment by focusing on both global and local consistency, achieving strong results on benchmarks like Cityscapes to FoggyCityscapes. Graph-induced prototype alignment by Xu et al. \cite{xu2020cross} improved semantic consistency via graph structures, while Every Pixel Matters by Hsu et al. \cite{hsu2020every} used pixel-wise adversarial alignment. These methods are powerful for feature alignment but face challenges in stability and hyperparameter tuning.

% Pseudo-Label Self-Training generates pseudo-labels on the target domain to provide supervision. Khodabandeh et al. \cite{khodabandeh2019robust} proposed robust learning with high-confidence pseudo-labels and iterative self-training to reduce label noise. Kim et al. \cite{kim2019self} applied self-training to single-stage detectors like SSD, optimizing target performance with pseudo-labels. These methods are stable but require careful handling of noisy pseudo-labels.

% Image-to-Image Translation narrows domain gaps by translating source images to target styles. Zhang et al. \cite{zhang2019cycle} used CycleGAN for style transfer and combined it with pseudo-label training. Chen et al. \cite{chen2020harmonizing} integrated image translation and adversarial feature learning, while Hsu et al. \cite{hsu2020progressive} used progressive adaptation with multi-stage translation and feature alignment. These methods depend on the mapping ability of the translation module and often combine with other strategies for robustness.

% Graph Reasoning enhances cross-domain knowledge transfer by modeling object relationships. Xu et al. \cite{xu2020cross} used graph neural networks to capture and transfer object relations for better feature alignment.

% Domain Randomization removes style bias by generating diversified source data. Kim et al. \cite{kim2019diversify} proposed randomizing source styles with adversarial training to improve generalization.

% Mean Teacher Training uses a teacher-student framework for adaptation. Deng et al. \cite{deng2021unbiased} combined image translation and adversarial feature learning with mean teacher updates, improving performance from Cityscapes to FoggyCityscapes. Cai et al. \cite{cai2019exploring} extended the mean teacher framework with feature matching.






% Domain Invariant Feature Learning. This method generates domain-invariant features effective for target domains through adversarial training and domain discriminators (such as gradient reversal layers). Representative papers include: [26] Chen et al. "Faster-RCNN in the wild" published at CVPR 2018, which first applied domain-invariant feature learning to the Faster-RCNN framework; [58] Saito et al.'s "Strong weak distribution alignment" proposed at CVPR 2019, which improved detection performance through strong-weak distribution alignment; additionally, [72], [73], [74] also adopted similar strategies.

% Pseudo-Label-Based Self-Training This method utilizes high-confidence predictions of models trained on source domains as pseudo-labels for target domains, gradually improving the model. Key papers include: [99] Inoue et al.'s "Cross-domain weakly supervised adaptation" proposed at CVPR 2018, combining weakly supervised information for self-training; [62] Chen et al.'s "Automatic adaptation from unlabeled videos" at CVPR 2019, utilizing video data for adaptation; and [63], [102] which further extended this method.

% Image-to-Image Translation This approach reduces visual domain differences by mapping target domain images to source domain styles through unpaired image translation techniques. Representative works include: [65] and [104] which proposed adaptation methods based on cycle consistency; [105], [106] also explored similar image translation strategies to improve detector performance.

% Domain Randomization This method trains detectors using source domain data stylized in various ways to eliminate source domain style bias and enhance generalization capabilities. Key papers include: [60] Kim et al.'s "Diversify and Match" proposed at CVPR 2019, combining randomization with adversarial learning; [106] also adopted similar methods.
% Mean-Teacher Training This approach improves model generalization using unlabeled target domain data through a student-teacher framework. Representative papers include: [69] and [101], where [101] Roychowdhury et al.'s "Mean teacher with object relations" proposed at CVPR 2019 further optimized this method by incorporating object relations.

% Graph Reasoning This method captures object relationships in the source domain through graph models and transfers them to the target domain. Key papers include: [100] Xu et al.'s "Cross-domain detection via graph-induced prototype alignment" at CVPR 2020 and [101], enhancing adaptation effects using graph structures.
% To solve this generalization problem, domain adaptation (DA) technology has gradually become a research focus in this field.

% \subsection{Unsupervised Domain Adaptation (UDA)  }
% UDA   aims to adapt the model trained on the labeled source domain to the unlabeled target domain. Common approaches include pseudo-label estimation \cite{rami2022online,rami2024source}, intermediate feature alignment \cite{fu2019self,wang2018transferable}, and generative adversarial networks (GAN)-based style transfer \cite{li2019cross,liu2019adaptive}. Pseudo-label estimation: Use the source domain labels to initially train the model, and generate pseudo-labels in the target domain \cite{rami2022online,rami2024source}. Intermediate feature alignment: Reduce the domain gap by aligning the intermediate features \cite{fu2019self,huang2020domain,wang2020smoothing}. GAN-based style transfer: GAN is used to bridge the visual gap \cite{li2019cross,liu2019adaptive,pang2022cross,zheng2019joint}. Although UDA  alleviates the burden of manual annotation to some extent by utilizing unlabeled data in the target domain, it still cannot avoid the data collection and model retraining process in each new environment, so domain generalization  technology has received increasing attention in recent years.



\subsection{Feature Decomposition}
Feature decomposition enhances model generalization by separating original features into multiple independent components, effectively filtering out task-irrelevant redundant elements while retaining key discriminative features.
% Feature Decomposition enhances generalization by separating generative factors into semantically meaningful representations. 
 Variational Autoencoders (VAEs) use variational inference to learn disentangled latent spaces \cite{higgins2016beta}. 
 % The original VAE \cite{kingma2013auto} showed unsupervised disentanglement on simple datasets. Enhancements like $\beta$-VAE \cite{higgins2016beta} emphasize KL-divergence for independent latent variables, while Factor-VAE \cite{kim2018disentangling} and $\beta$-TCVAE \cite{chen2019isolating} introduce additional regularization for improved factor isolation.
Generative Adversarial Networks (GANs) adapt adversarial training for disentanglement \cite{wu2021stylespace}.
% InfoGAN \cite{chen2016infogan} maximizes mutual information between latent codes and outputs for unsupervised factor separation. StyleSpace \cite{wu2021stylespace} manipulates latent dimensions for stylistic control, achieving effective coarse-grained disentanglement \cite{Lee_2018_ECCV}.
% \textbf{Causal and Group Theory-based Methods:} Causal inference provides rigorous frameworks for disentanglement. Suter et al. \cite{suter2019robustly} use structural causal models (SCM) to account for confounders, while group theory-based methods \cite{higgins2018towards} define disentanglement through symmetry groups, ensuring equivariant mappings \cite{ yang2021towards}. These approaches improve generalization by aligning representations with intrinsic data structures.
Jin et al. utilize channel attention mechanisms to extract task-relevant discriminative information for enhancing model generalization \cite{jin2021style}.
However, existing decomposition methods typically focus only on channel-wise feature decomposition, neglecting the spatial dimension, which leads to incomplete feature decomposition.
In this paper, To extract and recover discriminative information, we employ parallel spatial and channel attention mechanisms to jointly separate task-relevant features from the discarded residual features. To promote more effective feature separation, we design an asymmetric task-relevant feature decoupling loss function that ensures the recovered features exhibit stronger discriminative power compared to the original features. The core idea is to make the class probability distributions of the recovered features more distinct and clear, thereby reducing sample uncertainty.



% \subsection{Feature Decomposition}
% Feature Decomposition enhances generalization by separating generative factors into semantically meaningful representations. Key methods include:

% VAE-based Methods: Variational Autoencoders (VAEs) use variational inference to learn disentangled latent spaces. The original VAE \cite{kingma2013auto} showed unsupervised disentanglement on simple datasets. Enhancements like $\beta$-VAE \cite{higgins2016beta} emphasize KL-divergence for independent latent variables, while Factor-VAE \cite{kim2018disentangling} and $\beta$-TCVAE \cite{chen2019isolating} introduce additional regularization for improved factor isolation. These methods achieve dimension-wise disentanglement, aiding generalization in image generation and classification \cite{burgess2018understanding, kumar2018variational, kim2019relevance, dupont2018learning}.

% GAN-based Methods: Generative Adversarial Networks (GANs) adapt adversarial training for disentanglement. InfoGAN \cite{chen2016infogan} maximizes mutual information between latent codes and outputs for unsupervised factor separation. Other works \cite{larsen2016autoencoding, zhu2018visual} use adversarial objectives for disentangling task-relevant features, while StyleSpace \cite{wu2021stylespace} manipulates latent dimensions for stylistic control. These methods are effective for coarse-grained disentanglement and improving generalization in image tasks \cite{gonzalez2018image, Lee_2018_ECCV, Lee_2021_CVPR, Liu_2021_CVPR}.

% Causal and Group Theory-based Methods: Causal inference and group theory provide rigorous frameworks for disentanglement. Suter et al. \cite{suter2019robustly} use structural causal models (SCM) to account for confounders, while Yang et al. \cite{Yang_2021_CVPR} and Shen et al. \cite{shen2020disentangled} model factor interactions via causal relationships. Group theory-based methods \cite{higgins2018towards} define disentanglement through symmetry groups, ensuring equivariant mappings, as in \cite{caselles2019symmetry, quessard2020learning, yang2021towards, wang2021self}. These approaches improve generalization by aligning representations with intrinsic data structures.



% VAE-based disentangled representation learning methods are widely used in computer vision for image generation, editing, and feature separation. Vanilla VAE [16] learns basic disentangled representations on simple datasets like MNIST [44]. $\beta$-VAE [6] introduces a $\beta$ parameter to better balance reconstruction and disentanglement, improving attribute separation in images. DIP-VAE [35] and FactorVAE [7] further enhance disentanglement via different regularizations, producing high-quality representations for demanding tasks. $\beta$-TCVAE [5] decomposes KL divergence for finer disentanglement while maintaining reconstruction quality, suitable for fine-grained image generation. RF-VAE [33] and JointVAE [32] target meaningful factors and mixed discrete-continuous representations, useful for controlling specific image attributes. Group theory-based VAE methods [18,39,40,41] provide mathematical support for disentanglement, especially for tasks with symmetry or geometric constraints.

% GAN-based disentangled representation learning excels at high-quality image generation and transformation. Vanilla GAN [17] enables realistic image synthesis via adversarial training, foundational for natural scene and face generation. InfoGAN [9] maximizes mutual information between latent codes and images, allowing controllable attribute generation (e.g., pose, expression, color), which benefits interactive editing and personalized generation tasks.

% Causal disentanglement methods address complex scenarios with confounding factors by modeling causal relationships between generative factors. Such methods [14,11,43] improve robustness and generalization, notably in image classification tasks requiring separation of background and foreground or handling multiple variable influences.

\section{Method}

% We are dedicated to designing a DG ReID framework that combines both high generalization and discriminative capabilities. The trained model can be directly deployed to unseen domains while maintaining good performance. Figure 2 illustrates the overall workflow of our framework. Specifically, we propose a Parallel Attention-based Feature Decomposition and Recovery (PAFDR) module aimed at enhancing the generalization ability and recognition accuracy of ReID models in unseen domains.
% As shown in Figure 2 (\textrm{I}), we add the PAFDR module after each convolution block in ResNet-50.

% While normalization can reduce differences between samples and enhance network generalization, it inevitably removes some discriminative information. To address this issue, we attempt to decompose and recover Task-Relevant (discriminative) features from those previously discarded by normalization to enhance feature discriminability. However, existing decomposition-based DG ReID methods focus only on channel-dimension feature decomposition while neglecting spatial-dimension feature decomposition, resulting in incomplete spatial feature decomposition (such as  object and background). In this section, as shown in Figure 2 (\textrm{II}), we propose a Parallel Attention-based Feature Decomposition and Recovery (PAFDR) module. PAFDR employs parallel spatial attention and channel attention to more thoroughly decompose and recover Task-Relevant (discriminative) features from those previously discarded by normalization. 
% Its parallel structure forces independent learning of spatial and channel attention, preventing excessive collaborative adaptation between them, thereby enabling the model to learn more generalizable feature.

% For the PAFDR module, the input feature is denoted as $F\in\mathbb{R}^{h\times w\times c}$ and the output is $\tilde{F}^{+}\in\mathbb{R}^{h\times w\times c}$, where $h$, $w$, and $c$ represent the height, width, and number of channels, respectively.

% Taking the widely-used ResNet-50 network as an example (see Figure 2 (\textrm{I})), PAFDR modules are added after each convolution block.


% We are committed to developing a generalizable and stable method. During the model training phase, one or multiple well-annotated source datasets can be utilized for learning. The trained model will be directly applied to new domains or datasets without additional adjustments, demonstrating excellent generalization capabilities.

% Figure 2 illustrates the overall framework of our proposed Parallel Attention-based Feature Decomposition and Recovery (PAFDR).
% , designed to enhance the generalization performance and recognition accuracy of models in unseen domains.
% This module can be seamlessly integrated into existing  systems, significantly enhancing model performance and recognition accuracy in unseen domains. 
% Using ResNet-50 as the backbone network (see Figure 2), we introduce PAFDR modules after each convolutional block.

% In this section, we provide a detailed explanation of PAFDR. This is a cohesive framework that combines: 3.2. Parallel Attention-based Feature Decomposition and Recovery, 3.3. Asymmetric task-relevant Feature Decomposition Loss.

% The PAFDR module boasts excellent compatibility, allowing for easy integration into existing networks. For instance, we applied it to the widely used ResNet-50 \cite{He2015DeepRL}  network (as shown in Figure 2 ($\textrm{I}$)), simply by adding the PAFDR module after each convolutional block.
% In this module, the domain gaps between different features are first reduced through a combination of BN and IN. Then, we introduce a recovery step to extract task-relevant features from the information lost during normalization, and add these features back to the normalized features to enhance the discriminability. Furthermore, we design an asymmetric task-relevant feature decomposition loss to guide the spatial attention and channel attention modules to extract task-relevant features from the features lost during the normalization process.


We have designed a novel module named Parallel Attention-based Feature Decomposition and Recovery (PAFDR). The primary goal of this module is to enhance the generalization capability of deep learning models across different data domains while ensuring their core discriminative power for identifying critical information remains intact. This effectively supports applications in Domain Generalization (DG) and Domain Adaptation (DA). Please refer to Figure 2 for the overall architecture of the module. Notably, PAFDR is engineered as a plug-and-play component, allowing for straightforward integration into various existing network structures (such as those for classification, detection, and segmentation). Taking the widely-used ResNet-50 \cite{he2016deep} network as an example (illustrated in Figure 2(a)), PAFDR modules can be deployed after each convolutional block.

In the PAFDR module, domain gaps are first narrowed by combining BN and IN. Subsequently, the recovery step utilizes parallel spatial and channel attention to extract critical Task-Relevant (discriminative) features from information lost during the normalization process and adds them back to the normalized features to enhance discriminability. 
Its parallel structure forces independent learning of spatial and channel attention, preventing excessive collaborative adaptation between them, thereby enabling the model to learn more generalizable feature.
% Its parallel structure provides a regularization-like effect, further improving the model's generalization.
Moreover, for the PAFDR module, we design an Asymmetric Task-Relevant Feature Decomposition (ATFD) loss, which introduces asymmetric constraints to allow different features to match their most suitable comparison targets, thereby promoting thorough decomposition of Task-Relevant and task-irrelevant features.

% Internally within the PAFDR module (as shown in Figure 2(b)), we denote the input feature map as $F \in \mathbb{R}^{H \times W \times C}$ and the processed output feature map as $F' \in \mathbb{R}^{H \times W \times C}$, where $H$, $W$, and $C$ represent the height, width, and number of channels of the feature map, respectively. The module initially utilizes Batch Normalization (BN) and Instance Normalization (IN) techniques to eliminate style discrepancies present between samples or instances.

% Subsequently, we introduce a distinctive "recovery" step. This step aims to refine task-critical (discriminative) features from the residual information generated during the normalization process (i.e., the difference between the original features $F$ and the style-normalized features $\tilde{F}$). These refined features are then supplemented back into the normalized features $\tilde{F}$. Furthermore, to better decouple task-relevant features from task-irrelevant ones within the PAFDR module, we have specifically designed an asymmetric task-relevant feature decoupling loss function (the mechanism is detailed in Figure 2(c)).

PAFDR demonstrates good versatility and can be adapted to different network architectures to serve multiple tasks. We provide detailed illustrations of its application in object classification,
% and person re-identification tasks, 
and analyze its potential utility in object detection and semantic segmentation (noting that the specific form of the asymmetric feature decoupling loss may be slightly adjusted for different tasks). Moreover, given that PAFDR simultaneously enhances both the generalization and discriminative capabilities of the network—qualities that are also crucial for Unsupervised Domain Adaptation (UDA)—this module also holds the potential to improve the performance of existing UDA methods.

\subsection{Parallel Attention-based Feature Decomposition and Recovery (PAFDR)}
\label{Hypergraph Construction}
Real-world images from different scenes or datasets exhibit domain gaps (such as differences in image backgrounds and properties), which significantly impair the generalization capability of computer vision models.
% Person images captured in different scenarios exhibit variations in properties and backgrounds. Domain gaps between source and target domains typically hinder the generalization ability of ReID models. 
While normalization can reduce differences between samples and enhance network generalization, it inevitably removes some discriminative information. To address this issue, we attempt to decompose and recover Task-Relevant (discriminative) features from those previously discarded by normalization to enhance feature discriminability. However, existing decomposition-based DG and UDA methods focus only on channel-dimension feature decomposition while neglecting spatial-dimension feature decomposition, resulting in incomplete spatial feature decomposition (such as  object and background). In this section, as shown in Figure 2 (\textrm{II}), we propose a Parallel Attention-based Feature Decomposition and Recovery (PAFDR) module. PAFDR employs parallel spatial attention and channel attention to more thoroughly decompose and recover Task-Relevant (discriminative) features from those previously discarded by normalization. 
Its parallel structure forces independent learning of spatial and channel attention, preventing excessive collaborative adaptation between them, thereby enabling the model to learn more generalizable feature.
% Its parallel structure provides a regularization-like effect, further improving the model's generalization. Specifically, the parallel structure forces spatial attention and channel attention to learn independently, preventing excessive co-adaptation between them, thereby enabling the model to learn more robust and generalizable feature representations. 
We now describe the proposed PAFDR module.


% Person images used for ReID from different scenes or datasets exhibit domain gaps (such as differences in image backgrounds and properties), which significantly impair the generalization capability of ReID models.

% Most prior decomposition-based DG  methods usually focus only on channel-based feature decomposition, neglecting spatial feature decomposition, which results in incomplete feature decomposition. In this section, as shown in Figure 2 ($\textrm{II}$), we introduce parallel spatial attention and channel attention to facilitate a more comprehensive decomposition of task-relevant features and task-irrelevant features from residual features. 

For the PAFDR module, the input feature is denoted as $F\in\mathbb{R}^{h\times w\times c}$ and the output is $\tilde{F}^{+}\in\mathbb{R}^{h\times w\times c}$, where $h$, $w$, and $c$ represent the height, width, and number of channels, respectively.

\subsubsection{\textbf{Domain Gaps Reduction via BN and IN.}}
In PAFDR, we reduce domain gaps between input features through a combination of BN and IN:
\begin{equation}
\begin{aligned}
\tilde{F}=IN(BN(F)),
\end{aligned}
\end{equation}
where $\tilde{F}$ represents the normalized feature. For BN and IN details, please refer to Supplementary Material.
% Reference \cite{ioffe2015batch,ulyanov2016instance}.



% \subsubsection{\textbf{Domain Gaps Reduction via BN and IN}} 
% In PAFDR, we reduce domain gaps between input features through a combination of Batch Normalization and Instance Normalization:
% %\begin{equation}
% %\begin{aligned}
% %\mathcal{L}_{\mathrm{con}} = \frac{1}{2n} \sum_{i=1}^{n} \big[l(\mathbf{p}_l^i, \mathbf{p}_a^i) + l(\mathbf{p}_a^i, \mathbf{p}_l^i) \big], \\
% %l(\mathbf{p}_l^i, \mathbf{p}_a^i) = - \log \frac{\exp \big\{ [r_3 - d(\mathbf{p}_l^i, \mathbf{p}_a^i)] / \tau_3\big\}}{\sum_{k=1}^n \exp\big\{[r_3 - d(\mathbf{p}_l^i, \mathbf{p}_a^k) ] / \tau_3\big\} },
% %\end{aligned}
% %\end{equation}
% \begin{equation}
% \begin{aligned}
% \tilde{F}=IN(BN(F)),
% \end{aligned}
% \end{equation}
% where the input feature is denoted as $F\in\mathbb{R}^{h\times w\times c}$, with dimensions ${h\times w\times c}$ (height, width, and number of channels). The input feature is first processed by Batch Normalization, and the resulting feature is further processed by Instance Normalization to obtain the final feature $\tilde{F}\in\mathbb{R}^{h\times w\times c}$.

% The Batch Normalization process for input features is given by:
% \begin{equation}
% \begin{aligned}
% BN(F)=\gamma_{bn}\cdot\frac{F-\mu_{bn}(F)}{\sqrt{\sigma_{bn}{}^{2}(F)+\varepsilon_{bn}}}+\beta_{bn},
% \end{aligned}
% \end{equation}
% where $\gamma_{bn}\in\mathbb{R}^{c}$ and $\beta_{bn}\in\mathbb{R}^{c}$ are learnable scale and shift parameters, respectively, and $\varepsilon_{bn}>0$ is a small positive constant to prevent division by zero. $\mu_{bn}\in\mathbb{R}^{c}$ and $\sigma_{bn}\in\mathbb{R}^{c}$ represent the mean and standard deviation calculated over the mini-batch data, respectively.

% The Instance Normalization layer normalizes features as follows:
% \begin{equation}
% \begin{aligned}
% \mathrm{IN}(F)=\gamma_{in}\cdot\frac{F-\mu_{in}(F)}{\sqrt{\sigma_{in}{}^{2}(F)+\varepsilon_{in}}}+\beta_{in},
% \end{aligned}
% \end{equation}
% where $\gamma_{in}\in\mathbb{R}^{c}$ and $\beta_{in}\in\mathbb{R}^{c}$ are learnable scale and shift parameters, respectively, and $\varepsilon_{in}>0$ is a small positive constant to prevent division by zero. Unlike Batch Normalization, the mean $\mu_{in}$ and standard deviation $\sigma_{in}$ are calculated for each individual sample.

\subsubsection{\textbf{Feature Decomposition and Recovery via parallel spatial and channel attentions}} 
Although the combination of BN and IN effectively reduces domain gaps and enhances generalization, it purely mathematical operation inevitably loses some discriminative information. To compensate for this deficiency, we design a mechanism to extract task-relevant features from the residual $R$ and compensate them back to the normalized features to enhance the discriminability. The residual feature $R$ is calculated as follows:
\begin{equation}
\begin{aligned}
R=F-\tilde{F},
\end{aligned}
\end{equation}
This value reflects the difference between the input feature $F$ and the normalized feature $\tilde{F}$, i.e., the information discarded by the normalization process.



% \subsection{Spatial-Channel Attention Weights for task-Relevant and Irrelevant Features}


% Consequently, the residual features are decomposed into two components: task-relevant features $R^{+}$ and task-irrelevant features $R^{-}$.

% \subsubsection{Spatial Attention Weights for task-Relevant and Irrelevant Features}
We use learnable channel-spatial weight $\mathbf{W}\in\mathbb{R}^{h\times w\times c}$ to mask the residual features $R$, decomposition it into two parts: task-relevant feature $\mathbf{R}^{+}\in\mathbb{R}^{h\times w\times c}$ and task-irrelevant features $\mathbf{R}^{-}\in\mathbb{R}^{h\times w\times c}$. The decomposition process is as follows:
\begin{equation}
\begin{aligned}
% R^{+}=R\cdot\mathbf{W}^{+}
R^{+}=\mathbf{W}\cdot R,
\end{aligned}
\end{equation}
\begin{equation}
\begin{aligned}
% R^{-}=R\cdot\mathbf{W}^{-}
R^{-}=\mathbf{(1-W)}\cdot R.
\end{aligned}
\end{equation}

% \subsubsection{Fusion of Spatial and Channel Weights for task-Relevant and Irrelevant Features}
% For a more thorough feature decomposition, we employ parallel spatial and channel attention to jointly decouple residual features, with the aim that the spatial-channel attention weights 
% To achieve a more thorough feature decomposition, we employed parallel spatial attention and channel attention to decompose the residual feature R, thereby obtaining the final weight
% $\mathbf{W}\in\mathbb{R}^{h\times w\times c}$.
In this paper, we employ parallel spatial and channel attention to decouple residual features, with the aim that the spatial-channel attention weights $\mathbf{W}\in\mathbb{R}^{h\times w\times c}$ can more thoroughly extract task-relevant features from both spatial and channel perspectives.
Specifically, the residual features $R$ first pass through spatial attention and channel attention in parallel, generating the corresponding spatial weights $\mathbf{W}_{\mathrm{spatial}}$ and channel weights $\mathbf{W}_{\mathrm{channel}}$ for task-relevant features. Then we multiply the spatial weights with the corresponding channel weights to obtain the final combined weights $\mathbf{W}$ for task-relevant features, as shown below:
\begin{equation}
\begin{aligned}
\mathbf{W}=\mathbf{W}_{\mathrm{spatial}}\cdot\mathbf{W}_{\mathrm{channel}}.
\end{aligned}
\end{equation}

% The spatial attention mechanism works in parallel with channel attention, enabling a more thorough decomposition of task-relevant and task-irrelevant features from both channel and spatial perspectives.



% We obtain the spatial attention weights $\mathbf{W}^{+}_{\mathrm{spatial}}\in\mathbb{R}^{1\times h\times w}$ for task-relevant features and $\mathbf{W}^{-}_{\mathrm{spatial}}\in\mathbb{R}^{1\times h\times w}$ for task-irrelevant features through spatial attention. By performing element-wise multiplication of these weights with the residual features $R$ along the spatial dimension, we can decompose task-relevant and task-irrelevant features from a spatial perspective.


% \begin{equation}
% \begin{aligned}
% \mathbf{W}_{\mathrm{spatial}}^{+}=\mathbf{W}_{\mathrm{spatial}}
% \end{aligned}
% \end{equation}
% \begin{equation}
% \begin{aligned}
% \mathbf{W}_{\mathrm{spatial}}^{-}=\mathbf{1}-\mathbf{W}_{\mathrm{spatial}}
% \end{aligned}
% \end{equation}

By performing element-wise multiplication of these weights with the residual features $R$ along the spatial dimension, we can decompose task-relevant and task-irrelevant features from a spatial perspective.
Spatial attention works as follows: First, max-pooling along the channel axis is applied to the residual features $R$ to obtain a ${1\times h\times w}$ single-channel tensor containing global channel information. This tensor is then convolved with a ${k\times k}$ kernel to produce spatial weights $\mathbf{W}_{\mathrm{spatial}}\in\mathbb{R}^{1\times h\times w}$. Finally, a sigmoid function is applied to the weights. The spatial attention weights $\mathbf{W}_{\mathrm{spatial}}$ is calculated as:
\begin{equation}
\begin{aligned}
\mathbf{W}_{\mathrm{spatial}}=\sigma(f^{\mathrm{k}\times\mathrm{k}}(max\_channel\_pool(\mathbf{R}))),
\end{aligned}
\end{equation}
where $max\_channel\_pool$ represents max-pooling along the channel axis, $f^{\mathrm{k}\times\mathrm{k}}$ denotes a convolution operation with a kernel size of ${k\times k}$, ${k}=7$ in this paper, and $\sigma$ represents the sigmoid function.

% \subsubsection{Channel Attention Weights for task-Relevant and Irrelevant Features}

% We obtain the channel attention weights $\mathbf{W}^{+}_{\mathrm{channel}}\in\mathbb{R}^{c\times 1\times 1}$ for task-relevant features and $\mathbf{W}^{-}_{\mathrm{channel}}\in\mathbb{R}^{c\times 1\times 1}$ for task-irrelevant features through channel attention. By performing element-wise multiplication of these weights with the residual features $R$ along the channel dimension, we can decompose task-relevant and task-irrelevant features from a channel perspective.
% \begin{equation}
% \begin{aligned}
% \mathbf{W}_{\mathrm{channel}}^{+}=\mathbf{W}_{\mathrm{channel}}
% \end{aligned}
% \end{equation}
% \begin{equation}
% \begin{aligned}
% \mathbf{W}_{\mathrm{channel}}^{-}=\mathbf{1}-\mathbf{W}_{\mathrm{channel}}
% \end{aligned}
% \end{equation}

By performing element-wise multiplication of these weights with the residual features $R$ along the channel dimension, we can decompose task-relevant and task-irrelevant features from a channel perspective.
Channel attention works as follows: First, max-pooling is applied to the residual features $R$ along the spatial dimensions to obtain a ${c\times 1\times 1}$ tensor containing global spatial information. Then, a ${1\times 1}$ convolution (equivalent to a fully connected layer) is used to reduce the number of channels to $c/r$ (where $r=16$ in this paper) to reduce computational complexity. After ReLU activation, the reduced channels are restored to $c$ channels. Finally, a Sigmoid activation function is applied to obtain the channel weights $\mathbf{W}_{\mathrm{channel}}\in\mathbb{R}^{c\times 1\times 1}$.
\begin{equation}
\begin{aligned}
\mathbf{W}_{\mathrm{channel}}=\sigma(W_{2}\delta(W_{1}max\_pool(R))),
\end{aligned}
\end{equation}
where $max\_pool$ represents max-pooling,  $\mathrm{W}_{1}$ and $\mathrm{W}_{2}$ represent two fully connected layers parameterized as $\mathrm{W}_{1}\in\mathbb{R}^{c\times(c/r)}$ and $\mathrm{W}_{2}\in\mathbb{R}^{(c/r)\times c}$. The first fully connected layer uses ReLU $(\delta(\cdot))$ as the activation function, while the second fully connected layer uses the sigmoid function $(\sigma(\cdot))$ as the activation function.

By adding the task-relevant feature ${R}^{+}$ to the normalized feature $\tilde{F}$, we obtain the output feature $\tilde{F}^{+}\in\mathbb{R}^{h\times w\times c}$ of the PAFDR module, as shown below:
\begin{equation}
\begin{aligned}
\tilde{F}^{+}=\tilde{F}+R^{+}.
\end{aligned}
\end{equation}
Correspondingly, by adding task-irrelevant features ${R}^{-}$ to the normalized features $\tilde{F}$, we obtain the contaminated features $\tilde{F}^{-}\in\mathbb{R}^{h\times w\times c}$, which are used for subsequent loss construction to facilitate feature decomposition.
% By adding the task-relevant feature ${R}^{-}$ to the normalized feature $\tilde{F}$, we obtain the output feature $\tilde{F}^{-}\in\mathbb{R}^{h\times w\times c}$ of the PAFDR module, as shown below:
\begin{equation}
\begin{aligned}
\tilde{F}^{-}=\tilde{F}+R^{-}.
\end{aligned}
\end{equation}

% %自适应大小表格
% \begin{table*}[]
% \caption{ Comparison with existing methods on multi-source DG .}         %表标题
% \resizebox{\linewidth}{!}{
% \begin{tabular}{c|cc|cc|cc|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{MS+D+C→M}                      & \multicolumn{2}{c|}{MS+M+C→D}                      & \multicolumn{2}{c|}{M+D+C→MS}                      & \multicolumn{2}{c|}{MS+M+D→C}                      & \multicolumn{2}{c}{Average}                        \\ \cline{2-11} 
%                         & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           \\ \hline

% OSNet \cite{zhou2019osnet}                  & \multicolumn{1}{c|}{72.5}          & 44.2          & \multicolumn{1}{c|}{65.2}          & 47.0          & \multicolumn{1}{c|}{33.2}          & 12.6          & \multicolumn{1}{c|}{23.9}          & 23.3          & \multicolumn{1}{c|}{48.7}          & 31.8          \\ \hline
% QAConv \cite{liao2020interpretable}                 & \multicolumn{1}{c|}{68.6}          & 39.5          & \multicolumn{1}{c|}{64.9}          & 43.4          & \multicolumn{1}{c|}{29.9}          & 10.0          & \multicolumn{1}{c|}{22.9}          & 19.2          & \multicolumn{1}{c|}{46.6}          & 28.0          \\ \hline
% % OSNet-IBN \cite{zhou2022learning}              & \multicolumn{1}{c|}{73.0}          & 44.9          & \multicolumn{1}{c|}{64.6}          & 45.7          & \multicolumn{1}{c|}{39.8}          & 16.2          & \multicolumn{1}{c|}{25.7}          & 25.4          & \multicolumn{1}{c|}{50.8}          & 33.0          \\ \hline
% DAML \cite{shu2021open}                   & \multicolumn{1}{c|}{75.5}          & 49.3          & \multicolumn{1}{c|}{66.5}          & 47.6          & \multicolumn{1}{c|}{32.2}          & 12.6          & \multicolumn{1}{c|}{29.8}          & 29.3          & \multicolumn{1}{c|}{51.0}          & 34.7          \\ \hline
% PAFDR \cite{jin2021style}                    & \multicolumn{1}{c|}{75.2}          & 48.5          & \multicolumn{1}{c|}{66.7}          & 48.3          & \multicolumn{1}{c|}{35.1}          & 13.8          & \multicolumn{1}{c|}{29.1}          & 29.0          & \multicolumn{1}{c|}{51.5}          & 34.9          \\ \hline
% M$^{3}$L \cite{zhao2021learning}                    & \multicolumn{1}{c|}{76.5}          & 51.1          & \multicolumn{1}{c|}{67.1}          & 48.2          & \multicolumn{1}{c|}{32.0}          & 13.1          & \multicolumn{1}{c|}{31.9}          & 30.9          & \multicolumn{1}{c|}{51.9}          & 35.8          \\ \hline
% RaMoE \cite{dai2021generalizable}  & \multicolumn{1}{c|}{82.0} & 56.5 & \multicolumn{1}{c|}{\underline{73.6}} & \textbf{56.9} & \multicolumn{1}{c|}{34.1} & 13.5 & \multicolumn{1}{c|}{36.6} & 35.5 & \multicolumn{1}{c|}{56.6} & 40.6 \\ \hline
% MetaBIN \cite{choi2021meta} & \multicolumn{1}{c|}{\underline{83.2}} & \textbf{61.2} & \multicolumn{1}{c|}{71.3} & 54.9 & \multicolumn{1}{c|}{40.8} & 17.0 & \multicolumn{1}{c|}{\textbf{38.1}} & \underline{37.5} & \multicolumn{1}{c|}{\underline{58.4}} & \underline{42.7} \\ \hline
% OSNet-AIN \cite{zhou2022learning}      & \multicolumn{1}{c|}{73.3}          & 45.8          & \multicolumn{1}{c|}{65.6}          & 47.2          & \multicolumn{1}{c|}{40.2}          & 16.2          & \multicolumn{1}{c|}{27.4}          & 27.1          & \multicolumn{1}{c|}{51.6}          & 34.1          \\ \hline
% MixNorm \cite{qi2022novel} & \multicolumn{1}{c|}{78.9} & 51.4 & \multicolumn{1}{c|}{70.8} & 49.9 & \multicolumn{1}{c|}{\textbf{47.2}} & \underline{19.4} & \multicolumn{1}{c|}{29.6} & 29.0 & \multicolumn{1}{c|}{56.6} & 37.4 \\ \hline
% PAFDR (ours)            & \multicolumn{1}{c|}{\textbf{83.6}} & \underline{57.8} & \multicolumn{1}{c|}{\textbf{73.8}} & \underline{56.4} & \multicolumn{1}{c|}{\underline{41.3}} & \textbf{19.8} & \multicolumn{1}{c|}{\underline{37.3}} & \textbf{37.7} & \multicolumn{1}{c|}{\textbf{58.5}} & \textbf{42.9} \\ \hline
% \end{tabular}}
% \end{table*}

%原始大小表格
% \begin{table*}[]
% \caption{ Comparison with existing methods on multi-source DG .}         %表标题
% \begin{tabular}{c|cc|cc|cc|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{MS+D+C→M}                      & \multicolumn{2}{c|}{MS+M+C→D}                      & \multicolumn{2}{c|}{M+D+C→MS}                      & \multicolumn{2}{c|}{MS+M+D→C}                      & \multicolumn{2}{c}{Average}                        \\ \cline{2-11} 
%                         & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           \\ \hline

% OSNet \cite{zhou2019osnet}                  & \multicolumn{1}{c|}{72.5}          & 44.2          & \multicolumn{1}{c|}{65.2}          & 47.0          & \multicolumn{1}{c|}{33.2}          & 12.6          & \multicolumn{1}{c|}{23.9}          & 23.3          & \multicolumn{1}{c|}{48.7}          & 31.8          \\ \hline
% QAConv \cite{liao2020interpretable}                 & \multicolumn{1}{c|}{68.6}          & 39.5          & \multicolumn{1}{c|}{64.9}          & 43.4          & \multicolumn{1}{c|}{29.9}          & 10.0          & \multicolumn{1}{c|}{22.9}          & 19.2          & \multicolumn{1}{c|}{46.6}          & 28.0          \\ \hline
% % OSNet-IBN \cite{zhou2022learning}              & \multicolumn{1}{c|}{73.0}          & 44.9          & \multicolumn{1}{c|}{64.6}          & 45.7          & \multicolumn{1}{c|}{39.8}          & 16.2          & \multicolumn{1}{c|}{25.7}          & 25.4          & \multicolumn{1}{c|}{50.8}          & 33.0          \\ \hline
% DAML \cite{shu2021open}                   & \multicolumn{1}{c|}{75.5}          & 49.3          & \multicolumn{1}{c|}{66.5}          & 47.6          & \multicolumn{1}{c|}{32.2}          & 12.6          & \multicolumn{1}{c|}{29.8}          & 29.3          & \multicolumn{1}{c|}{51.0}          & 34.7          \\ \hline
% PAFDR \cite{jin2021style}                    & \multicolumn{1}{c|}{75.2}          & 48.5          & \multicolumn{1}{c|}{66.7}          & 48.3          & \multicolumn{1}{c|}{35.1}          & 13.8          & \multicolumn{1}{c|}{29.1}          & 29.0          & \multicolumn{1}{c|}{51.5}          & 34.9          \\ \hline
% M$^{3}$L \cite{zhao2021learning}                    & \multicolumn{1}{c|}{76.5}          & 51.1          & \multicolumn{1}{c|}{67.1}          & 48.2          & \multicolumn{1}{c|}{32.0}          & 13.1          & \multicolumn{1}{c|}{31.9}          & 30.9          & \multicolumn{1}{c|}{51.9}          & 35.8          \\ \hline
% RaMoE \cite{dai2021generalizable}  & \multicolumn{1}{c|}{82.0} & 56.5 & \multicolumn{1}{c|}{\underline{73.6}} & \textbf{56.9} & \multicolumn{1}{c|}{34.1} & 13.5 & \multicolumn{1}{c|}{36.6} & 35.5 & \multicolumn{1}{c|}{56.6} & 40.6 \\ \hline
% MetaBIN \cite{choi2021meta} & \multicolumn{1}{c|}{\underline{83.2}} & \textbf{61.2} & \multicolumn{1}{c|}{71.3} & 54.9 & \multicolumn{1}{c|}{40.8} & 17.0 & \multicolumn{1}{c|}{\textbf{38.1}} & \underline{37.5} & \multicolumn{1}{c|}{\underline{58.4}} & \underline{42.7} \\ \hline
% OSNet-AIN \cite{zhou2022learning}      & \multicolumn{1}{c|}{73.3}          & 45.8          & \multicolumn{1}{c|}{65.6}          & 47.2          & \multicolumn{1}{c|}{40.2}          & 16.2          & \multicolumn{1}{c|}{27.4}          & 27.1          & \multicolumn{1}{c|}{51.6}          & 34.1          \\ \hline
% MixNorm \cite{qi2022novel} & \multicolumn{1}{c|}{78.9} & 51.4 & \multicolumn{1}{c|}{70.8} & 49.9 & \multicolumn{1}{c|}{\textbf{47.2}} & \underline{19.4} & \multicolumn{1}{c|}{29.6} & 29.0 & \multicolumn{1}{c|}{56.6} & 37.4 \\ \hline
% PAFDR (ours)            & \multicolumn{1}{c|}{\textbf{83.6}} & \underline{57.8} & \multicolumn{1}{c|}{\textbf{73.8}} & \underline{56.4} & \multicolumn{1}{c|}{\underline{41.3}} & \textbf{19.8} & \multicolumn{1}{c|}{\underline{37.3}} & \textbf{37.7} & \multicolumn{1}{c|}{\textbf{58.5}} & \textbf{42.9} \\ \hline
% \end{tabular}
% \end{table*}

\subsection{Asymmetric Task-relevant Feature Decomposition Loss}
Existing loss functions used for feature decomposition typically employ symmetrical designs,
% Existing decomposition-based domain generalization methods for person re-identification typically employ symmetrically designed loss functions to promote feature decomposition, 
which ensures that compared to normalized features $\tilde{F}$, the feature $\tilde{F}^{+}$ after adding Task-Relevant features ${R}^{+}$ is more discriminative, while the feature $\tilde{F}^{-}$ after adding task-irrelevant feature ${R}^{-}$ is less discriminative.
However, as shown by features $\tilde{F}$ in Figure 1 ($\textrm{III}$), due to the loss of discriminative information, the discriminability of the normalized feature $\tilde{F}$ is weaker compared to the original image feature $F$ \cite{jin2021style,pan2018two}. Loss functions based on comparisons with weakly discriminative normalized features $\tilde{F}$ will not yield the most discriminative features, hindering the thorough decomposition of Task-Relevant and task-irrelevant features.

To address this issue, we propose an Asymmetric Task-Relevant Feature Decomposition (ATFD) loss. ATFD loss employs asymmetric constraints, enabling different features to match their most suitable comparison objects, thereby maximally promoting the decomposition of Task-Relevant and task-irrelevant features.
% To effectively decompose Task-Relevant and task-irrelevant features, we propose Asymmetric Task-Relevant Feature Decomposition Loss (ATFD) loss,
% To achieve better generalization and discriminability, 
% We propose the Asymmetric Task-Relevant Feature Decomposition (ATFD) loss to promote a more thorough decomposition of Task-Relevant and task-irrelevant features.
% As illustrated ATFD Loss in Figure 2, ATFD loss is designed to ensure that 
As shown by the ATFD Loss in Figure 2 ($\textrm{III}$) (a), the core idea of the ATFD loss is to expect that after compensating the Task-Relevant feature $R^{+}$ to the normalized feature $\tilde{F}$, the discriminability of the feature $\tilde{F}^{+}$ will be enhanced and even exceed that of the original feature $F$ with the strongest discriminability. In other words, compared to the original feature F, the feature $\tilde{F}^{+}$ becomes less ambiguous in its predicted class likelihood (reduced uncertainty), with a smaller entropy. Conversely, adding the task-irrelevant feature $R^{-}$ to $\tilde{F}$ will weaken the discriminability of feature, making the feature $\tilde{F}^{-}$ less discriminative than the normalized feature  $\tilde{F}$ with the weakest discriminability. In other words, compared to the normalized feature $\tilde{F}$, its predicted class likelihood becomes more ambiguous (increased uncertainty), with a bigger entropy. As shown in Figure 2 ($\textrm{III}$) (b), we achieve this goal by designing the ATFD loss $\mathcal{L}_{ATFD}$, which consists of an Enhancement loss $\mathcal{L}_{ATFD}^{+}$ and a Suppression loss $\mathcal{L}_{ATFD}^{-}$, i.e., $\mathcal{L}_{ATFD}=\mathcal{L}_{ATFD}^{+}+\mathcal{L}_{ATFD}^{-}$.



% Loss functions in existing decomposition-based domain generalization methods are often compared with normalized feature $\tilde{F}$ to encourage feature decomposition.
% However, as shown by features $\tilde{F}$ in Figure 1, due to the loss of discriminative information, the discriminability of the normalized feature $\tilde{F}$ is weaker compared to the original image feature $F$ \cite{jin2021style,huang2017arbitrary,pan2018two,zhou2019osnet}. Loss functions based on comparisons with weakly discriminative normalized features $\tilde{F}$ will not yield the most discriminative features.

% \textcolor{red}{In our literature [666]}, we designed an asymmetric task-related feature decoupling loss for the pedestrian re-identification task to address this problem. However, the asymmetric task-related feature decoupling loss is designed based on pedestrian triplets and is not applicable to general domain generalization and domain adaptation tasks. 

% Regarding this issue, in our literature [666], we designed an asymmetric task-related feature decoupling loss for pedestrian re-identification tasks. However, this loss function was based on pedestrian triplets and is not applicable to general domain generalization and domain adaptation tasks. In this paper, we have designed an asymmetric task-related feature decoupling loss, extending PAFDR to general domain generalization and domain adaptation tasks.

% To effectively decompose task-relevant and task-irrelevant features, we propose Asymmetric task-relevant Feature Decomposition Loss (ATFD) loss,

% we extend PAFDR to general domain generalization and domain adaptation tasks.
% Specifically, to achieve better generalization and discriminability, 

% In this paper, we propose Asymmetric Task-relevant Feature Decomposition (ATFD) loss to promote a more thorough decomposition of task-relevant and task-irrelevant features.
% As shown by the ATFD Loss in Figure 2 ($\textrm{III}$), the core idea of the ATFD loss is to expect that after compensating the task-relevant feature $R^{+}$ to the normalized feature $\tilde{F}$, the discriminability of feature $\tilde{F}^{+}$ will be enhanced and even exceed that of the original feature $F$ with the strongest discriminability. In other words, compared to the original feature F, the feature $\tilde{F}^{+}$ becomes less ambiguous in its predicted class likelihood (reduced uncertainty), with a smaller entropy. Conversely, adding the task-irrelevant feature $R^{-}$ to $\tilde{F}$ will weaken the discriminability, making $\tilde{F}^{-}$ less discriminative than the normalized feature $\tilde{F}$ with the weakest discriminability. In other words, compared to the normalized feature $\tilde{F}$, its predicted class likelihood becomes more ambiguous (increased uncertainty), with a bigger entropy.

% We achieve this goal by designing the ATFD loss, which consists of an enhancement loss $\mathcal{L}_{ATFD}^{+}$ and a suppression loss $\mathcal{L}_{ATFD}^{-}$, i.e., $\mathcal{L}_{ATFD}=\mathcal{L}_{ATFD}^{+}+\mathcal{L}_{ATFD}^{-}$.

% In each training batch, we collect three types of sample images: anchor samples (abbreviated as $a$), positive samples (abbreviated as $p$), and negative samples (abbreviated as $n$). Positive and anchor samples come from the same person, while negative samples come from different persons. In terms of expression, we use subscripts to identify the feature of different samples, such as using $\tilde{F}_{a}$ to represent the normalized feature of the anchor sample.
% \begin{equation}
% \begin{aligned}
% \mathcal{L}_{ATFD}^{+}=Softplus(d(\tilde{F}_{a}^{+},\tilde{F}_{p}^{+})-d(F_{a},F_{p}))\\+Softplus(d(F_{a},F_{n})-d(\tilde{F}_{a}^{+},\tilde{F}_{n}^{+})),
% \end{aligned}
% \end{equation}

% \begin{equation}
% \begin{aligned}
% \mathcal{L}_{ATFD}^{-}=Softplus(d(\tilde{F}_{a},\tilde{F}_{p})-d(\tilde{F}_{a}^{-},\tilde{F}_{p}^{-}))\\+Softplus(d(\tilde{F}_{a}^{-},\tilde{F}_{n}^{-})-d(\tilde{F}_{a},\tilde{F}_{n})).
% \end{aligned}
% \end{equation}

% where the function $Softplus(\cdot)=ln(1+exp(\cdot))$ is a strictly increasing function used to remove negative losses during optimization, making the optimization process smoother. $d(\mathbf{x},\mathbf{y})=0.5-\mathbf{x}^{\mathrm{T}}\mathbf{y}/(2\parallel\mathbf{x}\parallel\parallel\mathbf{y}\parallel)$ represents the distance between feature $x$ and feature $y$.





% \begin{figure}[t]
%   \centering
%   \includegraphics[width=0.8\linewidth]{samples/pictures/vis_0208.pdf}
%   \caption{Asymmetric task-relevant Feature Decomposition Loss}
%   \label{fig:onecol}
% \end{figure}

% According to the design of ATFD loss, compared with the original feature F,

 Considering the classification task as an illustration, we pass the enhanced feature vector $\tilde{F}^{+}=\tilde{F}+\mathit{R}^{+}$ to a fully connected layer (containing K nodes, where K represents the number of categories), then through a softmax function (which we denote as $\phi(\widetilde{\mathbf{F}}^+)$. Then we calculate its entropy, using the entropy function $H(\cdot)=-p(\cdot)\log p(\cdot)$. Similarly, the polluted feature vector can be obtained through $\tilde{F}^{-}=\tilde{F}+R^{-}$. 
% Where Softplus(·) = ln(1 + exp(·)) is a monotonically increasing function, designed to reduce the overall optimization difficulty by avoiding the occurrence of negative loss values. 
Enhancement loss $\mathcal{L}_{ATFD}^{+}$ and suppression loss $\mathcal{L}_{ATFD}^{-}$ are defined as follows:
% Based on the above analysis, we define the enhancement loss as:
% \begin{equation}
% \begin{aligned}
% \mathcal{L}_{ATFD}^{+}=Softplus(d(\tilde{F}_{a}^{+},\tilde{F}_{p}^{+})-d(F_{a},F_{p}))\\+Softplus(d(F_{a},F_{n})-d(\tilde{F}_{a}^{+},\tilde{F}_{n}^{+})),
% \end{aligned}
% \end{equation}
\begin{equation}
\begin{aligned}
\mathcal{L}_{ATFD}^+=S(H(\phi(\widetilde{\mathbf{F}}^+))-H(\phi({\mathbf{F}})))
\end{aligned}
\end{equation}
% Based on the above analysis, we design the suppression loss as:
% \begin{equation}
% \begin{aligned}
% \mathcal{L}_{ATFD}^{-}=Softplus(d(\tilde{F}_{a},\tilde{F}_{p})-d(\tilde{F}_{a}^{-},\tilde{F}_{p}^{-}))\\+Softplus(d(\tilde{F}_{a}^{-},\tilde{F}_{n}^{-})-d(\tilde{F}_{a},\tilde{F}_{n})).
% \end{aligned}
% \end{equation}
\begin{equation}
\begin{aligned}
\mathcal{L}_{ATFD}^-=S(H(\phi(\widetilde{\mathbf{F}}))-H(\phi(\widetilde{\mathbf{F}}^-))),
\end{aligned}
\end{equation}
where the function $S(\cdot)=ln(1+exp(\cdot))$ is a strictly increasing function used to remove negative losses during optimization, making the optimization process smoother. 
% Intuitively, ATFD loss assigns appropriate comparison objects to different features. This approach further promotes the decomposition of task-relevant and task-irrelevant features while enhancing feature discriminability.

Intuitively, ATFD loss matches appropriate comparison objects for different features, thereby more effectively decomposing task-related and task-irrelevant features while improving the discriminability of features.
In Eq. (12), compared to \textbf{the original feature $F$}, recovering the task-relevant features into the normalized features $\tilde{F}$ to form enhanced features $\tilde{F}^{+}=\tilde{F}+\mathit{R}^{+}$ can significantly improve discriminability. In Eq. (13), compared to \textbf{the normalized feature $\tilde{F}$}, when we add the task-irrelevant feature $R^{-}$ to the normalized feature $\tilde{F}$ to obtain the contaminated feature $\tilde{F}^{-}=\tilde{F}+R^{-}$, the discriminative ability decreases. 
For other tasks such as segmentation and detection, there are subtle differences in feature vector acquisition, which will be detailed in the next subsection.

% \subsection{Overall Loss Function}
\subsection{Applications}
The PAFDR method we propose demonstrates broad applicability and is designed to enhance both the generalization performance and feature discriminability of Domain Generalization (DG) and Domain Adaptation (DA) networks. As a flexible plug-and-play component, PAFDR can be conveniently integrated into various neural network frameworks to support diverse computer vision applications, including object classification, image segmentation, and object detection. However, minor differences exist in the process of extracting feature vectors—used for calculating the asymmetric task-relevant feature decoupling loss—depending on the specific task type, such as image-level classification, pixel-level segmentation, or region-level detection. These specific distinctions will be elaborated upon in the following subsections.

% Our proposed PAFDR demonstrates versatility in enhancing neural networks generalization and discriminative capabilities for both Domain Generalization (DG) and Domain Adaptation (DA) tasks. As a plug-and-play module, PAFDR integrates seamlessly into various neural network architectures across different computer vision applications including object classification, detection, and segmentation.

% As detailed in Section III-C, we pass the enhanced/corrupted feature vectors ef"/ef- through function to obtain entropy values. When implementing this process across different tasks—classification (image-level), segmentation (pixel-level), and detection (region-level)—there are notable differences in how feature vectors are extracted for computing the recovery loss. We elaborate on these implementation details in the following subsections.

\subsubsection{\textbf{classification}} 
% In classification tasks, we utilize ResNet-50 as the base architecture to showcase the application of PAFDR. As shown in Figure 2 ($\textrm{I}$), PAFDR modules are strategically positioned at the end of each convolution block. When input features F are fed into a PAFDR module, the system produces three distinct feature representations: standardized features Fe, amplified features Fe+, and deteriorated features Fe-. These representations collectively contribute to the computation of asymmetric task-oriented feature separation loss for model optimization.

In classification tasks, we demonstrate the integration method of PAFDR using the widely adopted ResNet-50 network architecture as an example. Referring to Figure 2(a), the proposed PAFDR module can be embedded after each convolutional group. For a PAFDR module, given an input feature map $F$, it will generate three distinct feature representations: a standardized feature $\tilde{F}$, a strengthened feature $\tilde{F}^{+}$, and a degraded feature$\tilde{F}^{-}$  with introduced interference. These features are then utilized to calculate Asymmetric Task-relevant Feature Decomposition (ATFD) loss, which guides the model's optimization.


% In our implementation, we choose ResNet-50 as the backbone network and integrate PAFDR modules after its four convolutional blocks, as shown in Figure 2.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% The total training loss of the entire network is defined as:

% \begin{equation}
% \begin{aligned}
% \mathcal{L}_{Overall}=\mathcal{L}_{task}+\sum_{a=1}^{4}\lambda_{a}\mathcal{L}_{ATFD}^{a},
% \end{aligned}
% \end{equation}

% The total loss $\mathcal{L}_{Overall}$ consists of a task loss $\mathcal{L}_{task}$ for a specific task and an Asymmetric Task-relevant Feature Decomposition (ATFD) loss $\mathcal{L}_{ATFD}$. (For example, for a classification task, the task loss is the classification loss, and the asymmetric task-dependent feature disentanglement loss is also adjusted for the classification task.)
% % The overallloss consists of two parts: task loss $\mathcal{L}_{task}$ for the and ATFD loss $\mathcal{L}_{ATFD}$. 
% Each PAFDR module corresponds to an asymmetric task-relevant feature decomposition loss $\mathcal{L}_{ATFD}^{a}$ (representing the loss for the $a$-th PAFDR module). Here, $\lambda_{a}$ ($a$=1,2,3,4) is a weighting used to balance the contribution of each PAFDR module, uniformly set to 0.1 in this paper.
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


\subsubsection{\textbf{Detection}} 
% Semantic segmentation aims to assign category labels to each pixel in an image, constituting a pixel-level dense prediction challenge. We adopt an approach similar to classification by seamlessly integrating the PAFDR module into the backbone structure of segmentation networks. However, a key difference exists in implementing the recovery loss, where we individually compute entropy values for features at each spatial location (as each pixel position in segmentation contains independent category prediction information), rather than calculating a single entropy value for globally pooled features.
In the domain of object detection, prevalent frameworks like R-CNN [51] and its successors, fast/faster-RCNN [52] and mask-RCNN [53], typically operate by first proposing candidate object regions, then precisely adjusting the bounding box location for each object via regression, and finally predicting the object's category based on feature information within the bounding box. Similar to the approach used for classification problems, we also embed the PAFDR module into the backbone network of the detection model. As illustrated in Figure 3(c), considering that object detection can be conceptualized as a classification process targeting "regions" (i.e., bounding boxes), we compute an entropy value for each ground-truth bounding box area. The reduction loss for optimization is ultimately calculated by averaging the entropy values from all object regions present in the image.

We denote ${B}_{i}$ as the entropy value of the $i$-th object bounding box in the image, and $\tilde{B}_ {i}$ as the entropy value of features within the $i$-th object bounding box before feature compensation in the PAFDR module. Similarly, $\tilde{B}_{i}^{+}$ and $\tilde{B}_{i}^{-}$ represent the entropy values of features obtained after compensation with task-relevant and task-irrelevant features, respectively, within the $i$-th object bounding box. Accordingly, the asymmetric task-relevant feature decoupling loss function can be defined as:
\begin{equation}
\mathcal{L}_{ATFD}^{+} = S ( \frac{1}{N} \sum_{i=1}^{N} H(\phi(\tilde{B}_{i}^{+}) - \frac{1}{N} \sum_{i=1}^{N} H(\phi({B}_{i}) )
\end{equation}

\begin{equation}
\mathcal{L}_{ATFD}^{-} = S ( \frac{1}{N} \sum_{i=1}^{N} H(\phi(\tilde{B}_{i}) - \frac{1}{N} \sum_{i=1}^{N} H(\phi(\tilde{B}_{i}^{-}) )
\end{equation}
Where $N$ represents the total number of object bounding boxes in the image. For this task, the final loss constraint is calculated using the average entropy across all object regions in the image.




\subsubsection{\textbf{ Segmentation}} 
Semantic segmentation aims to assign category labels to each pixel in an image, constituting a pixel-level dense prediction challenge. We adopt an approach similar to classification by seamlessly integrating the PAFDR module into the backbone structure of segmentation networks. However, a key difference exists in implementing the recovery loss, where we individually compute entropy values for features at each spatial location (as each pixel position in segmentation contains independent category prediction information), rather than calculating a single entropy value for globally pooled features.


\section{Experiments}
In this section, we first describe the datasets, evaluation metrics, and implementation details in Section 4.1. In Section 4.2, we demonstrate the comparison of our proposed method with existing domain generalization methods through multi-source domain generalization experiments and single-source domain generalization experiments. Then, we conduct ablation experiments in Section 4.3 to verify the effectiveness of each component of PAFDR. Finally, we perform a visualization analysis in Section 4.5.

\subsection{Object Classification}
To systematically evaluate the generalization of our proposed PAFDR, we tested its effectiveness under both Domain Generalization (DG) and Unsupervised Domain Adaptation (UDA) settings.
\subsubsection{\textbf{Datasets and Implementation Details}}
For domain generalization (DG) experiments, we used two mainstream benchmark datasets: PACS [5] and Office Home [77]. Each dataset contains four distinct domains, with PACS covering 7 categories and Office Home encompassing 65 categories.
For unsupervised domain adaptation tasks, we selected Domainnet and Digit-5 [13] as standard evaluation datasets. Domainnet is a large-scale multi-source domain adaptation dataset [15], comprising approximately 600,000 images across six domains (clipart, painting, QuickDraw, real images, and sketch), covering 345 categories. Digit-5 consists of five independent digit recognition datasets: MNIST [27] (MT), MNIST-M [28] (MM), USPS [29] (UP), SVHN [30] (SV), and SYN [28] (SYN), with each dataset treated as a separate domain.
Comprehensive dataset descriptions and implementation details are provided in the Supplementary Material.

% \subsection{Object Classification}
% % \subsection{Experimental Settings}

% To systematically evaluate the generalization of our proposed
% PAFDR, we evaluated the effectiveness of the proposed PAFDR
% under both Domain Generalization (DG) and Unsupervised Domain
% Adaptation (UDA), described below.
% \subsubsection{\textbf{Datasets and Implementation Details}}
% In our domain generalization (DG) experiments, we employed two mainstream benchmark datasets: PACS [5] and Office Home [77]. Each dataset contains four distinct domains, with PACS covering 7 categories and Office Home encompassing 65 categories. 

% % \subsubsection{\textbf{Unsupervised Domain Adaptation}}
% We selected Domainnet and Digit-5 [13] as standard evaluation datasets for UDA tasks. Domainnet is a large-scale multi-source domain adaptation dataset [15], comprising approximately 600,000 images across six domains (clipart, painting, QuickDraw, real images, and sketch), covering 345 categories. Digit-5 consists of five independent digit recognition datasets: MNIST [27] (MT), MNIST-M [28] (MM), USPS [29] (UP), SVHN [30] (SV), and SYN [28] (SYN), with each dataset treated as a separate domain. 

% Comprehensive dataset descriptions and implementation specifics are provided in the Supplementary Material.

% We adopted a cross-domain training/testing split strategy consistent with Jin et al. [4]. In our experiments, we selected RESNET18 as the base network architecture and incorporated PAFDR components after each convolutional module to enhance cross-domain feature extraction capabilities. During the training process, we set the maximum iteration epochs to 40, with an initial learning rate of 0.002. Each batch contained 30 images from various source domains (10 per domain), and we utilized the SGD algorithm for model optimization.



% We followed the data partitioning scheme proposed by Jin et al. [4]. For Digit-5, we constructed a network architecture with three convolutional layers and two fully connected layers, embedding PAFDR modules after each convolutional layer. Similar to the experimental setup in PAFDR [4], we used M3SDA [13] as a comparison benchmark. For the Domainnet dataset, we employed Resnet101 [49] as the base architecture and added PAFDR components after various convolutional modules to achieve cross-domain feature alignment.

% Regarding training parameters, we set the maximum iteration epochs to 60, with an initial learning rate of 0.005, adopting a cosine annealing strategy for learning rate adjustment. Each batch contained 64 images, and we chose SGD as the optimizer.
% Evaluation Method. In both UDA and DG tasks, we used classification accuracy as the primary evaluation metric, consistent with the approach in [4].

% \begin{table*}[t]
% \centering
% \caption{Comparison of domain generalization methods on PACS and Office-Home datasets.}
% \begin{tabular}{c|ccccc|ccccc}
% \hline
% Datasets          & \multicolumn{5}{c|}{PACS}                                                                          & \multicolumn{5}{c}{Office-Home}                                                                    \\ \hline
% Methods           & Art           & Cartoon       & Photo         & \multicolumn{1}{c|}{Sketch}        & Avg.          & Art           & Clipart       & Product       & \multicolumn{1}{c|}{Real}          & Avg.          \\ \hline
% DeepAll           & 76.5          & 76.9          & 94.6          & \multicolumn{1}{c|}{69.0}          & 79.2          & 53.3          & 51.8          & 71.2          & \multicolumn{1}{c|}{73.2}          & 62.4          \\
% JiGen {[}29{]}    & 79.4          & 75.3          & 96.0          & \multicolumn{1}{c|}{71.4}          & 80.5          & 53.0          & 47.5          & 71.5          & \multicolumn{1}{c|}{72.8}          & 61.2          \\
% SNR {[}4{]}       & 80.3          & 78.2          & 94.5          & \multicolumn{1}{c|}{74.1}          & 81.8          & 61.2          & 53.7          & 74.2          & \multicolumn{1}{c|}{75.1}          & 66.1          \\
% TI-SNR            & 83.4          & 79.7          & 96.2          & \multicolumn{1}{c|}{75.2}          & 83.6          & -             & -             & -             & \multicolumn{1}{c|}{-}             & -             \\
% StyleNeophile {[}33{]} & 84.4     & 79.3          & 94.9          & \multicolumn{1}{c|}{83.3}          & 85.5          & 59.6          & 55.0          & 73.6          & \multicolumn{1}{c|}{75.5}          & 65.9          \\
% I2-ADR {[}15{]}   & 82.9          & 80.8          & 95.0          & \multicolumn{1}{c|}{83.5}          & 85.6          & 66.4          & 53.3          & 74.9          & \multicolumn{1}{c|}{75.3}          & 67.5          \\
% COMEN {[}67{]}    & 82.6          & 81.0          & 94.6          & \multicolumn{1}{c|}{84.5}          & 85.7          & 57.6          & 55.8          & 75.5          & \multicolumn{1}{c|}{76.9}          & 66.5          \\
% % Fishr {[}78{]}    & \textbf{88.4} & 78.7          & \textbf{97.0} & \multicolumn{1}{c|}{77.8}          & 85.5          & 62.4          & 54.4          & 76.2          & \multicolumn{1}{c|}{78.3}          & 67.8          \\
% PCL {[}32{]}      & 86.4          & 78.9          & 95.5          & \multicolumn{1}{c|}{79.5}          & 85.1          & 64.9          & 53.3          & 76.3          & \multicolumn{1}{c|}{78.4}          & 68.2          \\
% KDDRL+{[}52{]}    & 82.3          & 78.9          & 95.6          & \multicolumn{1}{c|}{82.2}          & 84.8          & 59.4          & 55.1          & 74.8          & \multicolumn{1}{c|}{75.4}          & 66.2          \\
% CCFP {[}56{]}     & 87.5          & 81.3          & 96.4          & \multicolumn{1}{c|}{81.4}          & 86.6          & 63.7          & 55.5          & 77.2          & \multicolumn{1}{c|}{79.2}          & 68.9          \\
% EVIL {[}81{]}     & -             & -             & -             & \multicolumn{1}{c|}{-}             & 86.0          & -             & -             & -             & \multicolumn{1}{c|}{-}             & 68.2          \\
% \hline
% PAFDR (ours)      & 85.8          & \textbf{81.7} & 96.4          & \multicolumn{1}{c|}{\textbf{85.2}} & \textbf{87.3} & \textbf{63.5} & \textbf{59.3} & \textbf{76.7} & \multicolumn{1}{c|}{\textbf{77.7}} & \textbf{69.3} \\ \hline
% \end{tabular}
% \end{table*}

% \begin{table*}[h]
% \centering
% \begin{tabular}{|l|c|c|c|c|c|c|c|c|c|c|}
% \hline
% \textbf{Method} & \textbf{Col1} & \textbf{Col2} & \textbf{Col3} & \textbf{Col4} & \textbf{Col5} & \textbf{Col6} & \textbf{Col7} & \textbf{Col8} & \textbf{Col9} & \textbf{Col10} \\
% \hline
% DeepAll & 76.5 & 76.9 & 94.6 & 69.0 & 79.2 & 53.3 & 51.8 & 71.2 & 73.2 & 62.4 \\
% \hline
% JiGen [29] & 79.4 & 75.3 & 96.0 & 71.4 & 80.5 & 53.0 & 47.5 & 71.5 & 72.8 & 61.2 \\
% \hline
% SNR [4] & 80.3 & 78.2 & 94.5 & 74.1 & 81.8 & 61.2 & 53.7 & 74.2 & 75.1 & 66.1 \\
% \hline
% TI-SNR & 83.4 & 79.7 & 96.2 & 75.2 & 83.6 & - & - & - & - & - \\
% \hline
% StyleNeophile [33] & 84.4 & 79.3 & 94.9 & 83.3 & 85.5 & 59.6 & 55.0 & 73.6 & 75.5 & 65.9 \\
% \hline
% I2-ADR [15] & 82.9 & 80.8 & 95.0 & 83.5 & 85.6 & 66.4 & 53.3 & 74.9 & 75.3 & 67.5 \\
% \hline
% COMEN [67] & 82.6 & 81.0 & 94.6 & 84.5 & 85.7 & 57.6 & 55.8 & 75.5 & 76.9 & 66.5 \\
% \hline
% Fishr [78] & \textbf{88.4} & 78.7 & \textbf{97.0} & 77.8 & 85.5 & 62.4 & 54.4 & 76.2 & 78.3 & 67.8 \\
% \hline
% PCL [32] & 86.4 & 78.9 & 95.5 & 79.5 & 85.1 & 64.9 & 53.3 & 76.3 & 78.4 & 68.2 \\
% \hline
% KDDRL+[52] & 82.3 & 78.9 & 95.6 & 82.2 & 84.8 & 59.4 & 55.1 & 74.8 & 75.4 & 66.2 \\
% \hline
% CCFP [56] & 87.5 & 81.3 & 96.4 & 81.4 & 86.6 & 63.7 & 55.5 & 77.2 & 79.2 & 68.9 \\
% \hline
% EVIL [81] & - & - & - & - & 86.0 & - & - & - & - & 68.2 \\
% \hline
% PAFDR(ours) & 85.8 & \textbf{81.7} & 96.4 & \textbf{85.2} & \textbf{87.3} & \textbf{63.5} & \textbf{59.3} & \textbf{76.7} & \textbf{77.7} & \textbf{69.3} \\
% \hline
% \end{tabular}
% \caption{666}
% \label{tab:performance_comparison}
% \end{table*}


% \begin{table*}[t]
% \centering
% \caption{Comparison of domain generalization methods on PACS and Office-Home datasets.}
% \begin{tabular}{c|ccccc|ccccc}
% \hline
% Datasets          & \multicolumn{5}{c|}{PACS}                                                                          & \multicolumn{5}{c}{Office-Home}                                                                    \\ \hline
% Methods           & Art           & Cartoon       & Photo         & \multicolumn{1}{c|}{Sketch}        & Avg.          & Art           & Clipart       & Product       & \multicolumn{1}{c|}{Real}          & Avg.          \\ \hline
% DeepAll & 76.5 & 76.9 & 94.6 & 69.0 & 79.2 & 53.3 & 51.8 & 71.2 & 73.2 & 62.4 \\
% \hline
% JiGen [29] & 79.4 & 75.3 & 96.0 & 71.4 & 80.5 & 53.0 & 47.5 & 71.5 & 72.8 & 61.2 \\
% \hline
% SNR [4] & 80.3 & 78.2 & 94.5 & 74.1 & 81.8 & 61.2 & 53.7 & 74.2 & 75.1 & 66.1 \\
% \hline
% TI-SNR & 83.4 & 79.7 & 96.2 & 75.2 & 83.6 & - & - & - & - & - \\
% \hline
% StyleNeophile [33] & 84.4 & 79.3 & 94.9 & 83.3 & 85.5 & 59.6 & 55.0 & 73.6 & 75.5 & 65.9 \\
% \hline
% I2-ADR [15] & 82.9 & 80.8 & 95.0 & 83.5 & 85.6 & 66.4 & 53.3 & 74.9 & 75.3 & 67.5 \\
% \hline
% COMEN [67] & 82.6 & 81.0 & 94.6 & 84.5 & 85.7 & 57.6 & 55.8 & 75.5 & 76.9 & 66.5 \\
% \hline
% Fishr [78] & \textbf{88.4} & 78.7 & \textbf{97.0} & 77.8 & 85.5 & 62.4 & 54.4 & 76.2 & 78.3 & 67.8 \\
% \hline
% PCL [32] & 86.4 & 78.9 & 95.5 & 79.5 & 85.1 & 64.9 & 53.3 & 76.3 & 78.4 & 68.2 \\
% \hline
% KDDRL+[52] & 82.3 & 78.9 & 95.6 & 82.2 & 84.8 & 59.4 & 55.1 & 74.8 & 75.4 & 66.2 \\
% \hline
% CCFP [56] & 87.5 & 81.3 & 96.4 & 81.4 & 86.6 & 63.7 & 55.5 & 77.2 & 79.2 & 68.9 \\
% \hline
% EVIL [81] & - & - & - & - & 86.0 & - & - & - & - & 68.2 \\
% \hline
% PAFDR(ours) & 85.8 & \textbf{81.7} & 96.4 & \textbf{85.2} & \textbf{87.3} & \textbf{63.5} & \textbf{59.3} & \textbf{76.7} & \textbf{77.7} & \textbf{69.3} \\
% \hline
% \end{tabular}
% \end{table*}


\begin{table*}[t]
\centering
\caption{Comparison of domain generalization methods on PACS and Office-Home datasets.}
\begin{tabular}{c|ccccc|ccccc}
\hline
Datasets          & \multicolumn{5}{c|}{PACS}                                                                          & \multicolumn{5}{c}{Office-Home}                                                                    \\ \hline
Methods           & Art           & Cartoon       & Photo         & \multicolumn{1}{c|}{Sketch}        & Avg.          & Art           & Clipart       & Product       & \multicolumn{1}{c|}{Real}          & Avg.          \\ \hline
DeepAll           & 76.5          & 76.9          & 94.6          & \multicolumn{1}{c|}{69.0}          & 79.2          & 53.3          & 51.8          & 71.2          & \multicolumn{1}{c|}{73.2}          & 62.4          \\
JiGen \cite{carlucci2019domain}    & 79.4          & 75.3          & 96.0          & \multicolumn{1}{c|}{71.4}          & 80.5          & 53.0          & 47.5          & 71.5          & \multicolumn{1}{c|}{72.8}          & 61.2          \\

LDDG \cite{li2020domain}     & 81.0          & 77.9          & 95.7          & \multicolumn{1}{c|}{75.6}          & 82.6          & 54.7          & 52.7          & 72.7          & \multicolumn{1}{c|}{74.2}          & 63.6          \\
L2A-OT \cite{zhou2020learning}   & 83.3          & 78.2          & 96.2         & \multicolumn{1}{c|}{73.6}          & 82.8          & 60.6          & 50.1          & 74.8    & \multicolumn{1}{c|}{77.0}          & 65.6          \\
SagNet \cite{nam2021reducing}   & 83.6          & 77.7          & 95.5          & \multicolumn{1}{c|}{76.3}          & 83.3          & 60.2          & 45.4          & 70.4          & \multicolumn{1}{c|}{73.4}          & 62.3          \\
DAEL \cite{zhou2021domain}      & 84.6    & 74.4          & 95.6          & \multicolumn{1}{c|}{78.9}          & 83.4          & 59.4          & 55.1    & 74.0          & \multicolumn{1}{c|}{75.7}          & 66.1          \\
SelfReg {[}30{]}  & 82.3          & 78.4          & 96.2    & \multicolumn{1}{c|}{77.1}          & 83.5          & -             & -             & -             & \multicolumn{1}{c|}{-}             & -             \\
PCL \cite{yao2022pcl}  & -          & -          & -          & \multicolumn{1}{c|}{-}          & -          & 62.1          & 56.2          & 76.4          & \multicolumn{1}{c|}{77.0}  & 68.0          \\
SNR \cite{jin2022style}       & 80.3          & 78.2          & 94.5          & \multicolumn{1}{c|}{74.1}          & 81.8          & 61.2    & 53.7          & 74.2          & \multicolumn{1}{c|}{75.1}          & 66.1          \\
Tl-SNR  \cite{wang2022domain}          & 83.4          & 79.7   & 96.2    & \multicolumn{1}{c|}{75.2}          & 83.6          & -             & -             & -             & \multicolumn{1}{c|}{-}             & -             \\
% MixStyle {[}32{]} & 84.1          & 78.8          & 96.1          & \multicolumn{1}{c|}{75.9}          & 83.7          & 58.7          & 53.4          & 74.2          & \multicolumn{1}{c|}{\textbf{75.9}} & 65.6          \\

% XDED+[40]            & 83.60          & \textbf{80.20} & \underline{96.50}    & \multicolumn{1}{c|}{79.10}          & 84.8          & 60.80             & 57.10             & 75.30             & \multicolumn{1}{c|}{76.50}             & 67.43             \\
BNE \cite{segu2023batch}  & 78.8          & 78.9          & 94.8          & \multicolumn{1}{c|}{79.7}          & 83.1          & -          & -          & -          & \multicolumn{1}{c|}{-}  & -          \\
KDDRL \cite{niu2023knowledge}    & 82.8          & 79.9          & 96.1          & \multicolumn{1}{c|}{80.2} & 84.8    & 59.4          & 55.1    & 74.8    & \multicolumn{1}{c|}{75.4}          & 66.2    \\
CBDMoE \cite{xu2024cbdmoe}   & \underline{85.0}          & \underline{81.7}          & \underline{96.4}          & \multicolumn{1}{c|}{\textbf{80.5}} & \underline{85.9}    & \underline{63.5}          & \underline{59.3}    & \underline{76.7}    & \multicolumn{1}{c|}{\underline{77.7}}          & \underline{69.3}    \\
\hline % 新增的横线
PAFDR (ours)       & \textbf{85.2} & \textbf{82.9}    & \textbf{96.8} & \multicolumn{1}{c|}{\underline{80.4}}    & \textbf{86.3} & \textbf{63.7} & \textbf{59.4} & \textbf{77.5} & \multicolumn{1}{c|}{\textbf{78.2}}    & \textbf{69.7} \\ \hline
\end{tabular}
\end{table*}


\begin{table*}[t]
\centering
\begin{minipage}{0.48\textwidth}
\centering
\caption{Comparison of UDA methods on DomainNet.}
\begin{tabular}{c|ccccccc}
\hline
Methods           & Clp  & Inf  & Pnt  & Qdr  & Rel  & Skt  & Avg  \\ \hline
M3SDA \cite{peng2019moment}      & 58.6 & 26.0 & 52.3 & 6.3  & 62.7 & 49.5 & 42.6 \\
T-SVDNet \cite{li2021t} & 66.1 & 25.0 & 54.3 & 16.5 & 65.4 & 54.6 & 47.0 \\
PTMDA \cite{ren2022multi}    & 66.0 & 28.5 & 58.4 & 13.0 & 63.0 & 54.1 & 47.2 \\
SPS \cite{wang2022self}      & \underline{70.8} & 24.6 & 55.2 & 19.4 & 67.5 & 57.6 & 49.2 \\
DSFE \cite{wu2023domain}     & 68.2 & 25.8 & \underline{58.8} & 18.3 & \underline{71.9} & 57.6 & 50.1 \\
MIEM \cite{wen2024training}     & 69.0 & \underline{28.6} & 58.7 & \underline{20.5} & 68.9 & \underline{59.2} & \underline{50.8} \\ \hline
PAFDR(ours)       & \textbf{71.5} & \textbf{29.6} & \textbf{59.3} & \textbf{21.1} & \textbf{72.3} & \textbf{60.7} & \textbf{52.4} \\ \hline
\end{tabular}
\end{minipage}
\hfill
\begin{minipage}{0.48\textwidth}
\centering

\caption{Comparison of UDA methods on Digits-5.}
\begin{tabular}{c|cccccc}
\hline
Methods          & mm   & mt   & up   & sv   & syn  & Avg  \\ \hline
M3SDA \cite{peng2019moment}     & 72.8 & 98.4 & 96.1 & 81.3 & 89.6 & 87.7 \\
MDDA \cite{zhao2020multi}     & 78.6 & 98.8 & 93.9 & 79.3 & 79.3 & 88.1 \\
% CMSS {[}16{]}    & 75.3 & 99.0 & 97.7 & 88.4 & 93.7 & 90.8 \\
LtC-MSDA \cite{wang2020learning} & 85.6 & 99.0 & 98.3 & 83.2 & 93.0 & 91.8 \\
MRF-MSDA \cite{xu2022graphical}     & 90.7 & 99.2 & 98.5 & 85.8 & 94.7 & 93.7 \\
% SNR \cite{jin2022style}       & 88.9 & 99.3 & 98.7 & 91.2 & 96.8 & 94.5 \\
% STEM \cite{wang2020learning}     & 89.7 & \underline{99.4} & 98.4 & 89.9 & \textbf{97.5} & 95.0 \\
DIDA-Net \cite{deng2022dynamic}  & 85.7 & 99.3 & 98.6 & \underline{91.7} & \underline{97.3} & 94.5 \\
Tl-SNR  \cite{wang2022domain}    & \underline{90.6} & \textbf{99.5} & \underline{99.0} & \underline{91.7} & 97.0 & \underline{95.6} \\ \hline
PAFDR(ours)      & \textbf{91.4} & \underline{99.4} & \textbf{99.2} & \textbf{91.8} & \textbf{97.5} & \textbf{95.9} \\ \hline
\end{tabular}
\end{minipage}
\end{table*}


% \begin{table}[t]
% \centering
% \caption{Comparison of Classification Accuracy on DomainNet.}
% \begin{tabular}{c|ccccccc}
% \hline
% Methods           & Clp  & Inf  & Pnt  & Qdr  & Rel  & Skt  & Avg  \\ \hline
% M3SDA{[}5{]}      & 58.6 & 26.0 & 52.3 & 6.3  & 62.7 & 49.5 & 42.6 \\
% PAFDR               & 63.8 & 27.6 & 54.5 & 15.8 & 63.8 & 54.5 & 46.7 \\
% T-SVDNet {[}44{]} & 66.1 & 25.0 & 54.3 & 16.5 & 65.4 & 54.6 & 47.0 \\
% PTMDA {[}41{]}    & 66.0 & 28.5 & 58.4 & 13.0 & 63.0 & 54.1 & 47.2 \\
% SPS {[}45{]}      & 70.8 & 24.6 & 55.2 & 19.4 & 67.5 & 57.6 & 49.2 \\
% DSFE {[}46{]}     & 68.2 & 25.8 & 58.8 & 18.3 & 71.9 & 57.6 & 50.1 \\
% MIEM {[}50{]}     & 69.0 & 28.6 & 58.7 & 20.5 & 68.9 & 59.2 & 50.8 \\ \hline
% PAFDR(ours)       & 69.4 & 29.6 & 59.3 & 21.1 & 69.3 & 60.7 & 51.6 \\ \hline
% \end{tabular}
% \end{table}


% \begin{table}[t]
% \centering
% \caption{Comparison of Classification Accuracy on Digits-5.}
% \begin{tabular}{c|cccccc}
% \hline
% Methods          & mm   & mt   & up   & SV   & syn  & Avg  \\ \hline
% M3SDA{[}5{]}     & 72.8 & 98.4 & 96.1 & 81.3 & 89.6 & 87.7 \\
% MDDA {[}6{]}     & 78.6 & 98.8 & 93.9 & 79.3 & 79.3 & 88.1 \\
% CMSS {[}16{]}    & 75.3 & 99.0 & 97.7 & 88.4 & 93.7 & 90.8 \\
% LtC-MSDA{[}42{]} & 85.6 & 99.0 & 98.3 & 83.2 & 93.0 & 91.8 \\
% PAFDR {[}4{]}      & 88.9 & 99.3 & 98.7 & 91.2 & 96.8 & 94.5 \\
% STEM{[}29{]}     & 89.7 & 99.4 & 98.4 & 89.9 & 97.5 & 95.0 \\
% DIDA-Net{[}9{]}  & 85.7 & 99.3 & 98.6 & 91.7 & 97.3 & 94.5 \\
% Tl-SNR           & 90.6 & 99.5 & 99.0 & 91.7 & 97.0 & 95.6 \\ \hline
% PAFDR(ours)      & 91.4 & 99.3 & 99.2 & 91.8 & 97.5 & 95.8 \\ \hline
% \end{tabular}
% \end{table}



\subsection{Comparison with Existing Approaches}
\subsubsection{\textbf{Results on Domain Generalization}}
% We evaluated the generalization of our proposed method through two types of experiments: First, a comparison with existing approaches on multi-source domain generalization  tasks. Second, a comparison with existing approaches methods on single-source domain generalization  tasks.
Since the PAFDR module has the ability to mitigate domain gaps and recover task-relevant features, it can enhance the generalization capability of the network while maintaining its discriminability. 
% We evaluated PAFDR's effectiveness in DG  by comparing it with existing methods in Table 1 and Table 3.
We evaluated PAFDR's effectiveness capability by experiments on DG tasks, comparing against existing approaches.

We evaluate our PAFDR against these leading domain generalization approaches. (1) DeepAll involves training a singular pre-trained ResNet using combined data from all source domains with cross-entropy loss. This trained classifier is subsequently applied directly to target domain examples. (2) Representation learning encompasses alignment-oriented techniques (CORAL and LDDG \cite{li2020domain}), disentanglement approaches (JiGen \cite{carlucci2019domain}), adversarial training methods (SagNet \cite{nam2021reducing}), causality-based frameworks (L2A-OT \cite{zhou2020learning}), and self-supervision approaches (DAEL \cite{zhou2021domain}, SelfReg, PCL \cite{yao2022pcl}, and SNR \cite{jin2022style}). (3) Data augmentation incorporates style transfer techniques (Tl-SNR \cite{wang2022domain}) and domain randomization strategies. (4) Learning strategies cover ensemble-based frameworks (BNE \cite{segu2023batch}, KDDRL \cite{niu2023knowledge}, and CBDMoE \cite{xu2024cbdmoe}). 

As shown in Table 1, PAFDR outperforms all competing methods on both datasets. On PACS, PAFDR achieves 86.3 average accuracy, surpassing CBDMoE (85.9). On Office-Home, PAFDR achieves 69.7 average accuracy, outperforming CBDMoE (69.3). Compared to the baseline SNR method, PAFDR improves by 4.5 and 3.6 percentage points on PACS and Office-Home respectively, demonstrating the effectiveness of our method..

% As shown in Table 1, our proposed PAFDR method consistently outperforms all competing methods on both datasets. On the PACS dataset, PAFDR achieves the best average accuracy of 86.3, surpassing the second-best method CBDMoE (85.9) by 0.4 percentage points. Notably, PAFDR achieves the highest accuracy on Art (85.2), Cartoon (82.9), and Photo (96.8) domains, and competitive results on Sketch (80.4, second only to CBDMoE's 80.5).
% On the Office-Home dataset, PAFDR demonstrates even more significant improvements, achieving an average accuracy of 69.7, which outperforms the second-best method CBDMoE (69.3) by 0.4 percentage points. Our method attains the best results on all four domains: Art (63.7), Clipart (59.4), Product (77.5), and Real (78.2).
% Particularly worth noting is that compared to the baseline SNR method \cite{jin2022style}, our PAFDR achieves substantial improvements of 4.5 percentage points on PACS (81.8 → 86.3) and 3.6 percentage points on Office-Home (66.1 → 69.7). This significant performance gain highlights the effectiveness of our dynamic adaptive feature disentanglement and recalibration mechanism, which can better separate domain-invariant and domain-specific features while adaptively adjusting their importance for different target domains.
% The superior performance of our method across diverse visual domains demonstrates the robustness and generalization capability of PAFDR, making it a promising approach for practical domain generalization applications. 

%  We evaluate our PAFDR against these leading domain generalization approaches. (1) DeepAll involves training a singular pre-trained ResNet using combined data from all source domains with cross-entropy loss. This trained classifier is subsequently applied directly to target domain examples. (2) Representation learning encompasses alignment-oriented techniques (CORAL [18] and LDDG [23]), disentanglement approaches (PAFDR [4]), adversarial training methods (SagNet [14]), attention mechanisms (I2-ADR [15]), causality-based frameworks (CIRL [28]), and self-supervision approaches (JiGen [29], SelfReg [30], and PCL [31]). (3) Data augmentation incorporates style transfer techniques (MixStyle [32], StyleNeophile [33], and STEAM [68]) and domain randomization strategies (FACT [35]). (4) Learning strategies cover ensemble-based frameworks (BSF [8], BNE [9], DAEL [5], SWAD [69], KDDRL [52], XDED [40], DART [41], and EoA [42]) and meta-learning approaches (L2A-OT [38] and MVDG [44]). We select PAFDR \cite{jin2021style} as our baseline, which is a strong method in decomposition-based domain generalization. The comparative results are displayed in Tables I and II.

% As shown in Table 1, our proposed PAFDR method consistently outperforms all competing methods on both datasets. On the PACS dataset, PAFDR achieves the best average accuracy of 84.9, surpassing the second-best method KDDRL+ (84.7) by 0.2 percentage points. Notably, PAFDR achieves the highest accuracy on Art (84.8) and Photo (96.4) domains, and competitive results on Cartoon (79.5, second only to Tl-PAFDR's 79.7) and Sketch (79.1, second only to KDDRL+'s 82.2).
% On the Office-Home dataset, PAFDR demonstrates even more significant improvements, achieving an average accuracy of 66.8, which outperforms the second-best method KDDRL+ (66.2) by 0.6 percentage points. Our method attains the best results on three out of four domains: Art (61.3), Clipart (55.2), and Product (75.1), while achieving competitive performance on the Real domain (75.7, second only to MixStyle's 75.9).
% Particularly worth noting is that compared to the baseline PAFDR method [4], our PAFDR achieves substantial improvements of 3.1 percentage points on PACS (81.8 → 84.9) and 0.7 percentage points on Office-Home (66.1 → 66.8). This significant performance gain highlights the effectiveness of our dynamic adaptive feature disentanglement and recalibration mechanism, which can better separate domain-invariant and domain-specific features while adaptively adjusting their importance for different target domains.
% The superior performance of our method across diverse visual domains demonstrates the robustness and generalization of PAFDR, making it a promising approach for practical domain generalization applications.

% \noindent \textbf{Single-Source Domain Generalization  Experiment Results.} 

% \begin{table}[]
% \caption{Comparison with existing methods on single-source DG .}         %表标题
% \resizebox{\linewidth}{!}{
% \begin{tabular}{c|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{M→D} & \multicolumn{2}{c}{D→M} \\ \cline{2-5} 
%        & Rank-1 & mAP & Rank-1 & mAP \\ \hline
% IBN-Net  \cite{pan2018two}   & 43.7 & 24.3 & 24.0 & 23.5 \\ \hline
% CrossGrad \cite{shankar2018generalizing}    & 48.5 & 27.1 & 56.7 & 26.3 \\ \hline

% OSNet \cite{zhou2019osnet}        & 44.7 & 25.9 & 52.2 & 24.0 \\ \hline
% % OSNet-IBN \cite{zhou2022learning}    & 47.9 & 27.6 & 57.8 & 27.4 \\ \hline
% QAConv \cite{liao2020interpretable}       & 48.8 & 28.7 & 58.6 & 27.2 \\ \hline
% L2A-OT \cite{zhou2020learning}       & 50.1 & 29.2 & 63.8 & 30.2 \\ \hline
% PAFDR \cite{jin2021style}     & 55.1 & 33.6 & 66.7 & 33.9 \\ \hline
% MetaBIN \cite{choi2021meta} & 55.2 & 33.1 & 69.2 & 35.9 \\ \hline
% OSNet-AIN \cite{zhou2022learning}    & 52.4 & 30.5 & 61.0 & 30.6 \\ \hline
% DTIN-Net \cite{jiao2022dynamically} & \underline{57.0}  & \underline{36.1}  & \textbf{69.8}  & 37.4  \\ \hline
% SuA-SpML \cite{zhang2023style} & 54.4 & 33.4 & 64.1 & 35.2 \\ \hline
% PAFDR (ours)  & \textbf{57.2} & \textbf{36.4} & \underline{69.6} & \textbf{37.8} \\ \hline
% \end{tabular}}
% \end{table}

% \begin{table}[]
% \caption{Comparison with existing methods on single-source DG .}         %表标题
% \begin{tabular}{c|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{M→D} & \multicolumn{2}{c}{D→M} \\ \cline{2-5} 
%        & Rank-1 & mAP & Rank-1 & mAP \\ \hline
% IBN-Net  \cite{pan2018two}   & 43.7 & 24.3 & 24.0 & 23.5 \\ \hline
% CrossGrad \cite{shankar2018generalizing}    & 48.5 & 27.1 & 56.7 & 26.3 \\ \hline
% OSNet \cite{zhou2019osnet}        & 44.7 & 25.9 & 52.2 & 24.0 \\ \hline
% % OSNet-IBN \cite{zhou2022learning}    & 47.9 & 27.6 & 57.8 & 27.4 \\ \hline
% QAConv \cite{liao2020interpretable}       & 48.8 & 28.7 & 58.6 & 27.2 \\ \hline
% L2A-OT \cite{zhou2020learning}       & 50.1 & 29.2 & 63.8 & 30.2 \\ \hline
% PAFDR \cite{jin2021style}     & 55.1 & 33.6 & 66.7 & 33.9 \\ \hline
% MetaBIN \cite{choi2021meta} & 55.2 & 33.1 & 69.2 & 35.9 \\ \hline
% OSNet-AIN \cite{zhou2022learning}    & 52.4 & 30.5 & 61.0 & 30.6 \\ \hline
% DTIN-Net \cite{jiao2022dynamically} & \underline{57.0}  & \underline{36.1}  & \textbf{69.8}  & 37.4  \\ \hline
% SuA-SpML \cite{zhang2023style} & 54.4 & 33.4 & 64.1 & 35.2 \\ \hline
% PAFDR (ours)  & \textbf{57.2} & \textbf{36.4} & \underline{69.6} & \textbf{37.8} \\ \hline
% \end{tabular}
% \end{table}


\subsubsection{\textbf{Results on Unsupervised Domain Adaptation}}
Integrating the PAFDR module into existing UDA methods can effectively reduce domain gaps while maintaining feature discriminability, thereby significantly enhancing domain adaptation performance. Table 2 and Table 3 present the experimental results on DomainNet and Digits-5 datasets, respectively.

To validate the effectiveness of PAFDR, we adopt the classic UDA method M3SDA \cite{peng2019moment} as our baseline network and refer to the scheme integrated with our PAFDR module as PAFDR-M3SDA.
The experimental results demonstrate: 1) In terms of overall performance (as indicated by the Avg column), PAFDR achieves the best performance on both datasets, reaching \textbf{52.4} on DomainNet and \textbf{95.9} on Digits-5, surpassing the second-best methods MIEM \cite{wen2024training} (\underline{50.8}) and Tl-SNR \cite{wang2022domain} (\underline{95.6}), respectively. 2) Compared to the baseline M3SDA, the introduction of PAFDR brings substantial improvements: a gain of 9.8 percentage points on DomainNet (from 42.6 to 52.4) and 8.2 percentage points on Digits-5 (from 87.7 to 95.9), fully validating the effectiveness of the PAFDR module in unsupervised domain adaptation tasks.




% We evaluated our PAFDR method on two representative multi-source domain adaptation datasets: DomainNet and Digits-5. 

% The experimental results demonstrate that our proposed PAFDR method achieved state-of-the-art average performance on both datasets.
% On the DomainNet dataset, we compared PAFDR with several advanced unsupervised domain adaptation methods, including M3SDA \cite{peng2019moment}, T-SVDNet \cite{li2021t}, PTMDA \cite{ren2022multi}, SPS \cite{wang2022self}, DSFE \cite{wu2023domain}, and MIEM \cite{wen2024training}. PAFDR achieved an average accuracy of 52.4 across six domains, surpassing the current best method MIEM (50.8) by 1.6 percentage points. Specifically, PAFDR achieved the best performance across all six domains: Clipart (71.5), Infograph (29.6), Painting (59.3), QuickDraw (21.1), Real (72.3), and Sketch (60.7). Notably, on the challenging Infograph domain, PAFDR reached an accuracy of 29.6, showing a 1.0 percentage point improvement over the second-best method MIEM (28.6).

% On the Digits-5 dataset, we compared with methods including M3SDA \cite{peng2019moment}, MDDA \cite{zhao2020multi}, LtC-MSDA \cite{wang2020learning}, MRF-MSDA \cite{xu2022graphical}, 
% % SNR \cite{jin2022style}, 
% DIDA-Net \cite{deng2022dynamic}, and Tl-SNR \cite{wang2022domain}. PAFDR also demonstrated excellent performance, achieving an average accuracy of 95.9, representing a 0.3 percentage point improvement over the existing best method Tl-SNR (95.6). PAFDR achieved the best performance in four domains: mm (91.4), up (99.2), sv (91.8), and syn (97.5). Particularly impressive is the improvement in the mm domain, which has significant domain shift, where accuracy increased to 91.4, showing a 0.8 percentage point improvement over the second-best method Tl-SNR (90.6).

% In conclusion, the experimental results indicate that PAFDR has significant advantages in handling multi-source domain adaptation tasks. It not only outperforms existing methods in overall performance but also achieves notable improvements in challenging domains. These results validate the effectiveness and robustness of our proposed method in addressing domain shift problems. 




% \subsubsection{\textbf{Results on Unsupervised Domain Adaptation}}
% Our PAFDR method was evaluated on two representative multi-source domain adaptation datasets: DomainNet and Digits-5. The experimental results demonstrate that our proposed PAFDR method achieved state-of-the-art average performance on both datasets.

% On the DomainNet dataset, PAFDR achieved an average accuracy of 51.6 across six domains, surpassing the current best method MIEM (50.8) by 0.8 percentage points. Specifically, PAFDR achieved the best performance in four domains: Infograph (29.6), Painting (59.3), QuickDraw (21.1), and Sketch (60.7). Notably, on the challenging QuickDraw domain, PAFDR reached an accuracy of 21.1, showing significant improvement over baseline methods.

% On the Digits-5 dataset, PAFDR also demonstrated excellent performance, achieving an average accuracy of 95.8. This represents a 0.2 percentage point improvement over the existing best method Tl-SNR (95.6). Notably, PAFDR achieved the best performance in three domains: mm (91.4), up (99.2), and sv (91.8). Particularly impressive is the improvement in the mm domain, which has significant domain shift, where accuracy increased to 91.4, showing a 2.5 percentage point improvement over the baseline PAFDR method (88.9).

% In conclusion, the experimental results indicate that PAFDR has significant advantages in handling multi-source domain adaptation tasks. It not only outperforms existing methods in overall performance but also achieves notable improvements in challenging domains (such as the QuickDraw domain in DomainNet and the mm domain in Digits-5). These results validate the effectiveness and robustness of our proposed method in addressing domain shift problems.



\subsection{Ablation Study} 
\label{sec:ablation}
\begin{table*}[t]
\centering
\caption{Ablation study on the channel and spatial attention.}
\begin{tabular}{c|ccccc|ccccc}
\hline
Datasets          & \multicolumn{5}{c|}{PACS}                                                                          & \multicolumn{5}{c}{Office-Home}                                                                    \\ \hline
Methods           & Art           & Cartoon       & Photo         & \multicolumn{1}{c|}{Sketch}        & Avg.          & Art           & Clipart       & Product       & \multicolumn{1}{c|}{Real}          & Avg.          \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w/o attention \end{tabular} & 79.3          & 75.1 & 94.2    & \multicolumn{1}{c|}{74.1}          & 80.7         &56.7               &51.5              &71.8              & \multicolumn{1}{c|}{73.5}             & 63.4             \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w/o spatial attention\end{tabular} & 82.4          & 78.7          & 94.6          & \multicolumn{1}{c|}{76.5}          &83.1           & 62.3          & 55.3          & 75.2          & \multicolumn{1}{c|}{76.1} &67.2           \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w/o channel attention \end{tabular} & 82.7          & 78.5          & 94.9          & \multicolumn{1}{c|}{76.8}          & 83.2          & 62.0          & 55.9          & 75.6          & \multicolumn{1}{c|}{76.7}  &67.6           \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w series attentions \end{tabular} & 84.1          & 79.9          & 95.7          & \multicolumn{1}{c|}{78.2} & 84.5    & 62.4          & 58.1    & 76.8    & \multicolumn{1}{c|}{77.9}          & 68.8    \\ \hline
% PAFDR (ours)       & \textbf{84.8} & \textbf{79.5}    & \textbf{96.4} & \multicolumn{1}{c|}{\textbf{79.1}}    & \textbf{84.9} & \textbf{62.7} & \textbf{58.4} & \textbf{77.5} & \multicolumn{1}{c|}{\textbf{78.2}}    & \textbf{69.2} \\ \hline

PAFDR (ours)       & \textbf{85.2} & \textbf{82.9}    & \textbf{96.8} & \multicolumn{1}{c|}{\textbf{80.4}}    & \textbf{86.3} & \textbf{63.7} & \textbf{59.4} & \textbf{77.5} & \multicolumn{1}{c|}{\textbf{78.2}}    & \textbf{69.7} \\ \hline
\end{tabular}
\end{table*}

% \begin{table}[t]
% \centering
% \caption{Study on the asymmetric task-relevant feature decomposition loss.}
% \resizebox{\columnwidth}{!}{%
% \begin{tabular}{c|cccc|c}
% \hline
% Methods & Art & Cartoon & Photo & Sketch & Avg. \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o $\mathcal{L}_{ATFD}$ {[}30{]}\end{tabular} & 82.3 & 78.4 & \underline{96.2} & 77.5 & 83.6 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o $\mathcal{L}_{ATFD}^{+}${[}32{]}\end{tabular} & 83.4 & \textbf{79.7} & \underline{96.2} & 75.2 & 83.6 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o $\mathcal{L}_{ATFD}^{-}$ {[}32{]}\end{tabular} & 84.1 & 78.8 & 96.1 & 75.9 & 83.7 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w $\mathcal{L}_{baseline}${[}52{]}\end{tabular} & 82.3 & 78.9 & 95.6 & \textbf{82.2} & \underline{84.7} \\ \hline
% PAFDR (ours) & \textbf{84.8} & \underline{79.5} & \textbf{96.4} & \underline{79.1} & \textbf{84.9} \\ \hline
% \end{tabular}%
% }
% \end{table}

\begin{table*}[t]
\centering
\caption{Ablation study on the asymmetric task-relevant feature decomposition loss.}
\begin{tabular}{c|ccccc|ccccc}
\hline
Datasets          & \multicolumn{5}{c|}{PACS}                                                                          & \multicolumn{5}{c}{Office-Home}                                                                    \\ \hline
Methods           & Art           & Cartoon       & Photo         & \multicolumn{1}{c|}{Sketch}        & Avg.          & Art           & Clipart       & Product       & \multicolumn{1}{c|}{Real}          & Avg.          \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w/o $\mathcal{L}_{ATFD}$ \end{tabular} & 79.6          & 75.7 & 94.1    & \multicolumn{1}{c|}{74.3}          & 80.9          & 55.9             & 51.7             & 71.2             & \multicolumn{1}{c|}{73.3}             & 63.0             \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w/o $\mathcal{L}_{ATFD}^{+}$ \end{tabular} & 81.5          & 77.4          & 94.7          & \multicolumn{1}{c|}{76.9}          & 82.6          & 59.5          & 55.8          & 74.6          & \multicolumn{1}{c|}{75.8} & 66.4          \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w/o $\mathcal{L}_{ATFD}^{-}$  \end{tabular} & 82.1          & 77.2          & 95.0          & \multicolumn{1}{c|}{76.4}          & 82.7          & 59.6          & 56.2          & 75.3          & \multicolumn{1}{c|}{76.0}  & 66.8          \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w $\mathcal{L}_{baseline}$ \end{tabular} & 83.7          & 79.8          & 95.6          & \multicolumn{1}{c|}{79.2} & 84.6    & 62.3          & 58.1    & 74.9    & \multicolumn{1}{c|}{78.2}          & 68.4    \\ \hline
% PAFDR (ours)       & \textbf{84.8} & \textbf{79.5}    & \textbf{96.4} & \multicolumn{1}{c|}{79.1}    & \textbf{84.9} & \textbf{62.7} & \textbf{58.4} & \textbf{77.5} & \multicolumn{1}{c|}{78.2}    & \textbf{69.2} \\ \hline

PAFDR       & \textbf{85.2} & \textbf{82.9}    & \textbf{96.8} & \multicolumn{1}{c|}{\textbf{80.4}}    & \textbf{86.3} & \textbf{63.7} & \textbf{59.4} & \textbf{77.5} & \multicolumn{1}{c|}{\textbf{78.4}}    & \textbf{69.7} \\ \hline
\end{tabular}
\end{table*}

We conduct comprehensive ablation studies to demonstrate the effectiveness of dual attention in PAFDR and its Asymmetric task-relevant Feature Decomposition Loss.
\subsubsection{\textbf{Ablation Study on Spatial Attention and Channel Attention}}
% \noindent \textbf{Influence of parallel spatial and channel attention.}
We conduct ablation studies to analyze the effectiveness of parallel attention in our PAFDR. The table shows results on two datasets (PACS and Office-Home).
Removing all attention (PAFDR w/o attentionattention
attention) significantly drops performance (average accuracy by 3.8 on PACS and 6.3 on Office-Home), highlighting attention's importance. Removing either spatial attention (PAFDR w/o spatial attentionspatial\ attention
spatial attention) or channel attention (PAFDR w/o channel attentionchannel\ attention
channel attention) individually results in comparable performance drops, indicating both contribute similarly.
Furthermore, comparing parallel spatial and channel attention (PAFDR) with its sequential counterpart (PAFDR w series attentionsseries\ attentions
series attentions) shows parallel design achieves better performance (average accuracy improvements of 1.8 on PACS and 0.9 on Office-Home).
These results indicate that both spatial attention and channel attention are crucial, and their parallel arrangement is the optimal choice for domain generalization, likely due to the regularization-like effect produced by the parallel structure. Specifically, the parallel structure forces spatial attention and channel attention to learn independently, preventing excessive co-adaptation between them, thereby enabling the model to learn more robust and generalizable feature representations.




% To validate the effectiveness of the attention mechanism in PAFDR, we conducted detailed ablation experiments on both PACS and Office-Home datasets. Specifically, we compared several variants: a baseline model without any attention mechanism (PAFDR w/o attention), a model without spatial attention (PAFDR w/o spatial attention), a model without channel attention (PAFDR w/o channel attention), and a model using serial attention (PAFDR w series attentions).

% On the PACS dataset, experimental results show that the complete PAFDR model achieved the best performance across all domains, reaching an average accuracy of 86.3. In contrast, the baseline model without any attention mechanism only achieved an average accuracy of 80.7, indicating that the attention mechanism brought a significant performance improvement of 5.6 percentage points. Specifically, the models without spatial attention and channel attention achieved average accuracies of 83.1 and 83.2 respectively, demonstrating that both attention mechanisms make important contributions to model performance. Notably, while the serial attention model (84.5) outperformed single-attention variants, it still fell short of our proposed parallel attention structure (86.3).



% On the Office-Home dataset, we observed similar trends. The complete PAFDR model achieved an average accuracy of 69.7, showing an improvement of 6.3 percentage points compared to the baseline model (63.4). The models without spatial attention and channel attention achieved average accuracies of 67.2 and 67.6 respectively, further confirming the importance of both attention mechanisms. The serial attention model (68.8) similarly underperformed compared to our proposed parallel structure.

% The ablation study results clearly demonstrate that the attention mechanism plays a crucial role in improving model performance, bringing significant improvements of 5.6 and 6.3 percentage points on the PACS and Office-Home datasets, respectively. The experiments also confirm that both spatial and channel attention mechanisms are essential, as the absence of either leads to performance degradation. Furthermore, our proposed parallel attention structure outperforms the traditional serial structure, validating the rationality of our design choices. These findings not only confirm the importance of attention mechanisms in domain generalization tasks but also highlight the superiority of our proposed parallel attention structure.

% \subsubsection{\textbf{Effect of Spatial Attention and Channel Attention}}
% To validate the effectiveness of the attention mechanism in PAFDR, we conducted detailed ablation experiments on both PACS and Office-Home datasets. Specifically, we compared several variants: a baseline model without any attention mechanism (PAFDR w/o attention), a model without spatial attention (PAFDR w/o spatial attention), a model without channel attention (PAFDR w/o channel attention), and a model using serial attention (PAFDR w series attentions).

% On the PACS dataset, experimental results show that the complete PAFDR model achieved the best performance across all domains, reaching an average accuracy of 84.9. In contrast, the baseline model without any attention mechanism only achieved an average accuracy of 80.0, indicating that the attention mechanism brought a significant performance improvement of 4.9 percentage points. Specifically, the models without spatial attention and channel attention achieved average accuracies of 83.1 and 83.2 respectively, demonstrating that both attention mechanisms make important contributions to model performance. Notably, while the serial attention model (84.2) outperformed single-attention variants, it still fell short of our proposed parallel attention structure (84.9).

% On the Office-Home dataset, we observed similar trends. The complete PAFDR model achieved an average accuracy of 69.2, showing an improvement of 5.8 percentage points compared to the baseline model (63.4). The models without spatial attention and channel attention achieved average accuracies of 67.2 and 67.6 respectively, further confirming the importance of both attention mechanisms. The serial attention model (68.8) similarly underperformed compared to our proposed parallel structure.

% The ablation study results clearly demonstrate that the attention mechanism plays a crucial role in improving model performance, bringing significant improvements of 4.9 and 5.8 percentage points on the PACS and Office-Home datasets, respectively. The experiments also confirm that both spatial and channel attention mechanisms are essential, as the absence of either leads to performance degradation. Furthermore, our proposed parallel attention structure outperforms the traditional serial structure, validating the rationality of our design choices. These findings not only confirm the importance of attention mechanisms in domain generalization tasks but also highlight the superiority of our proposed parallel attention structure.

% We investigate the effectiveness of the proposed task-relevant feature decomposition method that integrates spatial attention and channel attention. As shown in Table 3, in Market1501→Duke, the scheme with the complete spatial-channel attention achieved the best performance, with a 12.4\% increase in Rank-1 accuracy and a 9.2\% increase in mAP compared to the scheme with attention completely removed. In Duke→Market1501, the scheme with the complete spatial-channel attention also achieved the best performance, with a 29.9\%  increase in Rank-1 accuracy and a 9.0\% increase in mAP compared to the scheme with attention completely removed.
% These data indicate that combining spatial and channel attention can better decouple task-relevant and task-irrelevant features. The two attention mechanisms each have their unique advantages, and their complementary strengths enhance the overall feature decomposition effect.
% We conduct ablation studies to analyze the effectiveness of attention mechanisms in our PAFDR framework. Table 3 shows the experimental results on two DG  scenarios (M→D and D→M).

% Without any attention modules ("PAFDR w/o attention"), the performance drops significantly, where Rank-1 accuracy decreases by 13.0 (from 57.2 to 44.2) on M→D and 32.0 (from 69.6 to 37.6) on D→M, highlighting the importance of attention mechanisms. When examining individual components, removing spatial attention results in Rank-1 accuracy of 55.3 (M→D) and 66.7 (D→M), while removing channel attention achieves similar results with 55.6 (M→D) and 66.9 (D→M), indicating both attention types contribute comparably.

% Additionally, we compare parallel (PAFDR) versus series ("PAFDR w series attentions") arrangement of attention modules. The parallel design achieves better performance, with improvements of 0.5 and 1.2 in Rank-1 accuracy on M→D and D→M, respectively. These results demonstrate that both spatial and channel attention mechanisms are essential components, and their parallel arrangement is optimal for cross-domain  re-identification. A possible explanation is that, compared to sequential concatenation, parallel spatial and channel attention mechanisms work independently without strong correlations, and this independent operation of spatial and channel attention produces some regularization effects.
% \begin{table}[t]
% \centering
% \caption{Study on the channel and spatial attention.}
% \begin{tabular}{c|cccc|c}
% \hline
% Methods & Art & Cartoon & Photo & Sketch & Avg. \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o\\attention {[}30{]}\end{tabular} & 82.3 & 78.4 & \underline{96.2} & 77.5 & 83.6 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o\\spatial attention\end{tabular} & 83.4 & \textbf{79.7} & \underline{96.2} & 75.2 & 83.6 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o\\channel attention {[}32{]}\end{tabular} & 84.1 & 78.8 & 96.1 & 75.9 & 83.7 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w\\series attentions{[}52{]}\end{tabular} & 82.3 & 78.9 & 95.6 & \textbf{82.2} & \underline{84.7} \\ \hline
% PAFDR (ours) & \textbf{84.8} & \underline{79.5} & \textbf{96.4} & \underline{79.1} & \textbf{84.9} \\ \hline
% \end{tabular}
% \end{table}

% \begin{table}[t]
% \centering
% \caption{Study on the channel and spatial attention.}
% \resizebox{\columnwidth}{!}{%
% \begin{tabular}{c|cccc|c}
% \hline
% Methods & Art & Cartoon & Photo & Sketch & Avg. \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o\\attention {[}30{]}\end{tabular} & 82.3 & 78.4 & \underline{96.2} & 77.5 & 83.6 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o\\spatial attention\end{tabular} & 83.4 & \textbf{79.7} & \underline{96.2} & 75.2 & 83.6 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o\\channel attention {[}32{]}\end{tabular} & 84.1 & 78.8 & 96.1 & 75.9 & 83.7 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w\\series attentions{[}52{]}\end{tabular} & 82.3 & 78.9 & 95.6 & \textbf{82.2} & \underline{84.7} \\ \hline
% PAFDR (ours) & \textbf{84.8} & \underline{79.5} & \textbf{96.4} & \underline{79.1} & \textbf{84.9} \\ \hline
% \end{tabular}%
% }
% \end{table}



% % ￥自适应大小表格
% \begin{table}[]
% \caption{Study on the channel and spatial attention.}
% \resizebox{\linewidth}{!}{
% \begin{tabular}{ccccc}
% \hline
% \multicolumn{1}{c|}{\multirow{2}{*}{Method}}     & \multicolumn{2}{c|}{M→D}                                               & \multicolumn{2}{c}{D→M}                                   \\ \cline{2-5} 
% \multicolumn{1}{c|}{}                            & \multicolumn{1}{c|}{Rank-1}        & \multicolumn{1}{c|}{mAP}           & \multicolumn{1}{c|}{Rank-1}        & mAP                  \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\ attention\end{tabular}}         & \multicolumn{1}{c|}{44.2}          & \multicolumn{1}{c|}{25.6}          & \multicolumn{1}{c|}{37.6}          & 26.8                 \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\ spatial attention\end{tabular}} & \multicolumn{1}{c|}{55.3}          & \multicolumn{1}{c|}{33.4}          & \multicolumn{1}{c|}{66.7}          & 34.5                 \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\ channel attention\end{tabular}} & \multicolumn{1}{c|}{55.6}          & \multicolumn{1}{c|}{33.8}          & \multicolumn{1}{c|}{66.9}          & 35.1                 \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w\\ series attentions\end{tabular}} & \multicolumn{1}{c|}{56.7}          & \multicolumn{1}{c|}{35.5}          & \multicolumn{1}{c|}{68.4}          & 36.7                 \\ \hline
% \multicolumn{1}{c|}{PAFDR}                & \multicolumn{1}{c|}{\textbf{57.2}} & \multicolumn{1}{c|}{\textbf{36.4}} & \multicolumn{1}{c|}{\textbf{69.6}} & \textbf{37.8}        \\ \hline
% \end{tabular}}
% \end{table}

% \begin{table}[]
% \caption{Study on the channel and spatial attention.}
% \begin{tabular}{ccccc}
% \hline
% \multicolumn{1}{c|}{\multirow{2}{*}{Method}}     & \multicolumn{2}{c|}{M→D}                                               & \multicolumn{2}{c}{D→M}                                   \\ \cline{2-5} 
% \multicolumn{1}{c|}{}                            & \multicolumn{1}{c|}{Rank-1}        & \multicolumn{1}{c|}{mAP}           & \multicolumn{1}{c|}{Rank-1}        & mAP                  \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\ attention\end{tabular}}         & \multicolumn{1}{c|}{44.2}          & \multicolumn{1}{c|}{25.6}          & \multicolumn{1}{c|}{37.6}          & 26.8                 \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\ spatial attention\end{tabular}} & \multicolumn{1}{c|}{55.3}          & \multicolumn{1}{c|}{33.4}          & \multicolumn{1}{c|}{66.7}          & 34.5                 \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\ channel attention\end{tabular}} & \multicolumn{1}{c|}{55.6}          & \multicolumn{1}{c|}{33.8}          & \multicolumn{1}{c|}{66.9}          & 35.1                 \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w\\ series attentions\end{tabular}} & \multicolumn{1}{c|}{56.7}          & \multicolumn{1}{c|}{35.5}          & \multicolumn{1}{c|}{68.4}          & 36.7                 \\ \hline
% \multicolumn{1}{c|}{PAFDR}                & \multicolumn{1}{c|}{\textbf{57.2}} & \multicolumn{1}{c|}{\textbf{36.4}} & \multicolumn{1}{c|}{\textbf{69.6}} & \textbf{37.8}        \\ \hline
% \end{tabular}
% \end{table}

\subsubsection{\textbf{Effect of the asymmetric task-relevant feature decomposition loss $\mathcal{L}_{ATFD}$}}

\noindent \textbf{Influence of the asymmetric task-relevant feature decomposition (ATFD) loss $\mathcal{L}_{ATFD}$.}
We conduct ablation experiments to evaluate our proposed ATFD loss, $\mathcal{L}_{ATFD}$. As shown in the table, we analyze its components across two datasets (PACS and Office-Home).
Removing the entire $\mathcal{L}_{ATFD}$ loss (PAFDR w/o $\mathcal{L}_{ATFD}$) significantly drops performance (average accuracy by 5.4 on PACS and 6.7 on Office-Home). Investigating individual components, removing either the Enhancement loss (PAFDR w/o $\mathcal{L}_{ATFD}^{+}$) or the Suppression loss (PAFDR w/o $\mathcal{L}_{ATFD}^{-}$) also decreases performance, indicating both are indispensable and their synergy is crucial.
Additionally, we compare our asymmetric $\mathcal{L}_{ATFD}$ with a traditional baseline loss $\mathcal{L}_{baseline}$. Our $\mathcal{L}_{ATFD}$ shows consistent improvements across all metrics over $\mathcal{L}_{baseline}$ (average accuracy improvements of 1.7 on PACS and 1.3 on Office-Home).
These results verify that each component of our ATFD loss contributes to overall performance, with the complete loss achieving the best results in domain generalization, demonstrating the superiority of our asymmetric design. ATFD loss employs asymmetric constraints, enabling different features to match their most suitable comparison objects, thereby maximally promoting the decomposition of task-relevant and task-irrelevant features.






% To investigate the effectiveness of the asymmetric task-relevant feature decomposition (ATFD) loss, we conducted detailed ablation experiments on both PACS and Office-Home datasets. We designed four comparative experiments: a model with $\mathcal{L}_{ATFD}$ completely removed, a model with only negative decomposition loss $\mathcal{L}_{ATFD}^{-}$ retained, a model with only positive decomposition loss $\mathcal{L}_{ATFD}^{+}$ retained, and a model using the baseline loss function $\mathcal{L}_{baseline}$.

% On the PACS dataset, experimental results show that the complete PAFDR model achieved the best average accuracy of 86.3. When $\mathcal{L}_{ATFD}$ was completely removed, model performance significantly decreased to 80.9, demonstrating the crucial contribution of $\mathcal{L}_{ATFD}$ to model performance. Analyzing the positive and negative decomposition losses separately, we found that removing either component led to performance degradation: accuracy dropped to 82.6 when $\mathcal{L}_{ATFD}^{+}$ was removed, and to 82.7 when $\mathcal{L}_{ATFD}^{-}$ was removed. This indicates that both decomposition loss terms play indispensable roles in feature extraction. Notably, when using $\mathcal{L}_{baseline}$, the model achieved an accuracy of 84.6, which, while better than single-loss variants, still fell short of our complete ATFD structure, validating the superiority of $\mathcal{L}_{ATFD}$ in feature decomposition.

% On the Office-Home dataset, we observed similar performance trends. The complete PAFDR model achieved the best average accuracy of 69.7, while performance significantly dropped to 63.0 when $\mathcal{L}_{ATFD}$ was removed. Removing $\mathcal{L}_{ATFD}^{+}$ or $\mathcal{L}_{ATFD}^{-}$ led to accuracy decreases to 66.4 and 66.8 respectively, again confirming the necessity of both loss terms. The model using $\mathcal{L}_{baseline}$ achieved an accuracy of 68.4, also lower than our complete model.

% Through these detailed ablation experiments, we can conclude that: $\mathcal{L}_{ATFD}$ plays a crucial role in improving model performance, bringing significant improvements of 5.4 and 6.7 percentage points on PACS and Office-Home datasets respectively; the synergistic effect of $\mathcal{L}_{ATFD}^{+}$ and $\mathcal{L}_{ATFD}^{-}$ is crucial for effective feature decomposition, with performance degrading when either component is missing; our proposed ATFD structure demonstrates stronger feature decomposition capabilities compared to $\mathcal{L}_{baseline}$, which not only confirms the importance of asymmetric feature decomposition in domain generalization tasks but also validates the rationality and effectiveness of our loss function design.

\subsubsection{\textbf{Optimal Integration Strategy for PAFDR Module.}}
We investigate the impact of inserting the PAFDR module at various locations within ResNet18, specifically after the final layer of each convolutional block (stage-1 through stage-4, see Fig. 2(a)), as well as the effect of applying it to all four stages simultaneously. The results in Table VI indicate that, PAFDR consistently improves performance regardless of the stage where it is placed. Notably, the most significant improvement is observed when PAFDR is incorporated into every stage of the network.

\begin{table}[htbp] 
 \centering 
 \caption{Ablation study on which stage to add PAFDR.} 
 \label{tab:ablation_snr} 
 \begin{tabular}{c|ccccc} 
 \hline 
 \multirow{2}{*}{Method} & \multicolumn{5}{c}{PACS} \\ 
 \cline{2-6} 
 & Art & Cat & Pho & Skt & Avg \\ 
 \hline 
 Baseline (SNR) & 80.3 & 78.2 & 94.5 & 74.1 & 81.8 \\ 
 stage-1 & 82.1 & 79.5 & 95.2 & 75.8 & 83.2 \\ 
 stage-2 & 81.7 & 80.1 & 95.0 & 76.3 & 83.3 \\ 
 stage-3 & 82.5 & 79.8 & 95.3 & 76.7 & 83.6 \\ 
 stage-4 & 82.0 & 80.3 & 95.1 & 76.5 & 83.5 \\ 
 \textbf{stage-all} & \textbf{85.2} & \textbf{82.9}    & \textbf{96.8} &  \textbf{80.4}    & \textbf{86.3} \\ 
 \hline 
 \end{tabular} 
 \end{table} 

 \begin{figure*}[!t]
    \centering
    \includegraphics[width=1\textwidth]{imgs/vis_activation_0625.pdf}
    \vspace{-10pt}
    \caption{
    %大象的第二个激活图不太行。
  ($\textrm{I}$) The activation maps of different features within a PAFDR module (PAFDR 3) show that PAFDR can well separate task-relevant/irrelevant features. The enhanced feature $\tilde{F}^{+}$ has better discriminability than the original feature $F$, while the contaminated feature $\tilde{F}^{-}$ has worse discriminability than the normalized feature $\tilde{F}$. ($\textrm{II}$) Activation maps of our proposed method (bottom) and the baseline (top) correspond to changes in image backgrounds and properties (taking color changes as an example). Our maps are more robust to changes in image background and properties.
    }
    \label{fig:vis_feat}
    \vspace{-10pt}
\end{figure*}

\begin{figure}[t]
  \centering
  \includegraphics[width=1\linewidth]{imgs/t-SNE_Digit-Five-0626.pdf}
  \caption{
  T-SNE visualization of feature distributions on the digit-five dataset for unsupervised domain adaptation (UDA) classification tasks. The figure presents a comparison between our DAFDR method and the baseline approach (SNR).}
  \label{fig:onecol}
\end{figure}


% \subsubsection{\textbf{Effect of the asymmetric task-relevant feature decomposition loss $\mathcal{L}_{ATFD}$}}

% To investigate the effectiveness of the asymmetric task-relevant feature decomposition (ATFD) loss, we conducted detailed ablation experiments on both PACS and Office-Home datasets. We designed four comparative experiments: a model with ATFD loss completely removed, a model with only negative decomposition loss retained, a model with only positive decomposition loss retained, and a model using the baseline loss function from PAFDR.

% On the PACS dataset, experimental results show that the complete PAFDR model achieved the best average accuracy of 84.9. When ATFD loss was completely removed, model performance significantly decreased to 81.2, demonstrating the crucial contribution of ATFD loss to model performance. Analyzing the positive and negative decomposition losses separately, we found that removing either component led to performance degradation: accuracy dropped to 82.6 when positive decomposition loss was removed, and to 82.7 when negative decomposition loss was removed. This indicates that both decomposition loss terms play indispensable roles in feature extraction. Notably, when using PAFDR's baseline loss function, the model achieved an accuracy of 84.3, which, while better than single-loss variants, still fell short of our complete ATFD structure, validating the superiority of ATFD loss function in feature decomposition.

% On the Office-Home dataset, we observed similar performance trends. The complete PAFDR model achieved the best average accuracy of 69.2, while performance significantly dropped to 63.0 when ATFD loss was removed. Removing positive or negative decomposition loss led to accuracy decreases to 66.4 and 66.8 respectively, again confirming the necessity of both loss terms. The model using PAFDR baseline loss function achieved an accuracy of 68.4, also lower than our complete model.

% Through these detailed ablation experiments, we can conclude that: ATFD loss function plays a crucial role in improving model performance, bringing significant improvements of 3.7 and 6.2 percentage points on PACS and Office-Home datasets respectively; the synergistic effect of positive and negative decomposition losses is crucial for effective feature decomposition, with performance degrading when either component is missing; our proposed ATFD structure demonstrates stronger feature decomposition capabilities compared to PAFDR's baseline loss function, which not only confirms the importance of asymmetric feature decomposition in domain generalization tasks but also validates the rationality and effectiveness of our loss function design.


% This loss consists of an enhancement loss $\mathcal{L}_{ATFD}^{+}$ and a suppression loss $\mathcal{L}_{ATFD}^{-}$. In the Market1501→Duke, the complete PAFDR with $\mathcal{L}_{ATFD}$ achieves the best performance, with a 3.5\% increase in Rank-1 accuracy and a 6.2\% improvement in mAP compared to the scheme without this loss. In the Duke→ Market1501, the complete PAFDR with $\mathcal{L}_{ATFD}$ also achieves the best performance, with a 2.8\% increase in Rank-1 accuracy and a 2.2\% improvement in mAP compared to the scheme without this loss. Experimental results show that the asymmetric task-relevant feature decomposition loss effectively helps the model to decompose task-relevant and task-irrelevant features. Moreover, both the enhancement loss $\mathcal{L}_{ATFD}^{+}$ and suppression loss $\mathcal{L}_{ATFD}^{-}$ are indispensable, and their synergistic effect is the key to the model's good performance.
% We conduct ablation experiments to evaluate the effectiveness of our proposed asymmetric task-relevant feature decomposition (ATFD) loss. As shown in Table 4, we analyze different components of the loss function across two DG  scenarios: Market-1501 to DukeMTMC- (M→D) and DukeMTMC- to Market-1501 (D→M).

% When removing the entire ATFD loss ($\mathcal{L}_{ATFD}$), the performance drops significantly, with Rank-1 accuracy decreasing by 4.1 (from 57.2 to 53.1) on M→D and 4.9 (from 69.6 to 64.7) on D→M. We further investigate the impact of individual components. Removing the enhancement loss ($\mathcal{L}_{ATFD}^{+}$) leads to a performance drop of 2.7 and 4.6 in Rank-1 accuracy on M→D and D→M, respectively. Similarly, without the suppression loss ($\mathcal{L}_{ATFD}^{-}$), the model shows decreased performance with Rank-1 accuracy dropping to 55.3 on M→D and 66.2 on D→M. Both the enhancement loss $\mathcal{L}_{ATFD}^{+}$ and suppression loss $\mathcal{L}_{ATFD}^{-}$ are indispensable, and their synergistic effect is the key to the model's good performance.

% Additionally, we also compared with the baseline's loss function $\mathcal{L}_{baseline}$, which adopts a symmetric design. Compared to $\mathcal{L}_{baseline}$, our complete ATFD loss $\mathcal{L}_{ATFD}$ demonstrates consistent improvements across all metrics, with gains of 0.4 in Rank-1 accuracy and 1.5 in mAP on M→D, and 1.2 in Rank-1 accuracy and 1.6 in mAP on D→M. These results verify that each component of our ATFD loss contributes to the overall performance, with the complete loss function achieving the best results in cross-domain  . The results demonstrate the superiority of our asymmetric design in the Loss function. The asymmetric loss function can impose the most appropriate constraints on different features, thereby promoting more thorough decomposition of task-related and task-irrelevant features while enhancing feature discriminability.


% %%自适应大小表格
% \begin{table}[]
% \caption{Study on the asymmetric task-relevant feature decomposition loss.}
% \resizebox{\linewidth}{!}{
% \begin{tabular}{c|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{M→D} & \multicolumn{2}{c}{D→M} \\ \cline{2-5} 
%                         & \multicolumn{1}{c|}{Rank-1} & mAP & \multicolumn{1}{c|}{Rank-1} & mAP \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}$ & \multicolumn{1}{c|}{53.1} & 28.6 & \multicolumn{1}{c|}{64.7} & 33.6 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{+}$ & \multicolumn{1}{c|}{54.5} & 32.4 & \multicolumn{1}{c|}{65.0} & 34.5 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{-}$ & \multicolumn{1}{c|}{55.3} & 33.3 & \multicolumn{1}{c|}{66.2} & 35.7 \\ \hline
% PAFDR w $\mathcal{L}_{baseline}$ & \multicolumn{1}{c|}{56.8} & 34.9 & \multicolumn{1}{c|}{68.4} & 36.2 \\ \hline
% PAFDR & \multicolumn{1}{c|}{\textbf{57.2}} & \textbf{36.4} & \multicolumn{1}{c|}{\textbf{69.6}} & \textbf{37.8} \\ \hline
% \end{tabular}}
% \end{table}

% \begin{table}[]
% \caption{Study on the asymmetric task-relevant feature decomposition loss.}
% \begin{tabular}{c|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{M→D} & \multicolumn{2}{c}{D→M} \\ \cline{2-5} 
%                         & \multicolumn{1}{c|}{Rank-1} & mAP & \multicolumn{1}{c|}{Rank-1} & mAP \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}$ & \multicolumn{1}{c|}{53.1} & 28.6 & \multicolumn{1}{c|}{64.7} & 33.6 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{+}$ & \multicolumn{1}{c|}{54.5} & 32.4 & \multicolumn{1}{c|}{65.0} & 34.5 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{-}$ & \multicolumn{1}{c|}{55.3} & 33.3 & \multicolumn{1}{c|}{66.2} & 35.7 \\ \hline
% PAFDR w $\mathcal{L}_{baseline}$ & \multicolumn{1}{c|}{56.8} & 34.9 & \multicolumn{1}{c|}{68.4} & 36.2 \\ \hline
% PAFDR & \multicolumn{1}{c|}{\textbf{57.2}} & \textbf{36.4} & \multicolumn{1}{c|}{\textbf{69.6}} & \textbf{37.8} \\ \hline
% \end{tabular}
% \end{table}


% This loss consists of an enhancement loss $\mathcal{L}_{ATFD}^{+}$ and a suppression loss $\mathcal{L}_{ATFD}^{-}$. In the Market1501→Duke, the complete PAFDR with $\mathcal{L}_{ATFD}$ achieves the best performance, with a 3.5\% increase in Rank-1 accuracy and a 6.2\% improvement in mAP compared to the scheme without this loss. In the Duke→ Market1501, the complete PAFDR with $\mathcal{L}_{ATFD}$ also achieves the best performance, with a 2.8\% increase in Rank-1 accuracy and a 2.2\% improvement in mAP compared to the scheme without this loss. Experimental results show that the asymmetric task-relevant feature decomposition loss effectively helps the model to decompose task-relevant and task-irrelevant features. Moreover, both the enhancement loss $\mathcal{L}_{ATFD}^{+}$ and suppression loss $\mathcal{L}_{ATFD}^{-}$ are indispensable, and their synergistic effect is the key to the model's good performance.
% We conduct ablation experiments to evaluate the effectiveness of our proposed asymmetric task-relevant feature decomposition (ATFD) loss. As shown in Table 4, we analyze different components of the loss function across two DG  scenarios: Market-1501 to DukeMTMC- (M→D) and DukeMTMC- to Market-1501 (D→M).

% When removing the entire ATFD loss ($\mathcal{L}_{ATFD}$), the performance drops significantly, with Rank-1 accuracy decreasing by 4.1 (from 57.2 to 53.1) on M→D and 4.9 (from 69.6 to 64.7) on D→M. We further investigate the impact of individual components. Removing the enhancement loss ($\mathcal{L}_{ATFD}^{+}$) leads to a performance drop of 2.7 and 4.6 in Rank-1 accuracy on M→D and D→M, respectively. Similarly, without the suppression loss ($\mathcal{L}_{ATFD}^{-}$), the model shows decreased performance with Rank-1 accuracy dropping to 55.3 on M→D and 66.2 on D→M. Both the enhancement loss $\mathcal{L}_{ATFD}^{+}$ and suppression loss $\mathcal{L}_{ATFD}^{-}$ are indispensable, and their synergistic effect is the key to the model's good performance.

% Additionally, we also compared with the baseline's loss function $\mathcal{L}_{baseline}$, which adopts a symmetric design. Compared to $\mathcal{L}_{baseline}$, our complete ATFD loss $\mathcal{L}_{ATFD}$ demonstrates consistent improvements across all metrics, with gains of 0.4 in Rank-1 accuracy and 1.5 in mAP on M→D, and 1.2 in Rank-1 accuracy and 1.6 in mAP on D→M. These results verify that each component of our ATFD loss contributes to the overall performance, with the complete loss function achieving the best results in cross-domain  . The results demonstrate the superiority of our asymmetric design in the Loss function. The asymmetric loss function can impose the most appropriate constraints on different features, thereby promoting more thorough decomposition of task-related and task-irrelevant features while enhancing feature discriminability.


% %%自适应大小表格
% \begin{table}[]
% \caption{Study on the asymmetric task-relevant feature decomposition loss.}
% \resizebox{\linewidth}{!}{
% \begin{tabular}{c|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{M→D} & \multicolumn{2}{c}{D→M} \\ \cline{2-5} 
%                         & \multicolumn{1}{c|}{Rank-1} & mAP & \multicolumn{1}{c|}{Rank-1} & mAP \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}$ & \multicolumn{1}{c|}{53.1} & 28.6 & \multicolumn{1}{c|}{64.7} & 33.6 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{+}$ & \multicolumn{1}{c|}{54.5} & 32.4 & \multicolumn{1}{c|}{65.0} & 34.5 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{-}$ & \multicolumn{1}{c|}{55.3} & 33.3 & \multicolumn{1}{c|}{66.2} & 35.7 \\ \hline
% PAFDR w $\mathcal{L}_{baseline}$ & \multicolumn{1}{c|}{56.8} & 34.9 & \multicolumn{1}{c|}{68.4} & 36.2 \\ \hline
% PAFDR & \multicolumn{1}{c|}{\textbf{57.2}} & \textbf{36.4} & \multicolumn{1}{c|}{\textbf{69.6}} & \textbf{37.8} \\ \hline
% \end{tabular}}
% \end{table}

% \begin{table}[]
% \caption{Study on the asymmetric task-relevant feature decomposition loss.}
% \begin{tabular}{c|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{M→D} & \multicolumn{2}{c}{D→M} \\ \cline{2-5} 
%                         & \multicolumn{1}{c|}{Rank-1} & mAP & \multicolumn{1}{c|}{Rank-1} & mAP \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}$ & \multicolumn{1}{c|}{53.1} & 28.6 & \multicolumn{1}{c|}{64.7} & 33.6 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{+}$ & \multicolumn{1}{c|}{54.5} & 32.4 & \multicolumn{1}{c|}{65.0} & 34.5 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{-}$ & \multicolumn{1}{c|}{55.3} & 33.3 & \multicolumn{1}{c|}{66.2} & 35.7 \\ \hline
% PAFDR w $\mathcal{L}_{baseline}$ & \multicolumn{1}{c|}{56.8} & 34.9 & \multicolumn{1}{c|}{68.4} & 36.2 \\ \hline
% PAFDR & \multicolumn{1}{c|}{\textbf{57.2}} & \textbf{36.4} & \multicolumn{1}{c|}{\textbf{69.6}} & \textbf{37.8} \\ \hline
% \end{tabular}
% \end{table}





% \begin{figure*}[!t]
%     \centering
%     \includegraphics[width=0.7\textwidth]{imgs/activation_out_0527.pdf}
%     \vspace{-6pt}
%     \caption{
%     Activation maps of our proposed method (bottom) and the baseline (top) correspond to changes in image backgrounds and properties (taking color changes as an example). Our maps are more robust to changes in image background and properties.
%     % Activation maps of our proposed method (bottom) and the baseline (top) correspond to images of different backgrounds and attributes (taking color temperature variations as an example). Our maps are more consistent/invariant to background variations and changes in image attributes.
%     }
%     \label{fig:vis_attn}
%     \vspace{-10pt}
% \end{figure*}

% \begin{figure*}[!t]
%     \centering
%     \includegraphics[width=0.64\textwidth]{fig/Vis_attn_new.pdf}
%     \vspace{-6pt}
%     \caption{
%     \textbf{Attention Visualization in different methods on ImageNet.} 
%     The vanilla attention in ViT fails to accurately outline the target objects, leading to fragments and artifacts manifested as abnormally high values for certain tokens. 
%     The proposed topology-aware HGA in HGFormer perfectly outlines target objects, significantly distinguishing between background and foreground.
%     % By contrast, the HGA exhibits superior capacity in regional context and spatial topology.
%     \textit{
%     Zoom in for better view.
%     }
%     }
%     \label{fig:vis_attn}
%     \vspace{-12pt}
% \end{figure*}


\subsection{Visualization}
\label{sec:vis}

% \begin{figure*}[!t]
%     \centering
%     \includegraphics[width=0.64\textwidth]{fig/plot_feat.pdf}
%     \vspace{-10pt}
%     \caption{
%     \textbf{Feature Visualization in different methods on ImageNet}. 
%     (a) Original Image. 
%     (b) ViT. 
%     (c) ViHGNN.
%     (d) HGFormer(Ours).
%     ViT blends the foreground with the background ambiguously. 
%     ViHGNN distinguishes between foreground and background, but its portrayals of objects is rough and unclear. 
%     HGFormer(Ours) significantly highlights the foreground and suppresses the background, achieving a detailed depiction of objects.
%     Note that the parameters and computation cost in these counterparts are similar. 
%     \textit{
%     Zoom in for better view.
%     }
%     }
%     \label{fig:vis_feat}
%     \vspace{-10pt}
% \end{figure*}




\subsubsection{\textbf{Visualization of Feature Map}}
To better understand the working mechanism of the PAFDR module, we visualize the intermediate feature maps of the third PAFDR module (PAFDR3).

Figure 3 ($\textrm{I}$) shows the activation maps of the original feature $\tilde{F}$, normalized feature Fe, enhanced feature 
$\tilde{F}^{+}=\tilde{F}+R^{+}$
and contaminated feature $\tilde{F}^{-}=\tilde{F}+R^{-}$. We observe that the enhanced feature $\tilde{F}^{+}$ recovered with task-relevant feature $R^{+}$ responds highly to the regions and can better capture discriminative areas. In contrast, the contaminated feature $\tilde{F}^{-}$, after adding task-irrelevant feature $R^{-}$, exhibits high responses mainly on the background. The enhanced feature shows better response to  object than the original feature, while the contaminated feature shows worse response to  object than the normalized feature.

In Figure 3 ($\textrm{II}$), we further compare the activation maps $\tilde{F}^{+}$ from our approach and those from the baseline method by varying the background and attributes (e.g., color) of input images. We can observe that for images with different backgrounds and attributes, our approach's activation maps remain more consistent/invariant compared to the baseline's activation maps. In contrast, while the baseline's activation maps are relatively resilient to style variations, they become chaotic when the background changes. These findings demonstrate that our approach is more robust to variations in background and attributes. This demonstrates the superiority of our method's spatial and channel attention in jointly extracting domain-invariant features.
% This figure demonstrates the comparison of activation maps between our proposed PAFDR method and the baseline approach when handling image variations. Through experiments across three scenarios - original images, temperature changes, and background variations - the results show that activation maps generated by PAFDR exhibit stronger consistency and invariance under different conditions. Notably, when image backgrounds are altered or image attributes change, our method maintains better focus on the main subject while reducing background interference, thus proving its superior robustness in handling image variations.

% Figure 4 visualizes the activation maps of different features within the PAFDR module. Through the analysis of three different input images, we demonstrate how the model effectively separates task-relevant and task-irrelevant features. From left to right, we show the input image, feature map F, enhanced feature map F̃⁺, task-relevant feature map F̃⁻, and task-irrelevant feature map F̃. The heat maps indicate that our PAFDR module successfully identifies and separates different types of features, where F̃⁻ primarily focuses on task-relevant regions (such as facial features), while F̃ captures more task-irrelevant information (such as clothing and background). These results confirm that the ATFD loss function plays its expected role in the feature separation process.

% To visually demonstrate the feature extraction capability of the PAFDR module, we conducted a visualization analysis of the feature response of the third PAFDR module. Figure 3(b) shows a comparison of the response maps of the original feature $F$ and the enhanced feature $\tilde{F}^{+}$ after adding the task-relevant feature. The results show that the enhanced feature $\tilde{F}^{+}$ has a stronger response in the discriminative areas of the  object, which indicates that the addition of the task-relevant feature indeed enhances the model's perception of task features. And our ATFD loss also plays the expected role.




\subsubsection{\textbf{Visualization of Feature distribution}}
In Figure 4, we visualize feature distributions using t-SNE [65] on the Digit-5 dataset, with mm, mt, sv, and syn as source domains and up as the target domain. By projecting high-dimensional features onto a two-dimensional space, we compare the feature distributions between the baseline  method (SNR) and our proposed PAFDR. The visualization clearly demonstrates that PAFDR achieves better class separation in the feature space, with more compact clustering within classes and clearer boundaries between them, indicating that our approach learns more discriminative feature representations.
% We visualize the feature distributions at the third PAFDR module of the network using t-SNE \cite{van2008visualizing}. They represent the feature distributions of input $F$,  normalized feature $\tilde{F}$, and output 

% As shown in Figure 4 ($\textrm{I}$), in the left subplot, before PAFDR, features extracted from the two datasets ("red": source training dataset Duke; "green": unseen target dataset Market1501) are largely separated and show a clear domain gap. In the middle subplot, within the PAFDR module, this domain gap has been eliminated. In the right subplot, after recovering the task-relevant features, there remains no domain gap between the two datasets.

% As shown in Figure 4 ($\textrm{II}$), the points in different colors and shapes represent  samples of different identities from Market 1501. In the left subplot,  clusters of the same task maintain certain cohesion, and there are gaps between  clusters of different identities. In the middle subplot, after processing by BN and IN,  clusters of the same task become scattered, and the gaps between  clusters of different identities disappear. In the right subplot, after recovering the task-relevant features,  clusters of the same task show stronger cohesion than the original features, and the gaps between  clusters of different identities are larger than in the original features.

% Overall, Figures 5 and 6 jointly demonstrate that our PAFDR can maintain discriminability while enhancing generalization.
% In Figure 3, we visualize the feature distributions from the third PAFDR module of the network using t-SNE. From top to bottom, they represent the input $F$, normalized features $\tilde{F}$, and output $\tilde{F}^{+}$ distributions of the PAFDR module. The original features $F$ from the two datasets ("green": source training dataset Duke; "red": unseen target dataset Market1501) show a clear domain gap. After normalization, the domain gap in features $\tilde{F}$ is eliminated, however, the discriminability is also reduced. After being enhanced by task-related features, $\tilde{F}^{+}$ shows reduced domain gap in feature distribution while preserving discriminability.
% Figure 5 illustrates the distribution visualization of intermediate features before, within, and after the PAFDR module processing. Using t-SNE dimensionality reduction, we visualize sample distributions from the source dataset Duke (red nodes) and the target dataset Market1501 (green nodes). The three subfigures from left to right show the distributions of original features F, intermediate features F̃, and enhanced features F̃⁺. The results demonstrate that through PAFDR module processing, the feature distributions gradually change, ultimately achieving better feature alignment and domain adaptation in F̃⁺, indicating that our method effectively reduces feature discrepancies between source and target domains.
% Figure 6 demonstrates the evolution of feature distributions for  samples with different identities from the Market1501 dataset through the PAFDR module. Using t-SNE visualization, we illustrate the feature distributions at three stages: original features F, intermediate features F̃, and enhanced features F̃⁺. Points in different colors represent  samples of different identities. The visualization reveals significant changes in sample distribution as features progress through the PAFDR module: from initially scattered distributions in F, through reorganization in F̃, to finally forming more compact and distinct task clusters in F̃⁺. This indicates that the PAFDR module effectively enhances task-relevant features and improves the separability between different identities.

% \begin{figure}[t]
%   \centering
%   \includegraphics[width=1\linewidth]{imgs/t-SNE_Digit-Five-0614.pdf}
%   \caption{%这张图中的a,b序号与图注的1、2不对应。这个图效果一般，可以把子图一换的差一点，或者还是仿照会议论文放两张图
%   T-SNE visualization of feature distributions on the digit-five dataset for unsupervised domain adaptation (UDA) classification tasks. The figure presents a comparison between our DAFDR method and the baseline approach (SNR).}
%   \label{fig:onecol}
% \end{figure}





% \begin{figure}[t]
%   \centering
%   \includesvg[width=1\linewidth]{samples/pictures/vis_tsne_0224_6.svg}
%   \caption{($\textrm{I}$) Visualization of distributions...}
%   \label{fig:onecol}
% \end{figure}

% \begin{figure}[t]
%   \centering
%   \includegraphics[width=1\linewidth]{}
%   \caption{Visualization of distributions of the intermediate features using the t - SNE tool before/within/after the PAFDR module. The points in different colors and shapes represent  samples of different identities from Market 1501.}
%   \label{fig:onecol}
% \end{figure}








% %自适应大小表格
% \begin{table*}[]
% \caption{ Comparison with existing methods on multi-source DG .}         %表标题
% \resizebox{\linewidth}{!}{
% \begin{tabular}{c|cc|cc|cc|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{MS+D+C→M}                      & \multicolumn{2}{c|}{MS+M+C→D}                      & \multicolumn{2}{c|}{M+D+C→MS}                      & \multicolumn{2}{c|}{MS+M+D→C}                      & \multicolumn{2}{c}{Average}                        \\ \cline{2-11} 
%                         & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           \\ \hline

% OSNet \cite{zhou2019osnet}                  & \multicolumn{1}{c|}{72.5}          & 44.2          & \multicolumn{1}{c|}{65.2}          & 47.0          & \multicolumn{1}{c|}{33.2}          & 12.6          & \multicolumn{1}{c|}{23.9}          & 23.3          & \multicolumn{1}{c|}{48.7}          & 31.8          \\ \hline
% QAConv \cite{liao2020interpretable}                 & \multicolumn{1}{c|}{68.6}          & 39.5          & \multicolumn{1}{c|}{64.9}          & 43.4          & \multicolumn{1}{c|}{29.9}          & 10.0          & \multicolumn{1}{c|}{22.9}          & 19.2          & \multicolumn{1}{c|}{46.6}          & 28.0          \\ \hline
% % OSNet-IBN \cite{zhou2022learning}              & \multicolumn{1}{c|}{73.0}          & 44.9          & \multicolumn{1}{c|}{64.6}          & 45.7          & \multicolumn{1}{c|}{39.8}          & 16.2          & \multicolumn{1}{c|}{25.7}          & 25.4          & \multicolumn{1}{c|}{50.8}          & 33.0          \\ \hline
% DAML \cite{shu2021open}                   & \multicolumn{1}{c|}{75.5}          & 49.3          & \multicolumn{1}{c|}{66.5}          & 47.6          & \multicolumn{1}{c|}{32.2}          & 12.6          & \multicolumn{1}{c|}{29.8}          & 29.3          & \multicolumn{1}{c|}{51.0}          & 34.7          \\ \hline
% PAFDR \cite{jin2021style}                    & \multicolumn{1}{c|}{75.2}          & 48.5          & \multicolumn{1}{c|}{66.7}          & 48.3          & \multicolumn{1}{c|}{35.1}          & 13.8          & \multicolumn{1}{c|}{29.1}          & 29.0          & \multicolumn{1}{c|}{51.5}          & 34.9          \\ \hline
% M$^{3}$L \cite{zhao2021learning}                    & \multicolumn{1}{c|}{76.5}          & 51.1          & \multicolumn{1}{c|}{67.1}          & 48.2          & \multicolumn{1}{c|}{32.0}          & 13.1          & \multicolumn{1}{c|}{31.9}          & 30.9          & \multicolumn{1}{c|}{51.9}          & 35.8          \\ \hline
% RaMoE \cite{dai2021generalizable}  & \multicolumn{1}{c|}{82.0} & 56.5 & \multicolumn{1}{c|}{\underline{73.6}} & \textbf{56.9} & \multicolumn{1}{c|}{34.1} & 13.5 & \multicolumn{1}{c|}{36.6} & 35.5 & \multicolumn{1}{c|}{56.6} & 40.6 \\ \hline
% MetaBIN \cite{choi2021meta} & \multicolumn{1}{c|}{\underline{83.2}} & \textbf{61.2} & \multicolumn{1}{c|}{71.3} & 54.9 & \multicolumn{1}{c|}{40.8} & 17.0 & \multicolumn{1}{c|}{\textbf{38.1}} & \underline{37.5} & \multicolumn{1}{c|}{\underline{58.4}} & \underline{42.7} \\ \hline
% OSNet-AIN \cite{zhou2022learning}      & \multicolumn{1}{c|}{73.3}          & 45.8          & \multicolumn{1}{c|}{65.6}          & 47.2          & \multicolumn{1}{c|}{40.2}          & 16.2          & \multicolumn{1}{c|}{27.4}          & 27.1          & \multicolumn{1}{c|}{51.6}          & 34.1          \\ \hline
% MixNorm \cite{qi2022novel} & \multicolumn{1}{c|}{78.9} & 51.4 & \multicolumn{1}{c|}{70.8} & 49.9 & \multicolumn{1}{c|}{\textbf{47.2}} & \underline{19.4} & \multicolumn{1}{c|}{29.6} & 29.0 & \multicolumn{1}{c|}{56.6} & 37.4 \\ \hline
% PAFDR (ours)            & \multicolumn{1}{c|}{\textbf{83.6}} & \underline{57.8} & \multicolumn{1}{c|}{\textbf{73.8}} & \underline{56.4} & \multicolumn{1}{c|}{\underline{41.3}} & \textbf{19.8} & \multicolumn{1}{c|}{\underline{37.3}} & \textbf{37.7} & \multicolumn{1}{c|}{\textbf{58.5}} & \textbf{42.9} \\ \hline
% \end{tabular}}
% \end{table*}





% $\tilde{F}^{+}$ from the PAFDR module.
% \begin{figure}[t]
%   \centering
%   \includegraphics[width=1\linewidth]{imgs/vis_activation_0224_4.pdf}
%   \caption{($\textrm{I}$) Activation maps of our proposed method (bottom) and the baseline (top) correspond to images of different backgrounds and attributes (taking color temperature variations as an example). Our maps are more consistent/invariant to background variations and changes in image attributes. ($\textrm{II}$) The activation maps of different features within a PAFDR module (PAFDR 3) show that PAFDR can well separate task-relevant/irrelevant features. The enhanced feature $\tilde{F}^{+}$ has better discriminability than the original feature $F$, while the contaminated feature $\tilde{F}^{-}$ has worse discriminability than the normalized feature $\tilde{F}$.}
%   \label{fig:onecol}
% \end{figure}


% \begin{figure}[t]
%   \centering
%   \includegraphics[width=1\linewidth]{imgs/vis_tsne_0225.pdf}
%   \caption{($\textrm{I}$) Visualization of distributions of intermediate features before/within/after the PAFDR module using the tool of t-SNE. ‘Red’/‘green’ nodes: samples from source dataset Duke/unseen target dataset Market1501. ($\textrm{II}$) Visualization of distributions of the intermediate features using the t-SNE tool before/within/after the PAFDR module. The points in different colors and shapes represent person samples of different identities from Market 1501.}
%   \label{fig:onecol}
% \end{figure}








\section{Conclusion}
% This paper innovatively reveals and addresses the critical issue of incomplete feature decomposition in existing decomposition-based DG ReID methods, which arises from their exclusive focus on channel decomposition and the use of symmetric loss functions. We propose a novel DG ReID framework with high generalization and discrimination. Its Parallel Attention Feature Decomposition and Recovery (PAFDR) module combines Batch Normalization (BN) and Instance Normalization (IN) to reduce domain gaps and employs parallel spatial and channel attention to more thoroughly decompose and recover Task-Relevant features, compensating for information loss from normalization. Its parallel structure offers a regularization-like effect, enhancing generalization. To address symmetric constraint limitations in existing loss functions, we introduce an Asymmetric Task-Relevant Feature Decomposition (ATFD) loss. ATFD applies asymmetric constraints, enabling features to match suitable comparison targets, promoting thorough decomposition of Task-Relevant and irrelevant features. Experiments show our method outperforms existing approaches in DG ReID tasks.



This paper proposes a novel Parallel Attention-based Feature Decomposition and Recovery (PAFDR) method to address the domain generalization problem in cross-domain  re-identification. Unlike existing methods that only focus on channel-wise feature decomposition, PAFDR achieves more comprehensive decomposition of task-relevant and task-irrelevant features through parallel spatial and channel attention mechanisms. Specifically, this paper first uses Instance Normalization (IN) and Batch Normalization (BN) to eliminate style variations, then extracts and recovers task-relevant features from the removed information through a dual attention mechanism to enhance feature discriminability. Furthermore, the proposed asymmetric task-relevant feature decomposition loss function further promotes effective separation of task-relevant and task-irrelevant features by applying different constraints to features with different discriminative abilities. Extensive experimental results demonstrate that the PAFDR method shows superior domain generalization performance on tasks of different scales. 
This research provides a new approach to solving the domain generalization problem in cross-domain, which has significant implications for improving performance in real-world applications.

\footnotesize
\bibliographystyle{IEEEtran}
% \bibliographystyle{ieeetr} 
% \bibliography{main}
\bibliography{arxiv}



}
\end{document}\documentclass[journal]{IEEEtran}

\usepackage{amsmath,amsfonts}
\usepackage{algorithmic}
\usepackage{algorithm}
\usepackage{array}
\usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
\usepackage{textcomp}
\usepackage{stfloats}
\usepackage{url}
\usepackage{verbatim}
\usepackage{graphicx}
\usepackage{cite}
\usepackage{multirow}
\usepackage{color}
\hyphenation{op-tical net-works semi-conduc-tor IEEE-Xplore}
\usepackage{booktabs}
\usepackage{xcolor}
\usepackage{colortbl}
% updated with editorial comments 8/9/2021

% \usepackage[numbers,sort&compress]{natbib}
% \usepackage[square,sort,comma,numbers]{natbib}

\usepackage{amsthm,amssymb}
\usepackage{mathrsfs}

\begin{document}

{
\twocolumn

\title{Parallel Attention-based Feature Decomposition and Recovery for Domain Generalization and Adaptation}

\author{Hangyuan Yang, Yongfei Zhang\IEEEauthorrefmark{1}
        % <-this % stops a space

\thanks{This work was supported by the project of the State Key Laboratory of Software Development Environment under Grant SKLSDE-2023ZX-18.}
\thanks{Hao Wang and Biao Leng are with School of Computer Science and Engineering, Beihang University, Beijing 100191, China (e-mail: <EMAIL>; <EMAIL>).}
\thanks{Shuo Zhang is with Beijing Key Laboratory of Traffic Data Analysis and Mining, School of Computer Science \& Technology, Beijing 100044, China (e-mail: <EMAIL>).}\thanks{Corresponding author: Biao Leng (<EMAIL>)}}


\markboth{IEEE TRANSACTIONS ON Multimedia}%
{Shell \MakeLowercase{\textit{et al.}}: Bare Demo of IEEEtran.cls for IEEE Journals}

% \IEEEpubid{0000--0000/00\$00.00~\copyright~2021 IEEE}
% Remember, if you use this you must call \IEEEpubidadjcol in the second
% column for its text to clear the IEEEpubid mark.

\maketitle

\begin{abstract}
Deep learning models in computer vision frequently demonstrate excellent results in training environments, but their effectiveness substantially declines when implemented in the real world where the visual characteristics of new images differ from those in the training data.
% The decomposition-based domain generalization (DG)  methods address this problem by decomposing  features into task-relevant and task-irrelevant features. 
% However, existing decomposition-based DG  methods usually focus only on channel-based feature decomposition, neglecting spatial feature decomposition. 
% This results in incomplete decomposition of task-relevant and task-irrelevant features in space, such as the inability to fully separate  features from background features.
% To address this issue, we propose \textbf{Parallel Attention-based Feature Decomposition and Recovery (PAFDR)}, to more thoroughly decompose and recover task-relevant features, enhancing DG  performance. 
% First, we use Instance Normalization (IN) and Batch Normalization (BN) to eliminate style variations.
% However, such a process inevitably eliminates discriminative information.
% For better discriminability, we utilize \textbf{parallel spatial attention and channel attention} to jointly extract task-relevant features from the removed information, and recover them back to the normalized features to enhance the discriminability.
% To better promote the separation of task-relevant and task-irrelevant features, an \textbf{Asymmetric task-relevant Feature Decomposition (ATFD) Loss} is added to ensure that the features after task-relevant feature recovery are more discriminative than the \textbf{original features}, while the features after adding task-irrelevant features are less discriminative than the \textbf{normalized features}.
% Extensive experiments on  
% datasets at different scales demonstrate the superiority of PAFDR in generalization.
% Traditional domain generalization approaches that separate task-related from task-unrelated components typically emphasize channel-based decomposition while overlooking spatial aspects. 
The decomposition-based domain generalization (DG) and Unsupervised domain adaptation (UDA) methods address this problem by decomposing features into task-relevant and task-irrelevant features. 
Nevertheless, current decomposition-based DG and UDA techniques predominantly emphasize channel-wise feature decomposition, overlooking spatial feature decomposition.
% This limitation creates incomplete separation, particularly between subject and background elements.
This limitation leads to incomplete separation, such as between the subject and background.
Our research introduces Parallel Attention-based Feature Decomposition and Recovery (PAFDR) to comprehensively decompose and recover task-relevant features, thereby improving cross-domain performance. 
We initially apply Batch Normalization (BN) and Instance Normalization (IN) to remove stylistic variations.
However, this process also eliminates some discriminative information.
To achieve more effective decomposition, we employ \textbf{parallel spatial attention and channel attention} to collaboratively extract task-related features from the removed data. Subsequently, task-related features are added to the normalized features, thereby boosting the discriminability.
% We further strengthen feature decomposition through our innovative Asymmetric task-relevant Feature Decomposition (ATFD) Loss function. It ensures that features after task-relevant recovery exhibit greater discriminability than \textbf{original features}, while features combined with task-irrelevant features demonstrate reduced discriminability compared to \textbf{normalized features}.
The proposed Asymmetric task-relevant Feature Decomposition (ATFD) Loss function further strengthens feature decomposition by ensuring that features after task-relevant recovery exhibit greater discriminability than \textbf{original features}, while features combined with task-irrelevant features demonstrate reduced discriminability compared to \textbf{normalized features}.
% Comprehensive testing across multiple datasets of varying scales confirms PAFDR's effectiveness in achieving superior generalization performance.
Experimental results demonstrate that our PAFDR effectively enhances network performance in both Domain Generalization (DG) and Unsupervised Domain Adaptation (UDA).
\end{abstract}

\begin{IEEEkeywords}
Image Classification, Vision Transformer, Hypergraph Learning.
\end{IEEEkeywords}

\section{Introduction}
\label{intro}
\IEEEPARstart{A}{lthough} supervised methods have exhibited excellent performance on the training data \cite{zhang2016learning,su2017pose,zhao2017spindle,ge2018fd,qian2018pose,zhang2019densely,jin2020uncertainty}, they often suffer severe performance degradation when deployed in real-world scenarios. 
This phenomenon is primarily caused by domain gaps, that is, the data discrepancy across different scenes.
Specifically, the images from different scenes show obvious differences in attributes (e.g., brightness, color, contrast). Furthermore, the complex backgrounds (e.g., buildings, vegetation and vehicles) pose additional challenges for accurately identifying the target.

In response to this challenge, we focus on domain generalization  \cite{song2019generalizable,muandet2013domain,jia2019frustratingly,li2018learning,shankar2018generalizing}, aiming to develop a method that does not require access to target scene data or model adjustment. 
% The research by \cite{zhou2019omni} demonstrates that adding an IN layer after the model's BN layer \cite{ioffe2015batch} can significantly enhance the model's domain generalization performance \cite{pan2018two}. 
The research by \cite{zhou2019omni} demonstrates that adding an IN layer after the model's BN layer \cite{ioffe2015batch} can significantly reduce domain gaps while improving model generalization.
As illustrated by features $\tilde{F}$ after IN and BN processing in Figure 1, while normalization methods help reduce domain gaps, they can also lead to the loss of some discriminative features \cite{huang2017arbitrary,jin2021style,pan2018two,zhou2019osnet}, thereby impacting performance.

\begin{figure}[t]
    \centering
    \includegraphics[width=1\columnwidth]{imgs/motivation-0318.pdf}
    \vspace{-20pt}
    \caption{
    \textbf{Observation and Motivation}. 
    From left to right, input represents the input image, F represents the activation map of the output features from the backbone network (taking ResNet50 as an example), $\tilde{\mathrm{\textbf{F}}}$ represents the activation map of the features after IN and BN processing, baseline represents the output feature activation map of existing decomposition-based DG   method (taking PAFDR \cite{jin2021style} as an example), and PAFDR represents the output feature activation map of the PAFDR proposed in this paper.
    }
    \vspace{-17pt}
    \label{fig:feat_intro}
\end{figure}

% \begin{figure}[t]
%  \centering
%   \includegraphics[width=0.9\linewidth]{DG-PAFDR/samples/pictures/motivation-0318.pdf}
%    \caption{Observation and Motivation. From left to right, input represents the input image, F represents the activation map of the output features from the backbone network (taking ResNet50 as an example), $\tilde{\mathrm{\textbf{F}}}$ represents the activation map of the features after IN and BN processing, baseline represents the output feature activation map of existing decomposition-based DG   method (taking PAFDR \cite{jin2021style} as an example), and PAFDR represents the output feature activation map of the PAFDR proposed in this paper.}
%    \label{fig:onecol}
% \end{figure}

In response to this challenge, we focus on domain generalization  \cite{song2019generalizable,muandet2013domain,jia2019frustratingly,li2018learning,shankar2018generalizing}, aiming to develop a method that does not require access to target scene data or model adjustment. 
% The research by \cite{zhou2019omni} demonstrates that adding an IN layer after the model's BN layer \cite{ioffe2015batch} can significantly enhance the model's domain generalization performance \cite{pan2018two}. 
The research by \cite{zhou2019omni} demonstrates that adding an IN layer after the model's BN layer \cite{ioffe2015batch} can significantly reduce domain gaps while improving model generalization.
As illustrated by features $\tilde{F}$ after IN and BN processing in Figure 1, while normalization methods help reduce domain gaps, they can also lead to the loss of some discriminative features \cite{huang2017arbitrary,jin2021style,pan2018two,zhou2019osnet}, thereby impacting performance.

Decomposition-based methods aim to decompose features into task-relevant and task-irrelevant components \cite{jin2021style,eom2019learning,zheng2021calibrated}, which can address the loss of discriminative information caused by normalization during task-relevant feature extraction. 
However, existing decomposition-based DG  methods usually focus only on channel-based feature decomposition.
% Typically, the channel of features captures abstract semantic information such as style and category, while the spatial dimension captures details like position, shape, and structure \cite{zhou2016learning}. 
The channel dimension of features encodes abstract semantic information like style and category, while the spatial dimensions preserve detailed characteristics such as position, shape, and structure \cite{zhou2016learning}.
% As shown by features Baseline (PAFDR \cite{jin2021style}) in Figure 1, neglecting spatial decomposition can result in incomplete decomposition of task-relevant and task-irrelevant features in space, such as the inability to fully separate  features from background features.
We use PAFDR \cite{jin2021style}, a representative method in decomposition-based DG, as our baseline. As shown by the feature baseline (output features of PAFDR) in Figure 1, ignoring spatial decomposition leads to incomplete decomposition of task-relevant and task-irrelevant features in space, for example, the inability to fully separate features from background features.
%  needs to focus on the s in images, so the confusion between features and background features severely affects the accuracy and generalization of  models.
The model needs to accurately identify and focus on foreground objects in images, thus incomplete separation between object and background features significantly impairs model accuracy and cross-domain generalization performance.
% Additionally, existing loss functions used for feature decomposition apply the same constraints to features with different discriminability, resulting in insufficient constraints on the feature decomposition process, which further exacerbates the incompleteness of feature decomposition.

\begin{figure*}[!t]
    \centering
    \includegraphics[width=0.98\textwidth]{imgs/overall_0403.pdf}
    \vspace{-8pt}
    \caption{
      \textbf{Overall flowchart.} 
      ($\textrm{I}$) ResNet-50 with the proposed PAFDR module being plugged in after some convolutional blocks. ($\textrm{II}$) Proposed PAFDR module. The combination of BN and IN is used to reduce domain gaps, followed by the recovery of task-relevant features (marked with a solid green line). The branch with a red dashed line is only used for suppression loss and is discarded during inference. The letters $a$ - $i$ in the feature map represent the spatial attention weights of different parts, and the gradient colors represent the channel attention weights. E represents the einheits matrix ($\textrm{III}$) Asymmetric task-relevant Feature Decomposition Loss ensures that features $\tilde{F}^{+}$ (after recovering task-relevant features $R^{+}$) are more discriminative than original features $F$, while features $\tilde{F}^{-}$ (after adding task-irrelevant features $R^{-}$) are less discriminative than normalized features $\tilde{F}$. The ATFD Loss, Enhancement Loss and Suppression Loss marked with black circled letters are calculated within the PAFDR module, while the Total Loss, ID Loss and HardTriplet Loss marked with gray circled letters are calculated outside the PAFDR module.}
        \vspace{-12pt}
    \label{fig:intro}
\end{figure*}
% To address these issues, we propose PAFDR, which introduces parallel spatial attention and channel attention to achieve more thorough decomposition of  image features, and applies asymmetric constraints on features of different discriminability to promote better decomposition of task-relevant and task-irrelevant features. 
To address these issues, we propose PAFDR, which introduces parallel spatial attention and channel attention, and applies asymmetric constraints on features of different discriminability to promote better decomposition of task-relevant and task-irrelevant features.
% Firstly, a combination of BN and IN is used to reduce domain gaps, thereby alleviating domain gaps between image features from different domains. 
Specifically, a combination of BN and IN is used to reduce domain gaps. 
However, this process inevitably removes some discriminative (task-relevant) features. 
As shown by features PAFDR in Figure 1, we utilize parallel spatial attention and channel attention to jointly extract task-relevant features from the removed features and recover them to the normalized features to enhance the discriminability. 
% Because of the different discriminability of different features, we propose an asymmetric task-relevant feature decomposition loss, which imposes different constraints on features with different discriminability.
% It also requires that the features after adding task-relevant features have better discriminability, while the features after adding task-irrelevant features have worse discriminability. 
% Asymmetric task-relevant feature decomposition loss makes the decomposition of task-relevant features and task-irrelevant features more thorough.
To better promote the separation of task-relevant and task-irrelevant features, an Asymmetric task-relevant Feature Decomposition Loss is added to ensure that the features after task-relevant feature recovery are more discriminative than the \textbf{original features}, while the features after adding task-irrelevant features are less discriminative than the \textbf{normalized features}.

% We have validated the generalization of the proposed PAFDR method on multiple widely used benchmarks and settings. Our method outperforms existing DG and UDA methods in both single-source and multi-source DG   experiments. 
We comprehensively evaluated the performance of the PAFDR method across multiple mainstream benchmarks and diverse experimental settings, with results consistently demonstrating that our approach significantly outperforms existing state-of-the-art methods in both Domain Generalization (DG) and Unsupervised Domain Adaptation (UDA) tasks.
In summary, our main contributions are as follows:

\begin{itemize}
\item We propose a feature decomposition method based on parallel spatial and channel attention to address the problem of incomplete decomposition caused by lack of spatial attention. This method jointly extracts task-relevant features from the information lost during normalization and recovers them to the normalized features to enhance discriminability.
% To address the problem of incomplete decomposition caused by lack of spatial attention, we propose a feature decomposition method based on parallel spatial attention and channel attention, which jointly extracts task-relevant features from the information lost in the normalization process and recover them to the normalized features to enhance the discriminability.

% \item In view of the different discriminability of different features, we propose an asymmetric task-relevant feature decomposition loss, which imposes different constraints on features with different discriminability, making the decomposition of task-relevant features and task-irrelevant features more thorough.
\item We propose an Asymmetric task-relevant Feature Decomposition Loss to better promote the separation of task-relevant and task-irrelevant features. This loss ensures that the features after task-relevant feature recovery are more discriminative than \textbf{the original features}, while the features after adding task-irrelevant features are less discriminative than \textbf{the normalized features}.
% To better promote the separation of task-relevant and task-irrelevant features, an Asymmetric task-relevant Feature Decomposition Loss is added to ensure that the features after task-relevant feature recovery are more discriminative than the \textbf{original features}, while the features after adding task-irrelevant features are less discriminative than the \textbf{normalized features}.
% \item We present PAFDR, a comprehensive framework for DG   that incorporates domain gap reduction through BN and IN, a dual attention mechanism combining parallel spatial and channel attention for thorough task-relevant feature extraction and recovery, and an asymmetric task-relevant feature decomposition loss to enhance feature discrimination. Extensive experiments demonstrate the framework's superior generalization capability across different scales of datasets.
\item We propose PAFDR, a versatile module applicable to various network architectures across different vision tasks. By combining BN and IN to reduce domain gaps, employing parallel spatial and channel attention mechanisms for comprehensive feature extraction and recovery, and introducing asymmetric task-relevant feature decomposition loss to enhance feature discriminability, PAFDR not only significantly improves network generalization but also effectively boosts the performance of existing UDA networks. Extensive experiments demonstrate its superior generalization capability across datasets of varying scales.

% We present PAFDR, an effective framework for domain-generalizable  that integrates BN and IN, parallel spatial and channel attention mechanisms, and an asymmetric task-related information decomposition loss. Experimental results validate the framework's superior generalization performance.
\end{itemize}

Extensive experimental results demonstrate that our PAFDR framework substantially enhances network generalization capability and improves upon existing unsupervised domain adaptation architectures. While our previous conference paper [37] presented a method specifically designed for person re-identification, this work generalizes the approach and incorporates it into popular computer vision tasks including object classification, detection, and semantic segmentation. Additionally, we have developed asymmetric task-specific loss functions based on entropy comparison techniques that are customized for each of these visual understanding tasks.

\section{Related Work}
\label{related}
In this section, we briefly review some vision transformers, recent advancements in hypergraph learning and graph-based neural networks in vision.


\subsection{Unsupervised Domain Adaptation (UDA)}
Domain Invariant Feature Learning. This method generates domain-invariant features effective for target domains through adversarial training and domain discriminators (such as gradient reversal layers). Representative papers include: [26] Chen et al. "Faster-RCNN in the wild" published at CVPR 2018, which first applied domain-invariant feature learning to the Faster-RCNN framework; [58] Saito et al.'s "Strong weak distribution alignment" proposed at CVPR 2019, which improved detection performance through strong-weak distribution alignment; additionally, [72], [73], [74] also adopted similar strategies.

Pseudo-Label-Based Self-Training This method utilizes high-confidence predictions of models trained on source domains as pseudo-labels for target domains, gradually improving the model. Key papers include: [99] Inoue et al.'s "Cross-domain weakly supervised adaptation" proposed at CVPR 2018, combining weakly supervised information for self-training; [62] Chen et al.'s "Automatic adaptation from unlabeled videos" at CVPR 2019, utilizing video data for adaptation; and [63], [102] which further extended this method.

Image-to-Image Translation This approach reduces visual domain differences by mapping target domain images to source domain styles through unpaired image translation techniques. Representative works include: [65] and [104] which proposed adaptation methods based on cycle consistency; [105], [106] also explored similar image translation strategies to improve detector performance.

Domain Randomization This method trains detectors using source domain data stylized in various ways to eliminate source domain style bias and enhance generalization capabilities. Key papers include: [60] Kim et al.'s "Diversify and Match" proposed at CVPR 2019, combining randomization with adversarial learning; [106] also adopted similar methods.
Mean-Teacher Training This approach improves model generalization using unlabeled target domain data through a student-teacher framework. Representative papers include: [69] and [101], where [101] Roychowdhury et al.'s "Mean teacher with object relations" proposed at CVPR 2019 further optimized this method by incorporating object relations.

Graph Reasoning This method captures object relationships in the source domain through graph models and transfers them to the target domain. Key papers include: [100] Xu et al.'s "Cross-domain detection via graph-induced prototype alignment" at CVPR 2020 and [101], enhancing adaptation effects using graph structures.
% To solve this generalization problem, domain adaptation (DA) technology has gradually become a research focus in this field.

% \subsection{Unsupervised Domain Adaptation (UDA)  }
% UDA   aims to adapt the model trained on the labeled source domain to the unlabeled target domain. Common approaches include pseudo-label estimation \cite{rami2022online,rami2024source}, intermediate feature alignment \cite{fu2019self,wang2018transferable}, and generative adversarial networks (GAN)-based style transfer \cite{li2019cross,liu2019adaptive}. Pseudo-label estimation: Use the source domain labels to initially train the model, and generate pseudo-labels in the target domain \cite{rami2022online,rami2024source}. Intermediate feature alignment: Reduce the domain gap by aligning the intermediate features \cite{fu2019self,huang2020domain,wang2020smoothing}. GAN-based style transfer: GAN is used to bridge the visual gap \cite{li2019cross,liu2019adaptive,pang2022cross,zheng2019joint}. Although UDA  alleviates the burden of manual annotation to some extent by utilizing unlabeled data in the target domain, it still cannot avoid the data collection and model retraining process in each new environment, so domain generalization  technology has received increasing attention in recent years.


\subsection{Domain Generalization (DG)}
DG  methods aim to directly apply the model trained on the source domain to unknown target domains, without accessing target domain data or requiring model adjustment. The current DG  methods can be mainly divided into four categories: ensemble-based, meta-learning-based, normalization-based, and feature decomposition-based methods. Among them, the ensemble-based methods use multiple expert models to extract features specific to certain domains \cite{dai2021generalizable,lin2021domain,xu2022mimic}. The meta-learning-based methods improve the generalization performance by simulating virtual domain transfers between training and testing \cite{choi2021meta,song2019generalizable,zhao2021learning}. The normalization-based methods use normalization methods such as BN and IN to reduce domain discrepancy and improve the model's generalization capability \cite{chen2023cluster,jiao2022dynamically}. The feature decomposition-based methods \cite{eom2019learning,zhang2022learning,jin2021style} have achieved good results by decomposing task-relevant and task-irrelevant features. However, existing decomposition-based DG  methods typically decouple features only from the channel perspective, neglecting spatial decomposition, which leads to incomplete feature decomposition.

Domain alignment is one of the most common methods in DG, with its core idea being to learn domain-invariant representations by minimizing the differences between multiple source domains. Early work by Muandet et al. [19] laid the foundation for this direction by aligning the marginal distributions of source domains. Subsequently, researchers explored aligning class-conditional distributions [32, 166] or posterior distributions [172] to capture data structures more comprehensively. For instance, Wang et al. [172] proposed hypothesis-invariant representations by aligning intra-class posterior distributions using Kullback-Leibler divergence. Additionally, alignment techniques include minimizing statistical moments [165, 167], adversarial learning [170, 171], and kernel methods [19, 176].
Meta-learning is another important method that simulates domain shifts during training to enable models to quickly adapt to new domains. Li et al. [33] applied the Model-Agnostic Meta-Learning (MAML) framework to DG, while Balaji et al. [34] introduced meta-regularization techniques to enhance generalization capabilities. These works demonstrate the potential of meta-learning in DG.
Data augmentation increases the diversity of training samples by generating synthetic data, thereby improving model robustness. Zhou et al. [35] proposed a domain-adversarial image generation method, and Shankar et al. [36] introduced cross-gradient training to enhance domain diversity. These methods are widely used in DG.
Ensemble learning improves generalization performance by combining multiple models. Mancini et al. [208] proposed using source-specific networks and selecting appropriate models through a gating mechanism, demonstrating the effectiveness of ensemble strategies.
Self-supervised learning utilizes auxiliary tasks to learn generalizable features. Carlucci et al. [49] demonstrated the effectiveness of jigsaw puzzles as a pretext task for DG, and other works have explored techniques like contrastive learning to enhance domain invariance.
Feature disentanglement aims to separate domain-specific features from domain-invariant ones, with contributions from works such as [189]. Additionally, Invariant Risk Minimization (IRM), proposed by Arjovsky et al. [127], focuses on learning invariances across environments, becoming another important direction in DG research.

\subsection{Feature Decomposition}
Variational Autoencoder (VAE)-based disentangled representation learning methods have wide applications in computer vision, particularly excelling in image generation, image editing, and feature separation tasks. For example, Vanilla VAE [16] can learn disentangled representations on simple datasets like MNIST [44] for image generation and reconstruction, demonstrating basic but effective performance. $\beta$-VAE [6], by introducing a $\beta$ parameter to balance reconstruction error and disentanglement degree, enhances the model's ability to capture independent factors of variation in images, resulting in better performance in image editing and feature separation tasks, especially suitable for scenarios requiring separation of different image attributes. DIP-VAE [35] and FactorVAE [7] further improve disentanglement effects through different regularization techniques, capable of generating higher quality representations suitable for computer vision tasks with high representation quality requirements. $\beta$-TCVAE [5] achieves finer-grained control by decomposing KL divergence, maintaining reconstruction quality while improving disentanglement performance, very suitable for fine-grained image generation tasks. RF-VAE [33] and JointVAE [32] are optimized for meaningful variation factors and mixed continuous and discrete representation scenarios, respectively, applicable to visual tasks requiring specific attribute control, such as adjusting specific features in images. Additionally, group theory-based VAE methods [18,39,40,41] provide theoretical support for disentangled representations from a mathematical perspective, particularly suitable for applications requiring symmetry invariance, such as object pose recognition, and are especially effective in handling visual tasks with geometric constraints.

Generative Adversarial Network (GAN)-based disentangled representation learning methods in computer vision are primarily used for high-quality image generation and image transformation tasks, demonstrating their advantages in generating realistic visual effects. Vanilla GAN [17] generates realistic images through adversarial training, widely applied to natural scene or face image generation tasks, establishing the foundational status of GANs in computer vision. InfoGAN [9], by maximizing mutual information between latent codes and generated images, achieves controllability over generated image attributes, allowing users to control features such as pose, expression, or color of generated images by adjusting latent codes. This attribute control capability gives InfoGAN significant advantages in interactive image editing and personalized generation tasks, such as generating artistic images with specific styles or customized face images, meeting users' demands for flexibility in image generation.

Causal disentanglement methods in computer vision are suitable for handling more complex scenarios, especially image data with confounding factors. These methods, by considering causal relationships between generative factors, can more accurately capture independent factors of variation in images, thereby improving model robustness and generalization ability. For example, causal disentanglement methods [14,11,43] have shown significant performance improvements in image classification tasks that require separating background and foreground features or handling multi-variable influences. 

\section{Method}
\subsection{Overview}
We are committed to developing a generalizable and stable method. During the model training phase, one or multiple well-annotated source datasets can be utilized for learning. The trained model will be directly applied to new domains or datasets without additional adjustments, demonstrating excellent generalization capabilities.

Figure 2 illustrates the overall framework of our proposed Parallel Attention-based Feature Decomposition and Recovery (PAFDR), designed to enhance the generalization performance and recognition accuracy of models in unseen domains.
% This module can be seamlessly integrated into existing  systems, significantly enhancing model performance and recognition accuracy in unseen domains. 
% Using ResNet-50 as the backbone network (see Figure 2), we introduce PAFDR modules after each convolutional block. 
In this section, we provide a detailed explanation of PAFDR. This is a cohesive framework that combines: 3.2. Parallel Attention-based Feature Decomposition and Recovery, 3.3. Asymmetric task-relevant Feature Decomposition Loss.

The PAFDR module boasts excellent compatibility, allowing for easy integration into existing networks. For instance, we applied it to the widely used ResNet-50 \cite{He2015DeepRL}  network (as shown in Figure 2 ($\textrm{I}$)), simply by adding the PAFDR module after each convolutional block.
In this module, the domain gaps between different features are first reduced through a combination of BN and IN. Then, we introduce a recovery step to extract task-relevant features from the information lost during normalization, and add these features back to the normalized features to enhance the discriminability. Furthermore, we design an asymmetric task-relevant feature decomposition loss to guide the spatial attention and channel attention modules to extract task-relevant features from the features lost during the normalization process.

\subsection{Parallel Attention-based Feature Decomposition and Recovery (PAFDR)}
\label{Hypergraph Construction}
Most prior decomposition-based DG  methods usually focus only on channel-based feature decomposition, neglecting spatial feature decomposition, which results in incomplete feature decomposition. In this section, as shown in Figure 2 ($\textrm{II}$), we introduce parallel spatial attention and channel attention to facilitate a more comprehensive decomposition of task-relevant features and task-irrelevant features from residual features. 

\subsubsection{\textbf{Domain Gaps Reduction via BN and IN}} 
In PAFDR, we reduce domain gaps between input features through a combination of Batch Normalization and Instance Normalization:
%\begin{equation}
%\begin{aligned}
%\mathcal{L}_{\mathrm{con}} = \frac{1}{2n} \sum_{i=1}^{n} \big[l(\mathbf{p}_l^i, \mathbf{p}_a^i) + l(\mathbf{p}_a^i, \mathbf{p}_l^i) \big], \\
%l(\mathbf{p}_l^i, \mathbf{p}_a^i) = - \log \frac{\exp \big\{ [r_3 - d(\mathbf{p}_l^i, \mathbf{p}_a^i)] / \tau_3\big\}}{\sum_{k=1}^n \exp\big\{[r_3 - d(\mathbf{p}_l^i, \mathbf{p}_a^k) ] / \tau_3\big\} },
%\end{aligned}
%\end{equation}
\begin{equation}
\begin{aligned}
\tilde{F}=IN(BN(F)),
\end{aligned}
\end{equation}
where the input feature is denoted as $F\in\mathbb{R}^{h\times w\times c}$, with dimensions ${h\times w\times c}$ (height, width, and number of channels). The input feature is first processed by Batch Normalization, and the resulting feature is further processed by Instance Normalization to obtain the final feature $\tilde{F}\in\mathbb{R}^{h\times w\times c}$.

The Batch Normalization process for input features is given by:
\begin{equation}
\begin{aligned}
BN(F)=\gamma_{bn}\cdot\frac{F-\mu_{bn}(F)}{\sqrt{\sigma_{bn}{}^{2}(F)+\varepsilon_{bn}}}+\beta_{bn},
\end{aligned}
\end{equation}
where $\gamma_{bn}\in\mathbb{R}^{c}$ and $\beta_{bn}\in\mathbb{R}^{c}$ are learnable scale and shift parameters, respectively, and $\varepsilon_{bn}>0$ is a small positive constant to prevent division by zero. $\mu_{bn}\in\mathbb{R}^{c}$ and $\sigma_{bn}\in\mathbb{R}^{c}$ represent the mean and standard deviation calculated over the mini-batch data, respectively.

The Instance Normalization layer normalizes features as follows:
\begin{equation}
\begin{aligned}
\mathrm{IN}(F)=\gamma_{in}\cdot\frac{F-\mu_{in}(F)}{\sqrt{\sigma_{in}{}^{2}(F)+\varepsilon_{in}}}+\beta_{in},
\end{aligned}
\end{equation}
where $\gamma_{in}\in\mathbb{R}^{c}$ and $\beta_{in}\in\mathbb{R}^{c}$ are learnable scale and shift parameters, respectively, and $\varepsilon_{in}>0$ is a small positive constant to prevent division by zero. Unlike Batch Normalization, the mean $\mu_{in}$ and standard deviation $\sigma_{in}$ are calculated for each individual sample.

\subsubsection{\textbf{Feature Decomposition and Recovery via parallel spatial and channel attentions}} 
Although the combination of BN and IN effectively reduces domain gaps and enhances generalization, this purely mathematical operation inevitably loses some discriminative information. To compensate for this deficiency, we design a mechanism to extract task-relevant features from the residual $R$ and compensate them back to the normalized features to enhance the discriminability. The residual feature $R$ is calculated as follows:
\begin{equation}
\begin{aligned}
R=F-\tilde{F},
\end{aligned}
\end{equation}
This value reflects the difference between the input feature $F$ and the normalized feature $\tilde{F}$, i.e., the information discarded by the normalization process.



% \subsection{Spatial-Channel Attention Weights for task-Relevant and Irrelevant Features}


% Consequently, the residual features are decomposed into two components: task-relevant features $R^{+}$ and task-irrelevant features $R^{-}$.

% \subsubsection{Spatial Attention Weights for task-Relevant and Irrelevant Features}
We use learnable channel-spatial weight $\mathbf{W}\in\mathbb{R}^{h\times w\times c}$ to mask the residual features $R$, decomposition it into two parts: task-relevant feature $\mathbf{R}^{+}\in\mathbb{R}^{h\times w\times c}$ and task-irrelevant features $\mathbf{R}^{-}\in\mathbb{R}^{h\times w\times c}$. The decomposition process is as follows:
\begin{equation}
\begin{aligned}
% R^{+}=R\cdot\mathbf{W}^{+}
R^{+}=\mathbf{W}\cdot R,
\end{aligned}
\end{equation}
\begin{equation}
\begin{aligned}
% R^{-}=R\cdot\mathbf{W}^{-}
R^{+}=\mathbf{(1-W)}\cdot R.
\end{aligned}
\end{equation}

% \subsubsection{Fusion of Spatial and Channel Weights for task-Relevant and Irrelevant Features}
% For a more thorough feature decomposition, we employ parallel spatial and channel attention to jointly decouple residual features, with the aim that the spatial-channel attention weights 
% To achieve a more thorough feature decomposition, we employed parallel spatial attention and channel attention to decompose the residual feature R, thereby obtaining the final weight
% $\mathbf{W}\in\mathbb{R}^{h\times w\times c}$.
In this paper, we employ parallel spatial and channel attention to decouple residual features, with the aim that the spatial-channel attention weights $\mathbf{W}\in\mathbb{R}^{h\times w\times c}$ can more thoroughly extract task-relevant features from both channel and spatial perspectives.
Specifically, the residual features $R$ first pass through spatial attention and channel attention in parallel, generating the corresponding spatial weights $\mathbf{W}_{\mathrm{spatial}}$ and channel weights $\mathbf{W}_{\mathrm{channel}}$ for task-relevant features. Then we multiply the spatial weights with the corresponding channel weights to obtain the final combined weights $\mathbf{W}$ for task-relevant features, as shown below:

\begin{equation}
\begin{aligned}
\mathbf{W}=\mathbf{W}_{\mathrm{spatial}}\cdot\mathbf{W}_{\mathrm{channel}}.
\end{aligned}
\end{equation}

% The spatial attention mechanism works in parallel with channel attention, enabling a more thorough decomposition of task-relevant and task-irrelevant features from both channel and spatial perspectives.



% We obtain the spatial attention weights $\mathbf{W}^{+}_{\mathrm{spatial}}\in\mathbb{R}^{1\times h\times w}$ for task-relevant features and $\mathbf{W}^{-}_{\mathrm{spatial}}\in\mathbb{R}^{1\times h\times w}$ for task-irrelevant features through spatial attention. By performing element-wise multiplication of these weights with the residual features $R$ along the spatial dimension, we can decompose task-relevant and task-irrelevant features from a spatial perspective.


% \begin{equation}
% \begin{aligned}
% \mathbf{W}_{\mathrm{spatial}}^{+}=\mathbf{W}_{\mathrm{spatial}}
% \end{aligned}
% \end{equation}
% \begin{equation}
% \begin{aligned}
% \mathbf{W}_{\mathrm{spatial}}^{-}=\mathbf{1}-\mathbf{W}_{\mathrm{spatial}}
% \end{aligned}
% \end{equation}

By performing element-wise multiplication of these weights with the residual features $R$ along the spatial dimension, we can decompose task-relevant and task-irrelevant features from a spatial perspective.
Spatial attention works as follows: First, max-pooling along the channel axis is applied to the residual features $R$ to obtain a ${1\times h\times w}$ single-channel tensor containing global channel information. This tensor is then convolved with a ${k\times k}$ kernel to produce spatial weights $\mathbf{W}_{\mathrm{spatial}}\in\mathbb{R}^{1\times h\times w}$. Finally, a sigmoid function is applied to the weights. The spatial attention weights $\mathbf{W}_{\mathrm{spatial}}$ is calculated as:


\begin{equation}
\begin{aligned}
\mathbf{W}_{\mathrm{spatial}}=\sigma(f^{\mathrm{k}\times\mathrm{k}}(max\_channel\_pool(\mathbf{R}))),
\end{aligned}
\end{equation}
where $max\_channel\_pool$ represents max-pooling along the channel axis, $f^{\mathrm{k}\times\mathrm{k}}$ denotes a convolution operation with a kernel size of ${k\times k}$, ${k}=7$ in this paper, and $\sigma$ represents the sigmoid function.

% \subsubsection{Channel Attention Weights for task-Relevant and Irrelevant Features}

% We obtain the channel attention weights $\mathbf{W}^{+}_{\mathrm{channel}}\in\mathbb{R}^{c\times 1\times 1}$ for task-relevant features and $\mathbf{W}^{-}_{\mathrm{channel}}\in\mathbb{R}^{c\times 1\times 1}$ for task-irrelevant features through channel attention. By performing element-wise multiplication of these weights with the residual features $R$ along the channel dimension, we can decompose task-relevant and task-irrelevant features from a channel perspective.
% \begin{equation}
% \begin{aligned}
% \mathbf{W}_{\mathrm{channel}}^{+}=\mathbf{W}_{\mathrm{channel}}
% \end{aligned}
% \end{equation}
% \begin{equation}
% \begin{aligned}
% \mathbf{W}_{\mathrm{channel}}^{-}=\mathbf{1}-\mathbf{W}_{\mathrm{channel}}
% \end{aligned}
% \end{equation}

By performing element-wise multiplication of these weights with the residual features $R$ along the channel dimension, we can decompose task-relevant and task-irrelevant features from a channel perspective.
Channel attention works as follows: First, max-pooling is applied to the residual features $R$ along the spatial dimensions to obtain a ${c\times 1\times 1}$ tensor containing global spatial information. Then, a ${1\times 1}$ convolution (equivalent to a fully connected layer) is used to reduce the number of channels to $c/r$ (where $r=16$ in this paper) to reduce computational complexity. After ReLU activation, the reduced channels are restored to $c$ channels. Finally, a Sigmoid activation function is applied to obtain the channel weights $\mathbf{W}_{\mathrm{channel}}\in\mathbb{R}^{c\times 1\times 1}$.


\begin{equation}
\begin{aligned}
\mathbf{W}_{\mathrm{channel}}=\sigma(W_{2}\delta(W_{1}max\_pool(R))),
\end{aligned}
\end{equation}
where $max\_pool$ represents max-pooling,  $\mathrm{W}_{1}$ and $\mathrm{W}_{2}$ represent two fully connected layers parameterized as $\mathrm{W}_{1}\in\mathbb{R}^{c\times(c/r)}$ and $\mathrm{W}_{2}\in\mathbb{R}^{(c/r)\times c}$. The first fully connected layer uses ReLU $(\delta(\cdot))$ as the activation function, while the second fully connected layer uses the sigmoid function $(\sigma(\cdot))$ as the activation function.

By adding the task-relevant feature ${R}^{+}$ to the normalized feature $\tilde{F}$, we obtain the output feature $\tilde{F}^{+}\in\mathbb{R}^{h\times w\times c}$ of the PAFDR module, as shown below:
\begin{equation}
\begin{aligned}
\tilde{F}^{+}=\tilde{F}+R^{+},
\end{aligned}
\end{equation}
Correspondingly, by adding task-irrelevant features ${R}^{-}$ to the normalized features $\tilde{F}$, we obtain the contaminated features $\tilde{F}^{-}\in\mathbb{R}^{h\times w\times c}$, which are used for subsequent loss construction to facilitate feature decomposition.
% By adding the task-relevant feature ${R}^{-}$ to the normalized feature $\tilde{F}$, we obtain the output feature $\tilde{F}^{-}\in\mathbb{R}^{h\times w\times c}$ of the PAFDR module, as shown below:
\begin{equation}
\begin{aligned}
\tilde{F}^{-}=\tilde{F}+R^{-}.
\end{aligned}
\end{equation}

% %自适应大小表格
% \begin{table*}[]
% \caption{ Comparison with existing methods on multi-source DG .}         %表标题
% \resizebox{\linewidth}{!}{
% \begin{tabular}{c|cc|cc|cc|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{MS+D+C→M}                      & \multicolumn{2}{c|}{MS+M+C→D}                      & \multicolumn{2}{c|}{M+D+C→MS}                      & \multicolumn{2}{c|}{MS+M+D→C}                      & \multicolumn{2}{c}{Average}                        \\ \cline{2-11} 
%                         & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           \\ \hline

% OSNet \cite{zhou2019osnet}                  & \multicolumn{1}{c|}{72.5}          & 44.2          & \multicolumn{1}{c|}{65.2}          & 47.0          & \multicolumn{1}{c|}{33.2}          & 12.6          & \multicolumn{1}{c|}{23.9}          & 23.3          & \multicolumn{1}{c|}{48.7}          & 31.8          \\ \hline
% QAConv \cite{liao2020interpretable}                 & \multicolumn{1}{c|}{68.6}          & 39.5          & \multicolumn{1}{c|}{64.9}          & 43.4          & \multicolumn{1}{c|}{29.9}          & 10.0          & \multicolumn{1}{c|}{22.9}          & 19.2          & \multicolumn{1}{c|}{46.6}          & 28.0          \\ \hline
% % OSNet-IBN \cite{zhou2022learning}              & \multicolumn{1}{c|}{73.0}          & 44.9          & \multicolumn{1}{c|}{64.6}          & 45.7          & \multicolumn{1}{c|}{39.8}          & 16.2          & \multicolumn{1}{c|}{25.7}          & 25.4          & \multicolumn{1}{c|}{50.8}          & 33.0          \\ \hline
% DAML \cite{shu2021open}                   & \multicolumn{1}{c|}{75.5}          & 49.3          & \multicolumn{1}{c|}{66.5}          & 47.6          & \multicolumn{1}{c|}{32.2}          & 12.6          & \multicolumn{1}{c|}{29.8}          & 29.3          & \multicolumn{1}{c|}{51.0}          & 34.7          \\ \hline
% PAFDR \cite{jin2021style}                    & \multicolumn{1}{c|}{75.2}          & 48.5          & \multicolumn{1}{c|}{66.7}          & 48.3          & \multicolumn{1}{c|}{35.1}          & 13.8          & \multicolumn{1}{c|}{29.1}          & 29.0          & \multicolumn{1}{c|}{51.5}          & 34.9          \\ \hline
% M$^{3}$L \cite{zhao2021learning}                    & \multicolumn{1}{c|}{76.5}          & 51.1          & \multicolumn{1}{c|}{67.1}          & 48.2          & \multicolumn{1}{c|}{32.0}          & 13.1          & \multicolumn{1}{c|}{31.9}          & 30.9          & \multicolumn{1}{c|}{51.9}          & 35.8          \\ \hline
% RaMoE \cite{dai2021generalizable}  & \multicolumn{1}{c|}{82.0} & 56.5 & \multicolumn{1}{c|}{\underline{73.6}} & \textbf{56.9} & \multicolumn{1}{c|}{34.1} & 13.5 & \multicolumn{1}{c|}{36.6} & 35.5 & \multicolumn{1}{c|}{56.6} & 40.6 \\ \hline
% MetaBIN \cite{choi2021meta} & \multicolumn{1}{c|}{\underline{83.2}} & \textbf{61.2} & \multicolumn{1}{c|}{71.3} & 54.9 & \multicolumn{1}{c|}{40.8} & 17.0 & \multicolumn{1}{c|}{\textbf{38.1}} & \underline{37.5} & \multicolumn{1}{c|}{\underline{58.4}} & \underline{42.7} \\ \hline
% OSNet-AIN \cite{zhou2022learning}      & \multicolumn{1}{c|}{73.3}          & 45.8          & \multicolumn{1}{c|}{65.6}          & 47.2          & \multicolumn{1}{c|}{40.2}          & 16.2          & \multicolumn{1}{c|}{27.4}          & 27.1          & \multicolumn{1}{c|}{51.6}          & 34.1          \\ \hline
% MixNorm \cite{qi2022novel} & \multicolumn{1}{c|}{78.9} & 51.4 & \multicolumn{1}{c|}{70.8} & 49.9 & \multicolumn{1}{c|}{\textbf{47.2}} & \underline{19.4} & \multicolumn{1}{c|}{29.6} & 29.0 & \multicolumn{1}{c|}{56.6} & 37.4 \\ \hline
% PAFDR (ours)            & \multicolumn{1}{c|}{\textbf{83.6}} & \underline{57.8} & \multicolumn{1}{c|}{\textbf{73.8}} & \underline{56.4} & \multicolumn{1}{c|}{\underline{41.3}} & \textbf{19.8} & \multicolumn{1}{c|}{\underline{37.3}} & \textbf{37.7} & \multicolumn{1}{c|}{\textbf{58.5}} & \textbf{42.9} \\ \hline
% \end{tabular}}
% \end{table*}

%原始大小表格
% \begin{table*}[]
% \caption{ Comparison with existing methods on multi-source DG .}         %表标题
% \begin{tabular}{c|cc|cc|cc|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{MS+D+C→M}                      & \multicolumn{2}{c|}{MS+M+C→D}                      & \multicolumn{2}{c|}{M+D+C→MS}                      & \multicolumn{2}{c|}{MS+M+D→C}                      & \multicolumn{2}{c}{Average}                        \\ \cline{2-11} 
%                         & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           & \multicolumn{1}{c|}{Rank-1}        & mAP           \\ \hline

% OSNet \cite{zhou2019osnet}                  & \multicolumn{1}{c|}{72.5}          & 44.2          & \multicolumn{1}{c|}{65.2}          & 47.0          & \multicolumn{1}{c|}{33.2}          & 12.6          & \multicolumn{1}{c|}{23.9}          & 23.3          & \multicolumn{1}{c|}{48.7}          & 31.8          \\ \hline
% QAConv \cite{liao2020interpretable}                 & \multicolumn{1}{c|}{68.6}          & 39.5          & \multicolumn{1}{c|}{64.9}          & 43.4          & \multicolumn{1}{c|}{29.9}          & 10.0          & \multicolumn{1}{c|}{22.9}          & 19.2          & \multicolumn{1}{c|}{46.6}          & 28.0          \\ \hline
% % OSNet-IBN \cite{zhou2022learning}              & \multicolumn{1}{c|}{73.0}          & 44.9          & \multicolumn{1}{c|}{64.6}          & 45.7          & \multicolumn{1}{c|}{39.8}          & 16.2          & \multicolumn{1}{c|}{25.7}          & 25.4          & \multicolumn{1}{c|}{50.8}          & 33.0          \\ \hline
% DAML \cite{shu2021open}                   & \multicolumn{1}{c|}{75.5}          & 49.3          & \multicolumn{1}{c|}{66.5}          & 47.6          & \multicolumn{1}{c|}{32.2}          & 12.6          & \multicolumn{1}{c|}{29.8}          & 29.3          & \multicolumn{1}{c|}{51.0}          & 34.7          \\ \hline
% PAFDR \cite{jin2021style}                    & \multicolumn{1}{c|}{75.2}          & 48.5          & \multicolumn{1}{c|}{66.7}          & 48.3          & \multicolumn{1}{c|}{35.1}          & 13.8          & \multicolumn{1}{c|}{29.1}          & 29.0          & \multicolumn{1}{c|}{51.5}          & 34.9          \\ \hline
% M$^{3}$L \cite{zhao2021learning}                    & \multicolumn{1}{c|}{76.5}          & 51.1          & \multicolumn{1}{c|}{67.1}          & 48.2          & \multicolumn{1}{c|}{32.0}          & 13.1          & \multicolumn{1}{c|}{31.9}          & 30.9          & \multicolumn{1}{c|}{51.9}          & 35.8          \\ \hline
% RaMoE \cite{dai2021generalizable}  & \multicolumn{1}{c|}{82.0} & 56.5 & \multicolumn{1}{c|}{\underline{73.6}} & \textbf{56.9} & \multicolumn{1}{c|}{34.1} & 13.5 & \multicolumn{1}{c|}{36.6} & 35.5 & \multicolumn{1}{c|}{56.6} & 40.6 \\ \hline
% MetaBIN \cite{choi2021meta} & \multicolumn{1}{c|}{\underline{83.2}} & \textbf{61.2} & \multicolumn{1}{c|}{71.3} & 54.9 & \multicolumn{1}{c|}{40.8} & 17.0 & \multicolumn{1}{c|}{\textbf{38.1}} & \underline{37.5} & \multicolumn{1}{c|}{\underline{58.4}} & \underline{42.7} \\ \hline
% OSNet-AIN \cite{zhou2022learning}      & \multicolumn{1}{c|}{73.3}          & 45.8          & \multicolumn{1}{c|}{65.6}          & 47.2          & \multicolumn{1}{c|}{40.2}          & 16.2          & \multicolumn{1}{c|}{27.4}          & 27.1          & \multicolumn{1}{c|}{51.6}          & 34.1          \\ \hline
% MixNorm \cite{qi2022novel} & \multicolumn{1}{c|}{78.9} & 51.4 & \multicolumn{1}{c|}{70.8} & 49.9 & \multicolumn{1}{c|}{\textbf{47.2}} & \underline{19.4} & \multicolumn{1}{c|}{29.6} & 29.0 & \multicolumn{1}{c|}{56.6} & 37.4 \\ \hline
% PAFDR (ours)            & \multicolumn{1}{c|}{\textbf{83.6}} & \underline{57.8} & \multicolumn{1}{c|}{\textbf{73.8}} & \underline{56.4} & \multicolumn{1}{c|}{\underline{41.3}} & \textbf{19.8} & \multicolumn{1}{c|}{\underline{37.3}} & \textbf{37.7} & \multicolumn{1}{c|}{\textbf{58.5}} & \textbf{42.9} \\ \hline
% \end{tabular}
% \end{table*}

\subsection{Asymmetric Task-relevant Feature Decomposition Loss}
Loss functions in existing decomposition-based domain generalization methods are often compared with normalized feature $\tilde{F}$ to encourage feature decomposition.
However, as shown by features $\tilde{F}$ in Figure 1, due to the loss of discriminative information, the discriminability of the normalized feature $\tilde{F}$ is weaker compared to the original image feature $F$ \cite{jin2021style,huang2017arbitrary,pan2018two,zhou2019osnet}. Loss functions based on comparisons with weakly discriminative normalized features $\tilde{F}$ will not yield the most discriminative features.
In our literature [666], we designed an asymmetric task-related feature decoupling loss for the pedestrian re-identification task to address this problem. However, the asymmetric task-related feature decoupling loss is designed based on pedestrian triplets and is not applicable to general domain generalization and domain adaptation tasks. 

% Regarding this issue, in our literature [666], we designed an asymmetric task-related feature decoupling loss for pedestrian re-identification tasks. However, this loss function was based on pedestrian triplets and is not applicable to general domain generalization and domain adaptation tasks. In this paper, we have designed an asymmetric task-related feature decoupling loss, extending PAFDR to general domain generalization and domain adaptation tasks.

% To effectively decompose task-relevant and task-irrelevant features, we propose Asymmetric task-relevant Feature Decomposition Loss (ATFD) loss,
In this paper, we extend PAFDR to general domain generalization and domain adaptation tasks.
Specifically, to achieve better generalization and discriminability, we propose the Asymmetric Task-relevant Feature Decomposition (ATFD) loss to promote a more thorough decomposition of task-relevant and task-irrelevant features.
As shown by the ATFD Loss in Figure 2 ($\textrm{III}$), the core idea of the ATFD loss is to expect that after compensating the task-relevant feature $R^{+}$ to the normalized feature $\tilde{F}$, the discriminability of the feature will be enhanced and even exceed that of the original feature $F$ with the strongest discriminability. In other words, compared to the original feature F, the feature F+ becomes less ambiguous in its predicted class likelihood (reduced uncertainty), with a smaller entropy. Conversely, adding the task-irrelevant feature $R^{-}$ to $\tilde{F}$ will weaken the discriminability of this feature, making it less discriminative than the normalized feature $\tilde{F}$ with the weakest discriminability. In other words, compared to the normalized feature $\tilde{F}$, its predicted class likelihood becomes less ambiguous (reduced uncertainty), with a smaller entropy.



We achieve this goal by designing the ATFD loss, which consists of an enhancement loss $\mathcal{L}_{ATFD}^{+}$ and a suppression loss $\mathcal{L}_{ATFD}^{-}$, i.e., $\mathcal{L}_{ATFD}=\mathcal{L}_{ATFD}^{+}+\mathcal{L}_{ATFD}^{-}$.
% In each training batch, we collect three types of sample images: anchor samples (abbreviated as $a$), positive samples (abbreviated as $p$), and negative samples (abbreviated as $n$). Positive and anchor samples come from the same person, while negative samples come from different persons. In terms of expression, we use subscripts to identify the feature of different samples, such as using $\tilde{F}_{a}$ to represent the normalized feature of the anchor sample.
% \begin{equation}
% \begin{aligned}
% \mathcal{L}_{ATFD}^{+}=Softplus(d(\tilde{F}_{a}^{+},\tilde{F}_{p}^{+})-d(F_{a},F_{p}))\\+Softplus(d(F_{a},F_{n})-d(\tilde{F}_{a}^{+},\tilde{F}_{n}^{+})),
% \end{aligned}
% \end{equation}

% \begin{equation}
% \begin{aligned}
% \mathcal{L}_{ATFD}^{-}=Softplus(d(\tilde{F}_{a},\tilde{F}_{p})-d(\tilde{F}_{a}^{-},\tilde{F}_{p}^{-}))\\+Softplus(d(\tilde{F}_{a}^{-},\tilde{F}_{n}^{-})-d(\tilde{F}_{a},\tilde{F}_{n})).
% \end{aligned}
% \end{equation}

% where the function $Softplus(\cdot)=ln(1+exp(\cdot))$ is a strictly increasing function used to remove negative losses during optimization, making the optimization process smoother. $d(\mathbf{x},\mathbf{y})=0.5-\mathbf{x}^{\mathrm{T}}\mathbf{y}/(2\parallel\mathbf{x}\parallel\parallel\mathbf{y}\parallel)$ represents the distance between feature $x$ and feature $y$.





% \begin{figure}[t]
%   \centering
%   \includegraphics[width=0.8\linewidth]{samples/pictures/vis_0208.pdf}
%   \caption{Asymmetric task-relevant Feature Decomposition Loss}
%   \label{fig:onecol}
% \end{figure}

% According to the design of ATFD loss, compared with the original feature F,

 Considering the classification task as an illustration, we pass the enhanced feature vector $\tilde{F}^{+}=\tilde{F}+\mathit{R}^{+}$ to a fully connected layer (containing K nodes, where K represents the number of categories), then through a softmax function (which we denote as $\phi(\widetilde{\mathbf{F}}^+)$. Then we calculate its entropy, using the entropy function $H(\cdot)=-p(\cdot)\log p(\cdot)$. Similarly, the polluted feature vector can be obtained through $\tilde{F}^{-}=\tilde{F}+R^{-}$. 
% Where Softplus(·) = ln(1 + exp(·)) is a monotonically increasing function, designed to reduce the overall optimization difficulty by avoiding the occurrence of negative loss values. 
Enhancement loss $\mathcal{L}_{ATFD}^{+}$ and suppression loss $\mathcal{L}_{ATFD}^{-}$ are defined as follows:
% Based on the above analysis, we define the enhancement loss as:
% \begin{equation}
% \begin{aligned}
% \mathcal{L}_{ATFD}^{+}=Softplus(d(\tilde{F}_{a}^{+},\tilde{F}_{p}^{+})-d(F_{a},F_{p}))\\+Softplus(d(F_{a},F_{n})-d(\tilde{F}_{a}^{+},\tilde{F}_{n}^{+})),
% \end{aligned}
% \end{equation}
\begin{equation}
\begin{aligned}
\mathcal{L}_{ATFD}^+=S(H(\phi(\widetilde{\mathbf{F}}^+))-H(\phi({\mathbf{F}})))
\end{aligned}
\end{equation}
% Based on the above analysis, we design the suppression loss as:
% \begin{equation}
% \begin{aligned}
% \mathcal{L}_{ATFD}^{-}=Softplus(d(\tilde{F}_{a},\tilde{F}_{p})-d(\tilde{F}_{a}^{-},\tilde{F}_{p}^{-}))\\+Softplus(d(\tilde{F}_{a}^{-},\tilde{F}_{n}^{-})-d(\tilde{F}_{a},\tilde{F}_{n})).
% \end{aligned}
% \end{equation}
\begin{equation}
\begin{aligned}
\mathcal{L}_{ATFD}^-=S(H(\phi(\widetilde{\mathbf{F}}))-H(\phi(\widetilde{\mathbf{F}}^-)))
\end{aligned}
\end{equation}
where the function $S(\cdot)=ln(1+exp(\cdot))$ is a strictly increasing function used to remove negative losses during optimization, making the optimization process smoother. 
% Intuitively, ATFD loss assigns appropriate comparison objects to different features. This approach further promotes the decomposition of task-relevant and task-irrelevant features while enhancing feature discriminability.
Intuitively, ATFD loss matches appropriate comparison objects for different features, thereby more effectively decomposing task-related and task-irrelevant features while improving the discriminability of features.
In Eq. (12), compared to \textbf{the original feature $F$}, recovering the task-relevant features into the normalized features $\tilde{F}$ to form enhanced features $\tilde{F}^{+}=\tilde{F}+\mathit{R}^{+}$ can significantly improve discriminability. In Eq. (13), compared to \textbf{the normalized feature $\tilde{F}$}, when we add the task-irrelevant feature $R^{-}$ to the normalized feature $\tilde{F}$ to obtain the contaminated feature $\tilde{F}^{-}=\tilde{F}+R^{-}$, the discriminative ability decreases. 

\subsection{Overall Loss Function}

In our implementation, we choose ResNet-50 as the backbone network and integrate PAFDR modules after its four convolutional blocks, as shown in Figure 2. The total training loss of the entire network is defined as:

\begin{equation}
\begin{aligned}
\mathcal{L}=\mathcal{L}_{task}+\sum_{a=1}^{4}\lambda_{a}\mathcal{L}_{ATFD}^{a},
\end{aligned}
\end{equation}

This loss consists of two parts: the basic loss terms for the task $\mathcal{L}_{task}$ and ATFD loss $\mathcal{L}_{ATFD}$. Each PAFDR module corresponds to an asymmetric task-relevant feature decomposition loss $\mathcal{L}_{ATFD}^{a}$ (representing the loss for the $a$-th PAFDR module). Here, $\lambda_{a}$ ($a$=1,2,3,4) is a weighting used to balance the contribution of each PAFDR module, uniformly set to 0.1 in this paper.

\subsection{Applications, Extensions, and Variants}
Our proposed PAFDR demonstrates versatility in enhancing neural networks' generalization and discriminative capabilities for both Domain Generalization (DG) and Domain Adaptation (DA) tasks. As a plug-and-play module, PAFDR integrates seamlessly into various neural network architectures across different computer vision applications including object classification, detection, and segmentation.

As detailed in Section III-C, we pass the enhanced/corrupted feature vectors ef"/ef- through function to obtain entropy values. When implementing this process across different tasks—classification (image-level), segmentation (pixel-level), and detection (region-level)—there are notable differences in how feature vectors are extracted for computing the recovery loss. We elaborate on these implementation details in the following subsections.

\subsubsection{\textbf{classification}} 
In classification tasks, we utilize ResNet-50 as the base architecture to showcase the application of PAFDR. As shown in Figure 2 ($\textrm{I}$), PAFDR modules are strategically positioned at the end of each convolution block. When input features F are fed into a PAFDR module, the system produces three distinct feature representations: standardized features Fe, amplified features Fe+, and deteriorated features Fe-. These representations collectively contribute to the computation of asymmetric task-oriented feature separation loss for model optimization."

\subsubsection{\textbf{Detection}} 
Semantic segmentation aims to assign category labels to each pixel in an image, constituting a pixel-level dense prediction challenge. We adopt an approach similar to classification by seamlessly integrating the PAFDR module into the backbone structure of segmentation networks. However, a key difference exists in implementing the recovery loss, where we individually compute entropy values for features at each spatial location (as each pixel position in segmentation contains independent category prediction information), rather than calculating a single entropy value for globally pooled features.

We denote ${B}_{i}$ as the entropy value of the $i$-th object bounding box in the image, and $\tilde{B}_ {i}$ as the entropy value of features within the $i$-th object bounding box before feature compensation in the PAFDR module. Similarly, $\tilde{B}_{i}^{+}$ and $\tilde{B}_{i}^{-}$ represent the entropy values of features obtained after compensation with task-relevant and task-irrelevant features, respectively, within the $i$-th object bounding box. Accordingly, the asymmetric task-relevant feature decoupling loss function can be defined as:
\begin{equation}
\mathcal{L}_{ATFD}^{+} = S \left( \frac{1}{N} \sum_{i=1}^{N} H(\phi(\tilde{B}_{i}^{+}) - \frac{1}{N} \sum_{i=1}^{N} H(\phi({B}_{i}) \right)
\end{equation}

\begin{equation}
\mathcal{L}_{ATFD}^{-} = S \left( \frac{1}{N} \sum_{i=1}^{N} H(\phi(\tilde{B}_{i}) - \frac{1}{N} \sum_{i=1}^{N} H(\phi(\tilde{B}_{i}^{-}) \right)
\end{equation}
Where $N$ represents the total number of object bounding boxes in the image. For this task, the final loss constraint is calculated using the average entropy across all object regions in the image.




\subsubsection{\textbf{ Segmentation}} 
Semantic segmentation aims to assign category labels to each pixel in an image, constituting a pixel-level dense prediction challenge. We adopt an approach similar to classification by seamlessly integrating the PAFDR module into the backbone structure of segmentation networks. However, a key difference exists in implementing the recovery loss, where we individually compute entropy values for features at each spatial location (as each pixel position in segmentation contains independent category prediction information), rather than calculating a single entropy value for globally pooled features.


\section{Experiments}
In this section, we first describe the datasets, evaluation metrics, and implementation details in Section 4.1. In Section 4.2, we demonstrate the comparison of our proposed method with existing domain generalization methods through multi-source domain generalization experiments and single-source domain generalization experiments. Then, we conduct ablation experiments in Section 4.3 to verify the effectiveness of each component of PAFDR. Finally, we perform a visualization analysis in Section 4.5.

\subsection{Experimental Settings}
To systematically evaluate the generalization of our proposed
PAFDR, we evaluated the effectiveness of the proposed PAFDR
under both Domain Generalization (DG) and Unsupervised Domain
Adaptation (UDA), described below.
\subsubsection{\textbf{Domain Generalization}}
In our domain generalization (DG) experiments, we employed two mainstream benchmark datasets: PACS [5] and Office Home [77]. Each dataset contains four distinct domains, with PACS covering 7 categories and Office Home encompassing 65 categories. We adopted a cross-domain training/testing split strategy consistent with Jin et al. [4]. In our experiments, we selected RESNET18 as the base network architecture and incorporated TL-PAFDR components after each convolutional module to enhance cross-domain feature extraction capabilities. During the training process, we set the maximum iteration epochs to 40, with an initial learning rate of 0.002. Each batch contained 30 images from various source domains (10 per domain), and we utilized the SGD algorithm for model optimization.

\subsubsection{\textbf{Unsupervised Domain Adaptation}}
We selected Domainnet and Digit-5 [13] as standard evaluation datasets for UDA tasks. Domainnet is a large-scale multi-source domain adaptation dataset [15], comprising approximately 600,000 images across six domains (clipart, painting, QuickDraw, real images, and sketch), covering 345 categories. Digit-5 consists of five independent digit recognition datasets: MNIST [27] (MT), MNIST-M [28] (MM), USPS [29] (UP), SVHN [30] (SV), and SYN [28] (SYN), with each dataset treated as a separate domain. We followed the data partitioning scheme proposed by Jin et al. [4]. For Digit-5, we constructed a network architecture with three convolutional layers and two fully connected layers, embedding PAFDR modules after each convolutional layer. Similar to the experimental setup in PAFDR [4], we used M3SDA [13] as a comparison benchmark. For the Domainnet dataset, we employed Resnet101 [49] as the base architecture and added PAFDR components after various convolutional modules to achieve cross-domain feature alignment.

Regarding training parameters, we set the maximum iteration epochs to 60, with an initial learning rate of 0.005, adopting a cosine annealing strategy for learning rate adjustment. Each batch contained 64 images, and we chose SGD as the optimizer.
Evaluation Method. In both UDA and DG tasks, we used classification accuracy as the primary evaluation metric, consistent with the approach in [4].


\begin{table*}[t]
\centering
\caption{Comparison of domain generalization methods on PACS and Office-Home datasets.}
\begin{tabular}{c|ccccc|ccccc}
\hline
Datasets          & \multicolumn{5}{c|}{PACS}                                                                          & \multicolumn{5}{c}{Office-Home}                                                                    \\ \hline
Methods           & Art           & Cartoon       & Photo         & \multicolumn{1}{c|}{Sketch}        & Avg.          & Art           & Clipart       & Product       & \multicolumn{1}{c|}{Real}          & Avg.          \\ \hline
DeepAll           & 76.5          & 76.9          & 94.6          & \multicolumn{1}{c|}{69.0}          & 79.2          & 53.3          & 51.8          & 71.2          & \multicolumn{1}{c|}{73.2}          & 62.4          \\
JiGen {[}29{]}    & 79.4          & 75.3          & 96.0          & \multicolumn{1}{c|}{71.4}          & 80.5          & 53.0          & 47.5          & 71.5          & \multicolumn{1}{c|}{72.8}          & 61.2          \\
PAFDR {[}4{]}       & 80.3          & 78.2          & 94.5          & \multicolumn{1}{c|}{74.1}          & 81.8          & \underline{61.2}    & 53.7          & 74.2          & \multicolumn{1}{c|}{75.1}          & 66.1          \\
LDDG {[}23{]}     & 81.0          & 77.9          & 95.7          & \multicolumn{1}{c|}{75.6}          & 82.6          & 54.7          & 52.7          & 72.7          & \multicolumn{1}{c|}{74.2}          & 63.6          \\
L2A-OT {[}38{]}   & 83.3          & 78.2          & \underline{96.2}          & \multicolumn{1}{c|}{73.6}          & 82.8          & 60.6          & 50.1          & \underline{74.8}    & \multicolumn{1}{c|}{77.0}          & 65.6          \\
SagNet {[}14{]}   & 83.6          & 77.7          & 95.5          & \multicolumn{1}{c|}{76.3}          & 83.3          & 60.2          & 45.4          & 70.4          & \multicolumn{1}{c|}{73.4}          & 62.3          \\
DAEL+{[}5{]}      & \underline{84.6}    & 74.4          & 95.6          & \multicolumn{1}{c|}{78.9}          & 83.4          & 59.4          & \underline{55.1}    & 74.0          & \multicolumn{1}{c|}{75.7}          & 66.1          \\
SelfReg {[}30{]}  & 82.3          & 78.4          & \underline{96.2}    & \multicolumn{1}{c|}{77.5}          & 83.6          & -             & -             & -             & \multicolumn{1}{c|}{-}             & -             \\
Tl-PAFDR            & 83.4          & \textbf{79.7} & \underline{96.2}    & \multicolumn{1}{c|}{75.2}          & 83.6          & -             & -             & -             & \multicolumn{1}{c|}{-}             & -             \\
00MixStyle {[}32{]} & 84.1          & 78.8          & 96.1          & \multicolumn{1}{c|}{75.9}          & 83.7          & 58.7          & 53.4          & 74.2          & \multicolumn{1}{c|}{\textbf{75.9}} & 65.6          \\
PCL[31] {[}32{]} & -          & -          & -          & \multicolumn{1}{c|}{-}          & -          & 62.1          & 58.2          & 77.4          & \multicolumn{1}{c|}{78.0}  & 69.0          \\
KDDRL+{[}52{]}    & 82.3          & 78.9          & 95.6          & \multicolumn{1}{c|}{\textbf{82.2}} & \underline{84.7}    & 59.4          & \underline{55.1}    & \underline{74.8}    & \multicolumn{1}{c|}{75.4}          & \underline{66.2}    \\
\hline % 新增的横线
PAFDR (ours)       & \textbf{84.8} & \underline{79.5}    & \textbf{96.4} & \multicolumn{1}{c|}{\underline{79.1}}    & \textbf{84.9} & \textbf{62.7} & \textbf{58.4} & \textbf{77.5} & \multicolumn{1}{c|}{\underline{78.2}}    & \textbf{69.2} \\ \hline
\end{tabular}
\end{table*}


\begin{table*}[t]
\centering
\begin{minipage}{0.48\textwidth}
\centering
\caption{Comparison of UDA methods on DomainNet.}
\begin{tabular}{c|ccccccc}
\hline
Methods           & Clp  & Inf  & Pnt  & Qdr  & Rel  & Skt  & Avg  \\ \hline
M3SDA{[}5{]}      & 58.6 & 26.0 & 52.3 & 6.3  & 62.7 & 49.5 & 42.6 \\
T-SVDNet {[}44{]} & 66.1 & 25.0 & 54.3 & 16.5 & 65.4 & 54.6 & 47.0 \\
PTMDA {[}41{]}    & 66.0 & 28.5 & 58.4 & 13.0 & 63.0 & 54.1 & 47.2 \\
SPS {[}45{]}      & \textbf{70.8} & 24.6 & 55.2 & 19.4 & 67.5 & 57.6 & 49.2 \\
DSFE {[}46{]}     & 68.2 & 25.8 & \underline{58.8} & 18.3 & \textbf{71.9} & 57.6 & 50.1 \\
MIEM {[}50{]}     & 69.0 & \underline{28.6} & 58.7 & \underline{20.5} & 68.9 & \underline{59.2} & \underline{50.8} \\ \hline
PAFDR(ours)       & \underline{69.4} & \textbf{29.6} & \textbf{59.3} & \textbf{21.1} & \underline{69.3} & \textbf{60.7} & \textbf{51.6} \\ \hline
\end{tabular}
\end{minipage}
\hfill
\begin{minipage}{0.48\textwidth}
\centering

\caption{Comparison of UDA methods on Digits-5.}
\begin{tabular}{c|cccccc}
\hline
Methods          & mm   & mt   & up   & SV   & syn  & Avg  \\ \hline
M3SDA{[}5{]}     & 72.8 & 98.4 & 96.1 & 81.3 & 89.6 & 87.7 \\
MDDA {[}6{]}     & 78.6 & 98.8 & 93.9 & 79.3 & 79.3 & 88.1 \\
CMSS {[}16{]}    & 75.3 & 99.0 & 97.7 & 88.4 & 93.7 & 90.8 \\
LtC-MSDA{[}42{]} & 85.6 & 99.0 & 98.3 & 83.2 & 93.0 & 91.8 \\
PAFDR {[}4{]}      & 88.9 & 99.3 & 98.7 & 91.2 & 96.8 & 94.5 \\
STEM{[}29{]}     & 89.7 & \underline{99.4} & 98.4 & 89.9 & \textbf{97.5} & 95.0 \\
DIDA-Net{[}9{]}  & 85.7 & 99.3 & 98.6 & \underline{91.7} & \underline{97.3} & 94.5 \\
Tl-SNR           & \underline{90.6} & \textbf{99.5} & \underline{99.0} & \underline{91.7} & 97.0 & \underline{95.6} \\ \hline
PAFDR(ours)      & 91.4 & 99.3 & \textbf{99.2} & \textbf{91.8} & \textbf{97.5} & \textbf{95.8} \\ \hline
\end{tabular}
\end{minipage}
\end{table*}


% \begin{table}[t]
% \centering
% \caption{Comparison of Classification Accuracy on DomainNet.}
% \begin{tabular}{c|ccccccc}
% \hline
% Methods           & Clp  & Inf  & Pnt  & Qdr  & Rel  & Skt  & Avg  \\ \hline
% M3SDA{[}5{]}      & 58.6 & 26.0 & 52.3 & 6.3  & 62.7 & 49.5 & 42.6 \\
% PAFDR               & 63.8 & 27.6 & 54.5 & 15.8 & 63.8 & 54.5 & 46.7 \\
% T-SVDNet {[}44{]} & 66.1 & 25.0 & 54.3 & 16.5 & 65.4 & 54.6 & 47.0 \\
% PTMDA {[}41{]}    & 66.0 & 28.5 & 58.4 & 13.0 & 63.0 & 54.1 & 47.2 \\
% SPS {[}45{]}      & 70.8 & 24.6 & 55.2 & 19.4 & 67.5 & 57.6 & 49.2 \\
% DSFE {[}46{]}     & 68.2 & 25.8 & 58.8 & 18.3 & 71.9 & 57.6 & 50.1 \\
% MIEM {[}50{]}     & 69.0 & 28.6 & 58.7 & 20.5 & 68.9 & 59.2 & 50.8 \\ \hline
% PAFDR(ours)       & 69.4 & 29.6 & 59.3 & 21.1 & 69.3 & 60.7 & 51.6 \\ \hline
% \end{tabular}
% \end{table}


% \begin{table}[t]
% \centering
% \caption{Comparison of Classification Accuracy on Digits-5.}
% \begin{tabular}{c|cccccc}
% \hline
% Methods          & mm   & mt   & up   & SV   & syn  & Avg  \\ \hline
% M3SDA{[}5{]}     & 72.8 & 98.4 & 96.1 & 81.3 & 89.6 & 87.7 \\
% MDDA {[}6{]}     & 78.6 & 98.8 & 93.9 & 79.3 & 79.3 & 88.1 \\
% CMSS {[}16{]}    & 75.3 & 99.0 & 97.7 & 88.4 & 93.7 & 90.8 \\
% LtC-MSDA{[}42{]} & 85.6 & 99.0 & 98.3 & 83.2 & 93.0 & 91.8 \\
% PAFDR {[}4{]}      & 88.9 & 99.3 & 98.7 & 91.2 & 96.8 & 94.5 \\
% STEM{[}29{]}     & 89.7 & 99.4 & 98.4 & 89.9 & 97.5 & 95.0 \\
% DIDA-Net{[}9{]}  & 85.7 & 99.3 & 98.6 & 91.7 & 97.3 & 94.5 \\
% Tl-SNR           & 90.6 & 99.5 & 99.0 & 91.7 & 97.0 & 95.6 \\ \hline
% PAFDR(ours)      & 91.4 & 99.3 & 99.2 & 91.8 & 97.5 & 95.8 \\ \hline
% \end{tabular}
% \end{table}



\subsection{Comparison with Existing Approaches}
\subsubsection{\textbf{Results on Domain Generalization}}
% We evaluated the generalization of our proposed method through two types of experiments: First, a comparison with existing approaches on multi-source domain generalization  tasks. Second, a comparison with existing approaches methods on single-source domain generalization  tasks.
Since the PAFDR module has the ability to mitigate domain gaps and recover task-relevant features, it can enhance the generalization capability of the network while maintaining its discriminability. 
% We evaluated PAFDR's effectiveness in DG  by comparing it with existing methods in Table 1 and Table 3.
We evaluated PAFDR's effectiveness capability by experiments on DG tasks, comparing against existing approaches.

 We evaluate our PAFDR against these leading domain generalization approaches. (1) DeepAll involves training a singular pre-trained ResNet using combined data from all source domains with cross-entropy loss. This trained classifier is subsequently applied directly to target domain examples. (2) Representation learning encompasses alignment-oriented techniques (CORAL [18] and LDDG [23]), disentanglement approaches (PAFDR [4]), adversarial training methods (SagNet [14]), attention mechanisms (I2-ADR [15]), causality-based frameworks (CIRL [28]), and self-supervision approaches (JiGen [29], SelfReg [30], and PCL [31]). (3) Data augmentation incorporates style transfer techniques (MixStyle [32], StyleNeophile [33], and STEAM [68]) and domain randomization strategies (FACT [35]). (4) Learning strategies cover ensemble-based frameworks (BSF [8], BNE [9], DAEL [5], SWAD [69], KDDRL [52], XDED [40], DART [41], and EoA [42]) and meta-learning approaches (L2A-OT [38] and MVDG [44]). We select PAFDR \cite{jin2021style} as our baseline, which is a strong method in decomposition-based domain generalization. The comparative results are displayed in Tables I and II.

As shown in Table 1, our proposed PAFDR method consistently outperforms all competing methods on both datasets. On the PACS dataset, PAFDR achieves the best average accuracy of 84.9, surpassing the second-best method KDDRL+ (84.7) by 0.2 percentage points. Notably, PAFDR achieves the highest accuracy on Art (84.8) and Photo (96.4) domains, and competitive results on Cartoon (79.5, second only to Tl-PAFDR's 79.7) and Sketch (79.1, second only to KDDRL+'s 82.2).
On the Office-Home dataset, PAFDR demonstrates even more significant improvements, achieving an average accuracy of 66.8, which outperforms the second-best method KDDRL+ (66.2) by 0.6 percentage points. Our method attains the best results on three out of four domains: Art (61.3), Clipart (55.2), and Product (75.1), while achieving competitive performance on the Real domain (75.7, second only to MixStyle's 75.9).
Particularly worth noting is that compared to the baseline PAFDR method [4], our PAFDR achieves substantial improvements of 3.1 percentage points on PACS (81.8 → 84.9) and 0.7 percentage points on Office-Home (66.1 → 66.8). This significant performance gain highlights the effectiveness of our dynamic adaptive feature disentanglement and recalibration mechanism, which can better separate domain-invariant and domain-specific features while adaptively adjusting their importance for different target domains.
The superior performance of our method across diverse visual domains demonstrates the robustness and generalization of PAFDR, making it a promising approach for practical domain generalization applications.

% \noindent \textbf{Single-Source Domain Generalization  Experiment Results.} 

% \begin{table}[]
% \caption{Comparison with existing methods on single-source DG .}         %表标题
% \resizebox{\linewidth}{!}{
% \begin{tabular}{c|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{M→D} & \multicolumn{2}{c}{D→M} \\ \cline{2-5} 
%        & Rank-1 & mAP & Rank-1 & mAP \\ \hline
% IBN-Net  \cite{pan2018two}   & 43.7 & 24.3 & 24.0 & 23.5 \\ \hline
% CrossGrad \cite{shankar2018generalizing}    & 48.5 & 27.1 & 56.7 & 26.3 \\ \hline

% OSNet \cite{zhou2019osnet}        & 44.7 & 25.9 & 52.2 & 24.0 \\ \hline
% % OSNet-IBN \cite{zhou2022learning}    & 47.9 & 27.6 & 57.8 & 27.4 \\ \hline
% QAConv \cite{liao2020interpretable}       & 48.8 & 28.7 & 58.6 & 27.2 \\ \hline
% L2A-OT \cite{zhou2020learning}       & 50.1 & 29.2 & 63.8 & 30.2 \\ \hline
% PAFDR \cite{jin2021style}     & 55.1 & 33.6 & 66.7 & 33.9 \\ \hline
% MetaBIN \cite{choi2021meta} & 55.2 & 33.1 & 69.2 & 35.9 \\ \hline
% OSNet-AIN \cite{zhou2022learning}    & 52.4 & 30.5 & 61.0 & 30.6 \\ \hline
% DTIN-Net \cite{jiao2022dynamically} & \underline{57.0}  & \underline{36.1}  & \textbf{69.8}  & 37.4  \\ \hline
% SuA-SpML \cite{zhang2023style} & 54.4 & 33.4 & 64.1 & 35.2 \\ \hline
% PAFDR (ours)  & \textbf{57.2} & \textbf{36.4} & \underline{69.6} & \textbf{37.8} \\ \hline
% \end{tabular}}
% \end{table}

% \begin{table}[]
% \caption{Comparison with existing methods on single-source DG .}         %表标题
% \begin{tabular}{c|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{M→D} & \multicolumn{2}{c}{D→M} \\ \cline{2-5} 
%        & Rank-1 & mAP & Rank-1 & mAP \\ \hline
% IBN-Net  \cite{pan2018two}   & 43.7 & 24.3 & 24.0 & 23.5 \\ \hline
% CrossGrad \cite{shankar2018generalizing}    & 48.5 & 27.1 & 56.7 & 26.3 \\ \hline
% OSNet \cite{zhou2019osnet}        & 44.7 & 25.9 & 52.2 & 24.0 \\ \hline
% % OSNet-IBN \cite{zhou2022learning}    & 47.9 & 27.6 & 57.8 & 27.4 \\ \hline
% QAConv \cite{liao2020interpretable}       & 48.8 & 28.7 & 58.6 & 27.2 \\ \hline
% L2A-OT \cite{zhou2020learning}       & 50.1 & 29.2 & 63.8 & 30.2 \\ \hline
% PAFDR \cite{jin2021style}     & 55.1 & 33.6 & 66.7 & 33.9 \\ \hline
% MetaBIN \cite{choi2021meta} & 55.2 & 33.1 & 69.2 & 35.9 \\ \hline
% OSNet-AIN \cite{zhou2022learning}    & 52.4 & 30.5 & 61.0 & 30.6 \\ \hline
% DTIN-Net \cite{jiao2022dynamically} & \underline{57.0}  & \underline{36.1}  & \textbf{69.8}  & 37.4  \\ \hline
% SuA-SpML \cite{zhang2023style} & 54.4 & 33.4 & 64.1 & 35.2 \\ \hline
% PAFDR (ours)  & \textbf{57.2} & \textbf{36.4} & \underline{69.6} & \textbf{37.8} \\ \hline
% \end{tabular}
% \end{table}

\subsubsection{\textbf{Results on Unsupervised Domain Adaptation}}
Our PAFDR method was evaluated on two representative multi-source domain adaptation datasets: DomainNet and Digits-5. The experimental results demonstrate that our proposed PAFDR method achieved state-of-the-art average performance on both datasets.

On the DomainNet dataset, PAFDR achieved an average accuracy of 51.6 across six domains, surpassing the current best method MIEM (50.8) by 0.8 percentage points. Specifically, PAFDR achieved the best performance in four domains: Infograph (29.6), Painting (59.3), QuickDraw (21.1), and Sketch (60.7). Notably, on the challenging QuickDraw domain, PAFDR reached an accuracy of 21.1, showing significant improvement over baseline methods.

On the Digits-5 dataset, PAFDR also demonstrated excellent performance, achieving an average accuracy of 95.8. This represents a 0.2 percentage point improvement over the existing best method Tl-SNR (95.6). Notably, PAFDR achieved the best performance in three domains: mm (91.4), up (99.2), and sv (91.8). Particularly impressive is the improvement in the mm domain, which has significant domain shift, where accuracy increased to 91.4, showing a 2.5 percentage point improvement over the baseline PAFDR method (88.9).

In conclusion, the experimental results indicate that PAFDR has significant advantages in handling multi-source domain adaptation tasks. It not only outperforms existing methods in overall performance but also achieves notable improvements in challenging domains (such as the QuickDraw domain in DomainNet and the mm domain in Digits-5). These results validate the effectiveness and robustness of our proposed method in addressing domain shift problems.



\subsection{Ablation Study} 
\label{sec:ablation}
We conduct comprehensive ablation studies to demonstrate the effectiveness of dual attention in PAFDR and its Asymmetric task-relevant Feature Decomposition Loss.

\subsubsection{\textbf{Effect of Spatial Attention and Channel Attention}}
To validate the effectiveness of the attention mechanism in PAFDR, we conducted detailed ablation experiments on both PACS and Office-Home datasets. Specifically, we compared several variants: a baseline model without any attention mechanism (PAFDR w/o attention), a model without spatial attention (PAFDR w/o spatial attention), a model without channel attention (PAFDR w/o channel attention), and a model using serial attention (PAFDR w series attentions).

On the PACS dataset, experimental results show that the complete PAFDR model achieved the best performance across all domains, reaching an average accuracy of 84.9. In contrast, the baseline model without any attention mechanism only achieved an average accuracy of 80.0, indicating that the attention mechanism brought a significant performance improvement of 4.9 percentage points. Specifically, the models without spatial attention and channel attention achieved average accuracies of 83.1 and 83.2 respectively, demonstrating that both attention mechanisms make important contributions to model performance. Notably, while the serial attention model (84.2) outperformed single-attention variants, it still fell short of our proposed parallel attention structure (84.9).

On the Office-Home dataset, we observed similar trends. The complete PAFDR model achieved an average accuracy of 69.2, showing an improvement of 5.8 percentage points compared to the baseline model (63.4). The models without spatial attention and channel attention achieved average accuracies of 67.2 and 67.6 respectively, further confirming the importance of both attention mechanisms. The serial attention model (68.8) similarly underperformed compared to our proposed parallel structure.

The ablation study results clearly demonstrate that the attention mechanism plays a crucial role in improving model performance, bringing significant improvements of 4.9 and 5.8 percentage points on the PACS and Office-Home datasets, respectively. The experiments also confirm that both spatial and channel attention mechanisms are essential, as the absence of either leads to performance degradation. Furthermore, our proposed parallel attention structure outperforms the traditional serial structure, validating the rationality of our design choices. These findings not only confirm the importance of attention mechanisms in domain generalization tasks but also highlight the superiority of our proposed parallel attention structure.

% We investigate the effectiveness of the proposed task-relevant feature decomposition method that integrates spatial attention and channel attention. As shown in Table 3, in Market1501→Duke, the scheme with the complete spatial-channel attention achieved the best performance, with a 12.4\% increase in Rank-1 accuracy and a 9.2\% increase in mAP compared to the scheme with attention completely removed. In Duke→Market1501, the scheme with the complete spatial-channel attention also achieved the best performance, with a 29.9\%  increase in Rank-1 accuracy and a 9.0\% increase in mAP compared to the scheme with attention completely removed.
% These data indicate that combining spatial and channel attention can better decouple task-relevant and task-irrelevant features. The two attention mechanisms each have their unique advantages, and their complementary strengths enhance the overall feature decomposition effect.
% We conduct ablation studies to analyze the effectiveness of attention mechanisms in our PAFDR framework. Table 3 shows the experimental results on two DG  scenarios (M→D and D→M).

% Without any attention modules ("PAFDR w/o attention"), the performance drops significantly, where Rank-1 accuracy decreases by 13.0 (from 57.2 to 44.2) on M→D and 32.0 (from 69.6 to 37.6) on D→M, highlighting the importance of attention mechanisms. When examining individual components, removing spatial attention results in Rank-1 accuracy of 55.3 (M→D) and 66.7 (D→M), while removing channel attention achieves similar results with 55.6 (M→D) and 66.9 (D→M), indicating both attention types contribute comparably.

% Additionally, we compare parallel (PAFDR) versus series ("PAFDR w series attentions") arrangement of attention modules. The parallel design achieves better performance, with improvements of 0.5 and 1.2 in Rank-1 accuracy on M→D and D→M, respectively. These results demonstrate that both spatial and channel attention mechanisms are essential components, and their parallel arrangement is optimal for cross-domain  re-identification. A possible explanation is that, compared to sequential concatenation, parallel spatial and channel attention mechanisms work independently without strong correlations, and this independent operation of spatial and channel attention produces some regularization effects.
% \begin{table}[t]
% \centering
% \caption{Study on the channel and spatial attention.}
% \begin{tabular}{c|cccc|c}
% \hline
% Methods & Art & Cartoon & Photo & Sketch & Avg. \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o\\attention {[}30{]}\end{tabular} & 82.3 & 78.4 & \underline{96.2} & 77.5 & 83.6 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o\\spatial attention\end{tabular} & 83.4 & \textbf{79.7} & \underline{96.2} & 75.2 & 83.6 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o\\channel attention {[}32{]}\end{tabular} & 84.1 & 78.8 & 96.1 & 75.9 & 83.7 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w\\series attentions{[}52{]}\end{tabular} & 82.3 & 78.9 & 95.6 & \textbf{82.2} & \underline{84.7} \\ \hline
% PAFDR (ours) & \textbf{84.8} & \underline{79.5} & \textbf{96.4} & \underline{79.1} & \textbf{84.9} \\ \hline
% \end{tabular}
% \end{table}

% \begin{table}[t]
% \centering
% \caption{Study on the channel and spatial attention.}
% \resizebox{\columnwidth}{!}{%
% \begin{tabular}{c|cccc|c}
% \hline
% Methods & Art & Cartoon & Photo & Sketch & Avg. \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o\\attention {[}30{]}\end{tabular} & 82.3 & 78.4 & \underline{96.2} & 77.5 & 83.6 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o\\spatial attention\end{tabular} & 83.4 & \textbf{79.7} & \underline{96.2} & 75.2 & 83.6 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o\\channel attention {[}32{]}\end{tabular} & 84.1 & 78.8 & 96.1 & 75.9 & 83.7 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w\\series attentions{[}52{]}\end{tabular} & 82.3 & 78.9 & 95.6 & \textbf{82.2} & \underline{84.7} \\ \hline
% PAFDR (ours) & \textbf{84.8} & \underline{79.5} & \textbf{96.4} & \underline{79.1} & \textbf{84.9} \\ \hline
% \end{tabular}%
% }
% \end{table}

\begin{table*}[t]
\centering
\caption{Ablation study on the channel and spatial attention.}
\begin{tabular}{c|ccccc|ccccc}
\hline
Datasets          & \multicolumn{5}{c|}{PACS}                                                                          & \multicolumn{5}{c}{Office-Home}                                                                    \\ \hline
Methods           & Art           & Cartoon       & Photo         & \multicolumn{1}{c|}{Sketch}        & Avg.          & Art           & Clipart       & Product       & \multicolumn{1}{c|}{Real}          & Avg.          \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\attention {[}30{]}\end{tabular} & 79.3          & 75.1 & 94.2    & \multicolumn{1}{c|}{75.2}          & 80.0         &56.7               &51.5              &71.8              & \multicolumn{1}{c|}{73.5}             & 63.4             \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\spatial attention\end{tabular} & 82.4          & 78.7          & 94.6          & \multicolumn{1}{c|}{76.5}          &83.1           & 62.3          & 55.3          & 75.2          & \multicolumn{1}{c|}{76.1} &67.2           \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\channel attention {[}32{]}\end{tabular} & 82.7          & 78.5          & 94.9          & \multicolumn{1}{c|}{76.8}          & 83.2          & 62.0          & 55.9          & 75.6          & \multicolumn{1}{c|}{76.7}  &67.6           \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w\\series attentions{[}52{]}\end{tabular} & 84.1          & 78.9          & 95.7          & \multicolumn{1}{c|}{78.2} & 84.2    & 62.4          & 58.1    & 76.8    & \multicolumn{1}{c|}{77.9}          & 68.8    \\ \hline
PAFDR (ours)       & \textbf{84.8} & \textbf{79.5}    & \textbf{96.4} & \multicolumn{1}{c|}{\textbf{79.1}}    & \textbf{84.9} & \textbf{62.7} & \textbf{58.4} & \textbf{77.5} & \multicolumn{1}{c|}{\textbf{78.2}}    & \textbf{69.2} \\ \hline
\end{tabular}
\end{table*}

% \begin{table}[t]
% \centering
% \caption{Study on the asymmetric task-relevant feature decomposition loss.}
% \resizebox{\columnwidth}{!}{%
% \begin{tabular}{c|cccc|c}
% \hline
% Methods & Art & Cartoon & Photo & Sketch & Avg. \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o $\mathcal{L}_{ATFD}$ {[}30{]}\end{tabular} & 82.3 & 78.4 & \underline{96.2} & 77.5 & 83.6 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o $\mathcal{L}_{ATFD}^{+}${[}32{]}\end{tabular} & 83.4 & \textbf{79.7} & \underline{96.2} & 75.2 & 83.6 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w/o $\mathcal{L}_{ATFD}^{-}$ {[}32{]}\end{tabular} & 84.1 & 78.8 & 96.1 & 75.9 & 83.7 \\ \hline
% \begin{tabular}[c]{@{}c@{}}PAFDR w $\mathcal{L}_{baseline}${[}52{]}\end{tabular} & 82.3 & 78.9 & 95.6 & \textbf{82.2} & \underline{84.7} \\ \hline
% PAFDR (ours) & \textbf{84.8} & \underline{79.5} & \textbf{96.4} & \underline{79.1} & \textbf{84.9} \\ \hline
% \end{tabular}%
% }
% \end{table}

\begin{table*}[t]
\centering
\caption{Ablation study on the asymmetric task-relevant feature decomposition loss.}
\begin{tabular}{c|ccccc|ccccc}
\hline
Datasets          & \multicolumn{5}{c|}{PACS}                                                                          & \multicolumn{5}{c}{Office-Home}                                                                    \\ \hline
Methods           & Art           & Cartoon       & Photo         & \multicolumn{1}{c|}{Sketch}        & Avg.          & Art           & Clipart       & Product       & \multicolumn{1}{c|}{Real}          & Avg.          \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w/o $\mathcal{L}_{ATFD}$ {[}30{]}\end{tabular} & 79.6          & 75.7 & 94.3    & \multicolumn{1}{c|}{75.2}          & 81.2          & 55.9             & 51.7             & 71.2             & \multicolumn{1}{c|}{73.3}             & 63.0             \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w/o $\mathcal{L}_{ATFD}^{+}${[}32{]}\end{tabular} & 81.5          & 77.4          & 94.7          & \multicolumn{1}{c|}{76.9}          & 82.6          & 59.5          & 55.8          & 74.6          & \multicolumn{1}{c|}{75.8} & 66.4          \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w/o $\mathcal{L}_{ATFD}^{-}$ {[}32{]}\end{tabular} & 82.1          & 77.2          & 95.0          & \multicolumn{1}{c|}{76.4}          & 82.7          & 59.6          & 56.2          & 75.3          & \multicolumn{1}{c|}{76.0}  & 66.8          \\ \hline
\begin{tabular}[c]{@{}c@{}}PAFDR w $\mathcal{L}_{baseline}${[}52{]}\end{tabular} & 83.7          & 78.8          & 95.6          & \multicolumn{1}{c|}{\textbf{79.2}} & 84.3    & 62.3          & 58.1    & 74.9    & \multicolumn{1}{c|}{\textbf{78.4}}          & 68.4    \\ \hline
PAFDR (ours)       & \textbf{84.8} & \textbf{79.5}    & \textbf{96.4} & \multicolumn{1}{c|}{79.1}    & \textbf{84.9} & \textbf{62.7} & \textbf{58.4} & \textbf{77.5} & \multicolumn{1}{c|}{78.2}    & \textbf{69.2} \\ \hline
\end{tabular}
\end{table*}

% % ￥自适应大小表格
% \begin{table}[]
% \caption{Study on the channel and spatial attention.}
% \resizebox{\linewidth}{!}{
% \begin{tabular}{ccccc}
% \hline
% \multicolumn{1}{c|}{\multirow{2}{*}{Method}}     & \multicolumn{2}{c|}{M→D}                                               & \multicolumn{2}{c}{D→M}                                   \\ \cline{2-5} 
% \multicolumn{1}{c|}{}                            & \multicolumn{1}{c|}{Rank-1}        & \multicolumn{1}{c|}{mAP}           & \multicolumn{1}{c|}{Rank-1}        & mAP                  \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\ attention\end{tabular}}         & \multicolumn{1}{c|}{44.2}          & \multicolumn{1}{c|}{25.6}          & \multicolumn{1}{c|}{37.6}          & 26.8                 \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\ spatial attention\end{tabular}} & \multicolumn{1}{c|}{55.3}          & \multicolumn{1}{c|}{33.4}          & \multicolumn{1}{c|}{66.7}          & 34.5                 \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\ channel attention\end{tabular}} & \multicolumn{1}{c|}{55.6}          & \multicolumn{1}{c|}{33.8}          & \multicolumn{1}{c|}{66.9}          & 35.1                 \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w\\ series attentions\end{tabular}} & \multicolumn{1}{c|}{56.7}          & \multicolumn{1}{c|}{35.5}          & \multicolumn{1}{c|}{68.4}          & 36.7                 \\ \hline
% \multicolumn{1}{c|}{PAFDR}                & \multicolumn{1}{c|}{\textbf{57.2}} & \multicolumn{1}{c|}{\textbf{36.4}} & \multicolumn{1}{c|}{\textbf{69.6}} & \textbf{37.8}        \\ \hline
% \end{tabular}}
% \end{table}

% \begin{table}[]
% \caption{Study on the channel and spatial attention.}
% \begin{tabular}{ccccc}
% \hline
% \multicolumn{1}{c|}{\multirow{2}{*}{Method}}     & \multicolumn{2}{c|}{M→D}                                               & \multicolumn{2}{c}{D→M}                                   \\ \cline{2-5} 
% \multicolumn{1}{c|}{}                            & \multicolumn{1}{c|}{Rank-1}        & \multicolumn{1}{c|}{mAP}           & \multicolumn{1}{c|}{Rank-1}        & mAP                  \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\ attention\end{tabular}}         & \multicolumn{1}{c|}{44.2}          & \multicolumn{1}{c|}{25.6}          & \multicolumn{1}{c|}{37.6}          & 26.8                 \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\ spatial attention\end{tabular}} & \multicolumn{1}{c|}{55.3}          & \multicolumn{1}{c|}{33.4}          & \multicolumn{1}{c|}{66.7}          & 34.5                 \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w/o\\ channel attention\end{tabular}} & \multicolumn{1}{c|}{55.6}          & \multicolumn{1}{c|}{33.8}          & \multicolumn{1}{c|}{66.9}          & 35.1                 \\ \hline
% \multicolumn{1}{c|}{\begin{tabular}[c]{@{}c@{}}PAFDR w\\ series attentions\end{tabular}} & \multicolumn{1}{c|}{56.7}          & \multicolumn{1}{c|}{35.5}          & \multicolumn{1}{c|}{68.4}          & 36.7                 \\ \hline
% \multicolumn{1}{c|}{PAFDR}                & \multicolumn{1}{c|}{\textbf{57.2}} & \multicolumn{1}{c|}{\textbf{36.4}} & \multicolumn{1}{c|}{\textbf{69.6}} & \textbf{37.8}        \\ \hline
% \end{tabular}
% \end{table}

\subsubsection{\textbf{Influence of the asymmetric task-relevant feature decomposition loss}}
$\mathcal{L}_{ATFD}$.
To investigate the effectiveness of the asymmetric task-relevant feature decomposition (ATFD) loss, we conducted detailed ablation experiments on both PACS and Office-Home datasets. We designed four comparative experiments: a model with ATFD loss completely removed, a model with only negative decomposition loss retained, a model with only positive decomposition loss retained, and a model using the baseline loss function from PAFDR.

On the PACS dataset, experimental results show that the complete PAFDR model achieved the best average accuracy of 84.9. When ATFD loss was completely removed, model performance significantly decreased to 81.2, demonstrating the crucial contribution of ATFD loss to model performance. Analyzing the positive and negative decomposition losses separately, we found that removing either component led to performance degradation: accuracy dropped to 82.6 when positive decomposition loss was removed, and to 82.7 when negative decomposition loss was removed. This indicates that both decomposition loss terms play indispensable roles in feature extraction. Notably, when using PAFDR's baseline loss function, the model achieved an accuracy of 84.3, which, while better than single-loss variants, still fell short of our complete ATFD structure, validating the superiority of ATFD loss function in feature decomposition.

On the Office-Home dataset, we observed similar performance trends. The complete PAFDR model achieved the best average accuracy of 69.2, while performance significantly dropped to 63.0 when ATFD loss was removed. Removing positive or negative decomposition loss led to accuracy decreases to 66.4 and 66.8 respectively, again confirming the necessity of both loss terms. The model using PAFDR baseline loss function achieved an accuracy of 68.4, also lower than our complete model.

Through these detailed ablation experiments, we can conclude that: ATFD loss function plays a crucial role in improving model performance, bringing significant improvements of 3.7 and 6.2 percentage points on PACS and Office-Home datasets respectively; the synergistic effect of positive and negative decomposition losses is crucial for effective feature decomposition, with performance degrading when either component is missing; our proposed ATFD structure demonstrates stronger feature decomposition capabilities compared to PAFDR's baseline loss function, which not only confirms the importance of asymmetric feature decomposition in domain generalization tasks but also validates the rationality and effectiveness of our loss function design.
% This loss consists of an enhancement loss $\mathcal{L}_{ATFD}^{+}$ and a suppression loss $\mathcal{L}_{ATFD}^{-}$. In the Market1501→Duke, the complete PAFDR with $\mathcal{L}_{ATFD}$ achieves the best performance, with a 3.5\% increase in Rank-1 accuracy and a 6.2\% improvement in mAP compared to the scheme without this loss. In the Duke→ Market1501, the complete PAFDR with $\mathcal{L}_{ATFD}$ also achieves the best performance, with a 2.8\% increase in Rank-1 accuracy and a 2.2\% improvement in mAP compared to the scheme without this loss. Experimental results show that the asymmetric task-relevant feature decomposition loss effectively helps the model to decompose task-relevant and task-irrelevant features. Moreover, both the enhancement loss $\mathcal{L}_{ATFD}^{+}$ and suppression loss $\mathcal{L}_{ATFD}^{-}$ are indispensable, and their synergistic effect is the key to the model's good performance.
% We conduct ablation experiments to evaluate the effectiveness of our proposed asymmetric task-relevant feature decomposition (ATFD) loss. As shown in Table 4, we analyze different components of the loss function across two DG  scenarios: Market-1501 to DukeMTMC- (M→D) and DukeMTMC- to Market-1501 (D→M).

% When removing the entire ATFD loss ($\mathcal{L}_{ATFD}$), the performance drops significantly, with Rank-1 accuracy decreasing by 4.1 (from 57.2 to 53.1) on M→D and 4.9 (from 69.6 to 64.7) on D→M. We further investigate the impact of individual components. Removing the enhancement loss ($\mathcal{L}_{ATFD}^{+}$) leads to a performance drop of 2.7 and 4.6 in Rank-1 accuracy on M→D and D→M, respectively. Similarly, without the suppression loss ($\mathcal{L}_{ATFD}^{-}$), the model shows decreased performance with Rank-1 accuracy dropping to 55.3 on M→D and 66.2 on D→M. Both the enhancement loss $\mathcal{L}_{ATFD}^{+}$ and suppression loss $\mathcal{L}_{ATFD}^{-}$ are indispensable, and their synergistic effect is the key to the model's good performance.

% Additionally, we also compared with the baseline's loss function $\mathcal{L}_{baseline}$, which adopts a symmetric design. Compared to $\mathcal{L}_{baseline}$, our complete ATFD loss $\mathcal{L}_{ATFD}$ demonstrates consistent improvements across all metrics, with gains of 0.4 in Rank-1 accuracy and 1.5 in mAP on M→D, and 1.2 in Rank-1 accuracy and 1.6 in mAP on D→M. These results verify that each component of our ATFD loss contributes to the overall performance, with the complete loss function achieving the best results in cross-domain  . The results demonstrate the superiority of our asymmetric design in the Loss function. The asymmetric loss function can impose the most appropriate constraints on different features, thereby promoting more thorough decomposition of task-related and task-irrelevant features while enhancing feature discriminability.


% %%自适应大小表格
% \begin{table}[]
% \caption{Study on the asymmetric task-relevant feature decomposition loss.}
% \resizebox{\linewidth}{!}{
% \begin{tabular}{c|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{M→D} & \multicolumn{2}{c}{D→M} \\ \cline{2-5} 
%                         & \multicolumn{1}{c|}{Rank-1} & mAP & \multicolumn{1}{c|}{Rank-1} & mAP \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}$ & \multicolumn{1}{c|}{53.1} & 28.6 & \multicolumn{1}{c|}{64.7} & 33.6 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{+}$ & \multicolumn{1}{c|}{54.5} & 32.4 & \multicolumn{1}{c|}{65.0} & 34.5 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{-}$ & \multicolumn{1}{c|}{55.3} & 33.3 & \multicolumn{1}{c|}{66.2} & 35.7 \\ \hline
% PAFDR w $\mathcal{L}_{baseline}$ & \multicolumn{1}{c|}{56.8} & 34.9 & \multicolumn{1}{c|}{68.4} & 36.2 \\ \hline
% PAFDR & \multicolumn{1}{c|}{\textbf{57.2}} & \textbf{36.4} & \multicolumn{1}{c|}{\textbf{69.6}} & \textbf{37.8} \\ \hline
% \end{tabular}}
% \end{table}

% \begin{table}[]
% \caption{Study on the asymmetric task-relevant feature decomposition loss.}
% \begin{tabular}{c|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{M→D} & \multicolumn{2}{c}{D→M} \\ \cline{2-5} 
%                         & \multicolumn{1}{c|}{Rank-1} & mAP & \multicolumn{1}{c|}{Rank-1} & mAP \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}$ & \multicolumn{1}{c|}{53.1} & 28.6 & \multicolumn{1}{c|}{64.7} & 33.6 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{+}$ & \multicolumn{1}{c|}{54.5} & 32.4 & \multicolumn{1}{c|}{65.0} & 34.5 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{-}$ & \multicolumn{1}{c|}{55.3} & 33.3 & \multicolumn{1}{c|}{66.2} & 35.7 \\ \hline
% PAFDR w $\mathcal{L}_{baseline}$ & \multicolumn{1}{c|}{56.8} & 34.9 & \multicolumn{1}{c|}{68.4} & 36.2 \\ \hline
% PAFDR & \multicolumn{1}{c|}{\textbf{57.2}} & \textbf{36.4} & \multicolumn{1}{c|}{\textbf{69.6}} & \textbf{37.8} \\ \hline
% \end{tabular}
% \end{table}

\subsubsection{\textbf{Effect of topology-aware HGA}}
\noindent \textbf{Influence of the asymmetric task-relevant feature decomposition loss} $\mathcal{L}_{ATFD}$.
To investigate the effectiveness of the asymmetric task-relevant feature decomposition (ATFD) loss, we conducted detailed ablation experiments on both PACS and Office-Home datasets. We designed four comparative experiments: a model with ATFD loss completely removed, a model with only negative decomposition loss retained, a model with only positive decomposition loss retained, and a model using the baseline loss function from PAFDR.

On the PACS dataset, experimental results show that the complete PAFDR model achieved the best average accuracy of 84.9. When ATFD loss was completely removed, model performance significantly decreased to 81.2, demonstrating the crucial contribution of ATFD loss to model performance. Analyzing the positive and negative decomposition losses separately, we found that removing either component led to performance degradation: accuracy dropped to 82.6 when positive decomposition loss was removed, and to 82.7 when negative decomposition loss was removed. This indicates that both decomposition loss terms play indispensable roles in feature extraction. Notably, when using PAFDR's baseline loss function, the model achieved an accuracy of 84.3, which, while better than single-loss variants, still fell short of our complete ATFD structure, validating the superiority of ATFD loss function in feature decomposition.

On the Office-Home dataset, we observed similar performance trends. The complete PAFDR model achieved the best average accuracy of 69.2, while performance significantly dropped to 63.0 when ATFD loss was removed. Removing positive or negative decomposition loss led to accuracy decreases to 66.4 and 66.8 respectively, again confirming the necessity of both loss terms. The model using PAFDR baseline loss function achieved an accuracy of 68.4, also lower than our complete model.

Through these detailed ablation experiments, we can conclude that: ATFD loss function plays a crucial role in improving model performance, bringing significant improvements of 3.7 and 6.2 percentage points on PACS and Office-Home datasets respectively; the synergistic effect of positive and negative decomposition losses is crucial for effective feature decomposition, with performance degrading when either component is missing; our proposed ATFD structure demonstrates stronger feature decomposition capabilities compared to PAFDR's baseline loss function, which not only confirms the importance of asymmetric feature decomposition in domain generalization tasks but also validates the rationality and effectiveness of our loss function design.
% This loss consists of an enhancement loss $\mathcal{L}_{ATFD}^{+}$ and a suppression loss $\mathcal{L}_{ATFD}^{-}$. In the Market1501→Duke, the complete PAFDR with $\mathcal{L}_{ATFD}$ achieves the best performance, with a 3.5\% increase in Rank-1 accuracy and a 6.2\% improvement in mAP compared to the scheme without this loss. In the Duke→ Market1501, the complete PAFDR with $\mathcal{L}_{ATFD}$ also achieves the best performance, with a 2.8\% increase in Rank-1 accuracy and a 2.2\% improvement in mAP compared to the scheme without this loss. Experimental results show that the asymmetric task-relevant feature decomposition loss effectively helps the model to decompose task-relevant and task-irrelevant features. Moreover, both the enhancement loss $\mathcal{L}_{ATFD}^{+}$ and suppression loss $\mathcal{L}_{ATFD}^{-}$ are indispensable, and their synergistic effect is the key to the model's good performance.
% We conduct ablation experiments to evaluate the effectiveness of our proposed asymmetric task-relevant feature decomposition (ATFD) loss. As shown in Table 4, we analyze different components of the loss function across two DG  scenarios: Market-1501 to DukeMTMC- (M→D) and DukeMTMC- to Market-1501 (D→M).

% When removing the entire ATFD loss ($\mathcal{L}_{ATFD}$), the performance drops significantly, with Rank-1 accuracy decreasing by 4.1 (from 57.2 to 53.1) on M→D and 4.9 (from 69.6 to 64.7) on D→M. We further investigate the impact of individual components. Removing the enhancement loss ($\mathcal{L}_{ATFD}^{+}$) leads to a performance drop of 2.7 and 4.6 in Rank-1 accuracy on M→D and D→M, respectively. Similarly, without the suppression loss ($\mathcal{L}_{ATFD}^{-}$), the model shows decreased performance with Rank-1 accuracy dropping to 55.3 on M→D and 66.2 on D→M. Both the enhancement loss $\mathcal{L}_{ATFD}^{+}$ and suppression loss $\mathcal{L}_{ATFD}^{-}$ are indispensable, and their synergistic effect is the key to the model's good performance.

% Additionally, we also compared with the baseline's loss function $\mathcal{L}_{baseline}$, which adopts a symmetric design. Compared to $\mathcal{L}_{baseline}$, our complete ATFD loss $\mathcal{L}_{ATFD}$ demonstrates consistent improvements across all metrics, with gains of 0.4 in Rank-1 accuracy and 1.5 in mAP on M→D, and 1.2 in Rank-1 accuracy and 1.6 in mAP on D→M. These results verify that each component of our ATFD loss contributes to the overall performance, with the complete loss function achieving the best results in cross-domain  . The results demonstrate the superiority of our asymmetric design in the Loss function. The asymmetric loss function can impose the most appropriate constraints on different features, thereby promoting more thorough decomposition of task-related and task-irrelevant features while enhancing feature discriminability.


% %%自适应大小表格
% \begin{table}[]
% \caption{Study on the asymmetric task-relevant feature decomposition loss.}
% \resizebox{\linewidth}{!}{
% \begin{tabular}{c|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{M→D} & \multicolumn{2}{c}{D→M} \\ \cline{2-5} 
%                         & \multicolumn{1}{c|}{Rank-1} & mAP & \multicolumn{1}{c|}{Rank-1} & mAP \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}$ & \multicolumn{1}{c|}{53.1} & 28.6 & \multicolumn{1}{c|}{64.7} & 33.6 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{+}$ & \multicolumn{1}{c|}{54.5} & 32.4 & \multicolumn{1}{c|}{65.0} & 34.5 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{-}$ & \multicolumn{1}{c|}{55.3} & 33.3 & \multicolumn{1}{c|}{66.2} & 35.7 \\ \hline
% PAFDR w $\mathcal{L}_{baseline}$ & \multicolumn{1}{c|}{56.8} & 34.9 & \multicolumn{1}{c|}{68.4} & 36.2 \\ \hline
% PAFDR & \multicolumn{1}{c|}{\textbf{57.2}} & \textbf{36.4} & \multicolumn{1}{c|}{\textbf{69.6}} & \textbf{37.8} \\ \hline
% \end{tabular}}
% \end{table}

% \begin{table}[]
% \caption{Study on the asymmetric task-relevant feature decomposition loss.}
% \begin{tabular}{c|cc|cc}
% \hline
% \multirow{2}{*}{Method} & \multicolumn{2}{c|}{M→D} & \multicolumn{2}{c}{D→M} \\ \cline{2-5} 
%                         & \multicolumn{1}{c|}{Rank-1} & mAP & \multicolumn{1}{c|}{Rank-1} & mAP \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}$ & \multicolumn{1}{c|}{53.1} & 28.6 & \multicolumn{1}{c|}{64.7} & 33.6 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{+}$ & \multicolumn{1}{c|}{54.5} & 32.4 & \multicolumn{1}{c|}{65.0} & 34.5 \\ \hline
% PAFDR w/o $\mathcal{L}_{ATFD}^{-}$ & \multicolumn{1}{c|}{55.3} & 33.3 & \multicolumn{1}{c|}{66.2} & 35.7 \\ \hline
% PAFDR w $\mathcal{L}_{baseline}$ & \multicolumn{1}{c|}{56.8} & 34.9 & \multicolumn{1}{c|}{68.4} & 36.2 \\ \hline
% PAFDR & \multicolumn{1}{c|}{\textbf{57.2}} & \textbf{36.4} & \multicolumn{1}{c|}{\textbf{69.6}} & \textbf{37.8} \\ \hline
% \end{tabular}
% \end{table}


\begin{figure*}[!t]
    \centering
    \includegraphics[width=0.7\textwidth]{imgs/activation_out_0319.pdf}
    \vspace{-6pt}
    \caption{
    Activation maps of our proposed method (bottom) and the baseline (top) correspond to images of different backgrounds and attributes (taking color temperature variations as an example). Our maps are more consistent/invariant to background variations and changes in image attributes.
    }
    \label{fig:vis_attn}
    \vspace{-10pt}
\end{figure*}

% \begin{figure*}[!t]
%     \centering
%     \includegraphics[width=0.64\textwidth]{fig/Vis_attn_new.pdf}
%     \vspace{-6pt}
%     \caption{
%     \textbf{Attention Visualization in different methods on ImageNet.} 
%     The vanilla attention in ViT fails to accurately outline the target objects, leading to fragments and artifacts manifested as abnormally high values for certain tokens. 
%     The proposed topology-aware HGA in HGFormer perfectly outlines target objects, significantly distinguishing between background and foreground.
%     % By contrast, the HGA exhibits superior capacity in regional context and spatial topology.
%     \textit{
%     Zoom in for better view.
%     }
%     }
%     \label{fig:vis_attn}
%     \vspace{-12pt}
% \end{figure*}


\subsection{Visualization}
\label{sec:vis}

% \begin{figure*}[!t]
%     \centering
%     \includegraphics[width=0.64\textwidth]{fig/plot_feat.pdf}
%     \vspace{-10pt}
%     \caption{
%     \textbf{Feature Visualization in different methods on ImageNet}. 
%     (a) Original Image. 
%     (b) ViT. 
%     (c) ViHGNN.
%     (d) HGFormer(Ours).
%     ViT blends the foreground with the background ambiguously. 
%     ViHGNN distinguishes between foreground and background, but its portrayals of objects is rough and unclear. 
%     HGFormer(Ours) significantly highlights the foreground and suppresses the background, achieving a detailed depiction of objects.
%     Note that the parameters and computation cost in these counterparts are similar. 
%     \textit{
%     Zoom in for better view.
%     }
%     }
%     \label{fig:vis_feat}
%     \vspace{-10pt}
% \end{figure*}

\begin{figure*}[!t]
    \centering
    \includegraphics[width=0.7\textwidth]{imgs/acvivation-self-0318.pdf}
    \vspace{-10pt}
    \caption{
    %大象的第二个激活图不太行。
  The activation maps of different features within a PAFDR module (PAFDR 3) show that PAFDR can well separate task-relevant/irrelevant features. The enhanced feature $\tilde{F}^{+}$ has better discriminability than the original feature $F$, while the contaminated feature $\tilde{F}^{-}$ has worse discriminability than the normalized feature $\tilde{F}$.
    }
    \label{fig:vis_feat}
    \vspace{-10pt}
\end{figure*}

To illustrate the inner workings of the proposed HGFormer, we introduce three visualization studies from the following aspects: hypergraph, attention and feature.

\subsubsection{\textbf{Visualization of Feature Map}}
To better understand the working mechanism of the PAFDR module, we visualize the intermediate feature maps of the third PAFDR module (PAFDR3).

In Figure 3, we further compare the activation maps $\tilde{F}^{+}$ from our approach and those from the baseline method by varying the background and attributes (such as color temperature, contrast, saturation, etc.) of input images. We can observe that for images with different backgrounds and attributes, our approach's activation maps remain more consistent/invariant compared to the baseline's activation maps. In contrast, while the baseline's activation maps are relatively resilient to style variations, they become chaotic when the background changes. These findings demonstrate that our approach is more robust to variations in background and attributes. This demonstrates the superiority of our method's spatial and channel attention in jointly extracting domain-invariant features.
% This figure demonstrates the comparison of activation maps between our proposed PAFDR method and the baseline approach when handling image variations. Through experiments across three scenarios - original images, temperature changes, and background variations - the results show that activation maps generated by PAFDR exhibit stronger consistency and invariance under different conditions. Notably, when image backgrounds are altered or image attributes change, our method maintains better focus on the main subject while reducing background interference, thus proving its superior robustness in handling image variations.

% Figure 4 visualizes the activation maps of different features within the PAFDR module. Through the analysis of three different input images, we demonstrate how the model effectively separates task-relevant and task-irrelevant features. From left to right, we show the input image, feature map F, enhanced feature map F̃⁺, task-relevant feature map F̃⁻, and task-irrelevant feature map F̃. The heat maps indicate that our PAFDR module successfully identifies and separates different types of features, where F̃⁻ primarily focuses on task-relevant regions (such as facial features), while F̃ captures more task-irrelevant information (such as clothing and background). These results confirm that the ATFD loss function plays its expected role in the feature separation process.

% To visually demonstrate the feature extraction capability of the PAFDR module, we conducted a visualization analysis of the feature response of the third PAFDR module. Figure 3(b) shows a comparison of the response maps of the original feature $F$ and the enhanced feature $\tilde{F}^{+}$ after adding the task-relevant feature. The results show that the enhanced feature $\tilde{F}^{+}$ has a stronger response in the discriminative areas of the  object, which indicates that the addition of the task-relevant feature indeed enhances the model's perception of task features. And our ATFD loss also plays the expected role.
Figure 4 shows the activation maps of the original feature $\tilde{F}$, normalized feature Fe, enhanced feature 
$\tilde{F}^{+}=\tilde{F}+R^{+}$
and contaminated feature $\tilde{F}^{-}=\tilde{F}+R^{-}$. We observe that the enhanced feature $\tilde{F}^{+}$ recovered with task-relevant feature $R^{+}$ responds highly to the  object regions and can better capture discriminative areas. In contrast, the contaminated feature $\tilde{F}^{-}$, after adding task-irrelevant feature $R^{-}$, exhibits high responses mainly on the background. The enhanced feature shows better response to  object than the original feature, while the contaminated feature shows worse response to  object than the normalized feature.



\subsubsection{\textbf{Visualization of Feature distribution}}
In Figure 6, we visualize feature distributions using t-SNE [65] on the Digit-5 dataset, with mm, mt, sv, and syn as source domains and up as the target domain. By projecting high-dimensional features onto a two-dimensional space, we compare the feature distributions between the baseline PAFDR method and our proposed PAFDR model. The visualization clearly demonstrates that PAFDR achieves better class separation in the feature space, with more compact clustering within classes and clearer boundaries between them, indicating that our approach learns more discriminative feature representations.
% We visualize the feature distributions at the third PAFDR module of the network using t-SNE \cite{van2008visualizing}. They represent the feature distributions of input $F$,  normalized feature $\tilde{F}$, and output 

% As shown in Figure 4 ($\textrm{I}$), in the left subplot, before PAFDR, features extracted from the two datasets ("red": source training dataset Duke; "green": unseen target dataset Market1501) are largely separated and show a clear domain gap. In the middle subplot, within the PAFDR module, this domain gap has been eliminated. In the right subplot, after recovering the task-relevant features, there remains no domain gap between the two datasets.

% As shown in Figure 4 ($\textrm{II}$), the points in different colors and shapes represent  samples of different identities from Market 1501. In the left subplot,  clusters of the same task maintain certain cohesion, and there are gaps between  clusters of different identities. In the middle subplot, after processing by BN and IN,  clusters of the same task become scattered, and the gaps between  clusters of different identities disappear. In the right subplot, after recovering the task-relevant features,  clusters of the same task show stronger cohesion than the original features, and the gaps between  clusters of different identities are larger than in the original features.

% Overall, Figures 5 and 6 jointly demonstrate that our PAFDR can maintain discriminability while enhancing generalization.
% In Figure 3, we visualize the feature distributions from the third PAFDR module of the network using t-SNE. From top to bottom, they represent the input $F$, normalized features $\tilde{F}$, and output $\tilde{F}^{+}$ distributions of the PAFDR module. The original features $F$ from the two datasets ("green": source training dataset Duke; "red": unseen target dataset Market1501) show a clear domain gap. After normalization, the domain gap in features $\tilde{F}$ is eliminated, however, the discriminability is also reduced. After being enhanced by task-related features, $\tilde{F}^{+}$ shows reduced domain gap in feature distribution while preserving discriminability.
% Figure 5 illustrates the distribution visualization of intermediate features before, within, and after the PAFDR module processing. Using t-SNE dimensionality reduction, we visualize sample distributions from the source dataset Duke (red nodes) and the target dataset Market1501 (green nodes). The three subfigures from left to right show the distributions of original features F, intermediate features F̃, and enhanced features F̃⁺. The results demonstrate that through PAFDR module processing, the feature distributions gradually change, ultimately achieving better feature alignment and domain adaptation in F̃⁺, indicating that our method effectively reduces feature discrepancies between source and target domains.
% Figure 6 demonstrates the evolution of feature distributions for  samples with different identities from the Market1501 dataset through the PAFDR module. Using t-SNE visualization, we illustrate the feature distributions at three stages: original features F, intermediate features F̃, and enhanced features F̃⁺. Points in different colors represent  samples of different identities. The visualization reveals significant changes in sample distribution as features progress through the PAFDR module: from initially scattered distributions in F, through reorganization in F̃, to finally forming more compact and distinct task clusters in F̃⁺. This indicates that the PAFDR module effectively enhances task-relevant features and improves the separability between different identities.
\begin{figure}[t]
  \centering
  \includegraphics[width=1\linewidth]{imgs/t-SNE_Digit-Five-0325-5.pdf}
  \caption{%这张图中的a,b序号与图注的1、2不对应。这个图效果一般，可以把子图一换的差一点，或者还是仿照会议论文放两张图
  ($\textrm{I}$) Visualization of distributions of intermediate features before/within/after the PAFDR module using the tool of t-SNE. ‘Red’/‘green’ nodes: samples from source dataset Duke/unseen target dataset Market1501. ($\textrm{II}$) Visualization of distributions of the intermediate features using the t-SNE tool before/within/after the PAFDR module. The points in different colors and shapes represent samples of different identities from Market 1501.}
  \label{fig:onecol}
\end{figure}


% \begin{figure}[t]
%   \centering
%   \includesvg[width=1\linewidth]{samples/pictures/vis_tsne_0224_6.svg}
%   \caption{($\textrm{I}$) Visualization of distributions...}
%   \label{fig:onecol}
% \end{figure}

% \begin{figure}[t]
%   \centering
%   \includegraphics[width=1\linewidth]{}
%   \caption{Visualization of distributions of the intermediate features using the t - SNE tool before/within/after the PAFDR module. The points in different colors and shapes represent  samples of different identities from Market 1501.}
%   \label{fig:onecol}
% \end{figure}

\subsubsection{\textbf{Attention Visualization}}
The vanilla attention in ViT possesses permutation invariance and fully-connected interaction, lacking an inductive bias in local structure and spatial correlation. 
Compared to the implicit modeling, the proposed topology-aware HGA integrates hypergraph topology as perceptual indications to guide aggregation of global and unbiased inforh ghgmation.
We visually compare the attention maps between vanilla attention and HGA in Fig.~\ref{fig:vis_attn}.
By contrast, the four heads in HGA perfectly outline target objects, distinguish between background and foreground, exhibiting superior capacity in regional context and spatial topology.

\subsubsection{\textbf{Feature Visualization}}
The permutation invariance and the fully-connected interaction in ViT disrupt regional context and spatial topology, leading to ambiguous scene understanding in Fig.~\ref{fig:vis_feat}b.
The strict structural inductive bias of cascaded HGConvs in ViHGNN causes locality, over-smoothing and error accumulation, manifesting as rough portrayals in Fig.~\ref{fig:vis_feat}c.
We propose to incorporate the topology perception of HGConv as perceptual indications and the global understanding of transformer for contextual refinement.
As visualized in Fig.~\ref{fig:vis_feat}d, we develop an effective and unitive representation, achieving distinct and detailed scene depiction.


\section{Conclusion}
This paper proposes a novel Parallel Attention-based Feature Decomposition and Recovery (PAFDR) method to address the domain generalization problem in cross-domain  re-identification. Unlike existing methods that only focus on channel-wise feature decomposition, PAFDR achieves more comprehensive decomposition of task-relevant and task-irrelevant features through parallel spatial and channel attention mechanisms. Specifically, this paper first uses Instance Normalization (IN) and Batch Normalization (BN) to eliminate style variations, then extracts and recovers task-relevant features from the removed information through a dual attention mechanism to enhance feature discriminability. Furthermore, the proposed asymmetric task-relevant feature decomposition loss function further promotes effective separation of task-relevant and task-irrelevant features by applying different constraints to features with different discriminative abilities. Extensive experimental results demonstrate that the PAFDR method shows superior domain generalization performance on tasks of different scales. 
This research provides a new approach to solving the domain generalization problem in cross-domain, which has significant implications for improving performance in real-world applications.

\footnotesize
\bibliographystyle{IEEEtran}
% \bibliographystyle{ieeetr} 
% \bibliography{main}
\bibliography{arxiv}



}
\end{document}






