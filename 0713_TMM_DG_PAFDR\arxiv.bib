

@String(PAMI = {IEEE Trans. Pattern Anal. Mach. Intell.})
@String(IJCV = {Int. J. Comput. Vis.})
@String(CVPR= {Proc. IEEE Conf. Comput. Vis. Pattern Recognit.})
@String(ICCV= {Proc. IEEE Int. Conf. Comput. Vis.})
@String(ECCV= {Proc. Eur. Conf. Comput. Vis.})
@String(ICML= {Proc. Int. Conf. Mach. Learn.})
@String(NIPS= {Proc. Adv. Neural Inf. Process. Syst.})
@String(ICPR = {Int. Conf. Pattern Recog.})
@String(BMVC= {Brit. Mach. Vis. Conf.})
@String(TOG= {ACM Trans. Graph.})
@String(TIP  = {IEEE Trans. Image Process.})
@String(TVCG  = {IEEE Trans. Vis. Comput. Graph.})
@String(TNNLS  = {IEEE Trans. Neural Netw. Learn. Syst.})
@String(TMM  = {IEEE Trans. Multimedia})
@String(ACMMM= {ACM Int. Conf. Multimedia})
@String(ICME = {Int. Conf. Multimedia and Expo})
@String(ICASSP=	{ICASSP})
@String(ICIP = {IEEE Int. Conf. Image Process.})
@String(ACCV  = {ACCV})
@String(ICLR = {Proc. Int. Conf. Learn. Represent.})
@String(IJCAI = {IJCAI})
@String(PR   = {Pattern Recognition})
@String(AAAI = {Proc. AAAI Conf. Artif. Intell.})
@String(SIGIR = {SIGIR})
@String(ACL = {ACL})
@String(CVPRW= {IEEE Conf. Comput. Vis. Pattern Recog. Worksh.})
@String(CSVT = {IEEE Trans. Circuits Syst. Video Technol.})
@String(KDD = {Proc. {ACM SIGKDD} Int. Conf. Knowledge Discovery \& Data Mining})
@String(ACL = {Proc. Conf. Association for Computational Linguistics})




@String(SPL	= {IEEE Sign. Process. Letters})
@String(VR   = {Vis. Res.})
@String(JOV	 = {J. Vis.})
@String(TVC  = {The Vis. Comput.})
@String(JCST  = {J. Comput. Sci. Tech.})
@String(CGF  = {Comput. Graph. Forum})
@String(CVM = {Computational Visual Media})




@inproceedings{recht2019imagenet,
  title={Do imagenet classifiers generalize to imagenet?},
  author={Recht, Benjamin and Roelofs, Rebecca and Schmidt, Ludwig and Shankar, Vaishaal},
  booktitle={ICML},
  year={2019}
}

@inproceedings{zhou2022cocoop,
    title={Conditional Prompt Learning for Vision-Language Models},
    author={Zhou, Kaiyang and Yang, Jingkang and Loy, Chen Change and Liu, Ziwei},
    booktitle={CVPR},
    year={2022}
}

@inproceedings{liu2021semi,
  title={Semi-supervised meta-learning with disentanglement for domain-generalised medical image segmentation},
  author={Liu, Xiao and Thermos, Spyridon and O’Neil, Alison and Tsaftaris, Sotirios A},
  booktitle={MICCAI},
  year={2021},
}

@inproceedings{liu2022vmfnet,
  title={vMFNet: Compositionality Meets Domain-generalised Segmentation},
  author={Liu, Xiao and Thermos, Spyridon and Sanchez, Pedro and O'Neil, Alison Q and Tsaftaris, Sotirios A},
  booktitle={MICCAI},
  year={2022}
}

@article{yang2021generalized,
  title={Generalized out-of-distribution detection: A survey},
  author={Yang, Jingkang and Zhou, Kaiyang and Li, Yixuan and Liu, Ziwei},
  journal={arXiv preprint arXiv:2110.11334},
  year={2021}
}

@article{wang2021generalizing,
  title={Generalizing to unseen domains: A survey on domain generalization},
  author={Wang, Jindong and Lan, Cuiling and Liu, Chang and Ouyang, Yidong and Zeng, Wenjun and Qin, Tao},
  journal={arXiv preprint arXiv:2103.03097},
  year={2021}
}

@article{shen2021towards,
  title={Towards out-of-distribution generalization: A survey},
  author={Shen, Zheyan and Liu, Jiashuo and He, Yue and Zhang, Xingxuan and Xu, Renzhe and Yu, Han and Cui, Peng},
  journal={arXiv preprint arXiv:2108.13624},
  year={2021}
}

@article{zhou2021coop,
    title={Learning to Prompt for Vision-Language Models},
    author={Zhou, Kaiyang and Yang, Jingkang and Loy, Chen Change and Liu, Ziwei},
    journal={arXiv preprint arXiv:2109.01134},
    year={2021}
}

@inproceedings{sun2020test,
  title={Test-time training with self-supervision for generalization under distribution shifts},
  author={Sun, Yu and Wang, Xiaolong and Liu, Zhuang and Miller, John and Efros, Alexei and Hardt, Moritz},
  booktitle={ICML},
  year={2020}
}

@article{yao2022improving,
  title={Improving Out-of-Distribution Robustness via Selective Augmentation},
  author={Yao, Huaxiu and Wang, Yu and Li, Sai and Zhang, Linjun and Liang, Weixin and Zou, James and Finn, Chelsea},
  journal={arXiv preprint arXiv:2201.00299},
  year={2022}
}

@article{vedantam2021empirical,
  title={An empirical investigation of domain generalization with empirical risk minimizers},
  author={Vedantam, Ramakrishna and Lopez-Paz, David and Schwab, David J},
  journal={NeurIPS},
  year={2021}
}

@article{shi2021gradient,
  title={Gradient matching for domain generalization},
  author={Shi, Yuge and Seely, Jeffrey and Torr, Philip HS and Siddharth, N and Hannun, Awni and Usunier, Nicolas and Synnaeve, Gabriel},
  journal={arXiv preprint arXiv:2104.09937},
  year={2021}
}

@inproceedings{kundu2020universal,
  title={Universal source-free domain adaptation},
  author={Kundu, Jogendra Nath and Venkat, Naveen and Babu, R Venkatesh and others},
  booktitle={CVPR},
  year={2020}
}

@article{wang2020tent,
  title={Tent: Fully test-time adaptation by entropy minimization},
  author={Wang, Dequan and Shelhamer, Evan and Liu, Shaoteng and Olshausen, Bruno and Darrell, Trevor},
  journal={arXiv preprint arXiv:2006.10726},
  year={2020}
}

@article{iwasawa2021test,
  title={Test-time classifier adjustment module for model-agnostic domain generalization},
  author={Iwasawa, Yusuke and Matsuo, Yutaka},
  journal={NeurIPS},
  year={2021}
}

@article{zhang2021memo,
  title={MEMO: Test Time Robustness via Adaptation and Augmentation},
  author={Zhang, Marvin and Levine, Sergey and Finn, Chelsea},
  journal={arXiv preprint arXiv:2110.09506},
  year={2021}
}

@inproceedings{hendrycks2021many,
  title={The many faces of robustness: A critical analysis of out-of-distribution generalization},
  author={Hendrycks, Dan and Basart, Steven and Mu, Norman and Kadavath, Saurav and Wang, Frank and Dorundo, Evan and Desai, Rahul and Zhu, Tyler and Parajuli, Samyak and Guo, Mike and others},
  booktitle={ICCV},
  year={2021}
}

@inproceedings{hendrycks2021natural,
  title={Natural adversarial examples},
  author={Hendrycks, Dan and Zhao, Kevin and Basart, Steven and Steinhardt, Jacob and Song, Dawn},
  booktitle={CVPR},
  year={2021}
}

@inproceedings{mahajan2021domain,
  title={Domain generalization using causal matching},
  author={Mahajan, Divyat and Tople, Shruti and Sharma, Amit},
  booktitle={ICML},
  year={2021},
}

@article{laskin2020reinforcement,
  title={Reinforcement learning with augmented data},
  author={Laskin, Misha and Lee, Kimin and Stooke, Adam and Pinto, Lerrel and Abbeel, Pieter and Srinivas, Aravind},
  journal={NeurIPS},
  year={2020}
}

@inproceedings{cobbe2020leveraging,
  title={Leveraging procedural generation to benchmark reinforcement learning},
  author={Cobbe, Karl and Hesse, Chris and Hilton, Jacob and Schulman, John},
  booktitle={ICML},
  year={2020}
}

@article{kirk2021survey,
  title={A survey of generalisation in deep reinforcement learning},
  author={Kirk, Robert and Zhang, Amy and Grefenstette, Edward and Rockt{\"a}schel, Tim},
  journal={arXiv preprint arXiv:2111.09794},
  year={2021}
}

@article{wang2020meta,
  title={Meta-learning for domain generalization in semantic parsing},
  author={Wang, Bailin and Lapata, Mirella and Titov, Ivan},
  journal={arXiv preprint arXiv:2010.11988},
  year={2020}
}

@article{lee2019network,
  title={Network randomization: A simple technique for generalization in deep reinforcement learning},
  author={Lee, Kimin and Lee, Kibok and Shin, Jinwoo and Lee, Honglak},
  journal={arXiv preprint arXiv:1910.05396},
  year={2019}
}

@article{mazoure2021improving,
  title={Improving Zero-shot Generalization in Offline Reinforcement Learning using Generalized Similarity Functions},
  author={Mazoure, Bogdan and Kostrikov, Ilya and Nachum, Ofir and Tompson, Jonathan},
  journal={arXiv preprint arXiv:2111.14629},
  year={2021}
}

@inproceedings{hansen2021generalization,
  title={Generalization in reinforcement learning by soft data augmentation},
  author={Hansen, Nicklas and Wang, Xiaolong},
  booktitle={ICRA},
  year={2021}
}

@inproceedings{deitke2020robothor,
  title={Robothor: An open simulation-to-real embodied ai platform},
  author={Deitke, Matt and Han, Winson and Herrasti, Alvaro and Kembhavi, Aniruddha and Kolve, Eric and Mottaghi, Roozbeh and Salvador, Jordi and Schwenk, Dustin and VanderBilt, Eli and Wallingford, Matthew and others},
  booktitle={CVPR},
  year={2020}
}

@inproceedings{irvin2019chexpert,
  title={Chexpert: A large chest radiograph dataset with uncertainty labels and expert comparison},
  author={Irvin, Jeremy and Rajpurkar, Pranav and Ko, Michael and Yu, Yifan and Ciurea-Ilcus, Silviana and Chute, Chris and Marklund, Henrik and Haghgoo, Behzad and Ball, Robyn and Shpanskaya, Katie and others},
  booktitle={AAAI},
  year={2019}
}

@inproceedings{wang2017chestx,
  title={Chestx-ray8: Hospital-scale chest x-ray database and benchmarks on weakly-supervised classification and localization of common thorax diseases},
  author={Wang, Xiaosong and Peng, Yifan and Lu, Le and Lu, Zhiyong and Bagheri, Mohammadhadi and Summers, Ronald M},
  booktitle={CVPR},
  year={2017}
}

@inproceedings{krueger2021out,
  title={Out-of-distribution generalization via risk extrapolation (rex)},
  author={Krueger, David and Caballero, Ethan and Jacobsen, Joern-Henrik and Zhang, Amy and Binas, Jonathan and Zhang, Dinghuai and Le Priol, Remi and Courville, Aaron},
  booktitle={ICML},
  year={2021},
}

@article{glocker2019machine,
  title={Machine learning with multi-site imaging data: An empirical study on the impact of scanner effects},
  author={Glocker, Ben and Robinson, Robert and Castro, Daniel C and Dou, Qi and Konukoglu, Ender},
  journal={arXiv preprint arXiv:1910.04597},
  year={2019}
}

@article{robey2021model,
  title={Model-based domain generalization},
  author={Robey, Alexander and Pappas, George J and Hassani, Hamed},
  journal={NeurIPS},
  year={2021}
}

@article{li2022finding,
  title={Finding lost DG: Explaining domain generalization via model complexity},
  author={Li, Da and Gouk, Henry and Hospedales, Timothy},
  journal={arXiv preprint arXiv:2202.00563},
  year={2022}
}

@article{zhang2022nico++,
  title={NICO++: Towards Better Benchmarking for Domain Generalization},
  author={Zhang, Xingxuan and Zhou, Linjun and Xu, Renzhe and Cui, Peng and Shen, Zheyan and Liu, Haoxin},
  journal={arXiv preprint arXiv:2204.08040},
  year={2022}
}

@article{he2021towards,
  title={Towards non-iid image classification: A dataset and baselines},
  author={He, Yue and Shen, Zheyan and Cui, Peng},
  journal={PR},
  year={2021},
}

@article{beery2020iwildcam,
    title={The iWildCam 2020 Competition Dataset},
    author={Beery, Sara and Cole, Elijah and Gjoka, Arvi},
    journal={arXiv preprint arXiv:2004.10340},
    year={2020}
}

@inproceedings{christie2018functional,
  title={Functional Map of the World},
  author={Christie, Gordon and Fendley, Neil and Wilson, James and Mukherjee, Ryan},
  booktitle={CVPR},  
  year={2018}
}

@article{bandi2018detection,
  title={From detection of individual metastases to classification of lymph node status at the patient level: the CAMELYON17 challenge},
  author={Bandi, Peter and Geessink, Oscar and Manson, Quirine and Van Dijk, Marcory and Balkenhol, Maschenka and Hermsen, Meyke and Bejnordi, Babak Ehteshami and Lee, Byungjae and Paeng, Kyunghyun and Zhong, Aoxiao and others},
  journal={TMI},
  year={2018},
}

@article{chen2012marginalized,
  title={Marginalized denoising autoencoders for domain adaptation},
  author={Chen, Minmin and Xu, Zhixiang and Weinberger, Kilian and Sha, Fei},
  journal={arXiv preprint arXiv:1206.4683},
  year={2012}
}

@inproceedings{blitzer2006domain,
  title={Domain adaptation with structural correspondence learning},
  author={Blitzer, John and McDonald, Ryan and Pereira, Fernando},
  booktitle={EMNLP},
  year={2006}
}

@article{sainath2015convolutional,
  title={Convolutional neural networks for small-footprint keyword spotting},
  author={Sainath, Tara and Parada, Carolina},
  year={2015}
}

@inproceedings{grill2020bootstrap,
  title={Bootstrap your own latent: A new approach to self-supervised learning},
  author={Grill, Jean-Bastien and Strub, Florian and Altch{\'e}, Florent and Tallec, Corentin and Richemond, Pierre H and Buchatskaya, Elena and Doersch, Carl and Pires, Bernardo Avila and Guo, Zhaohan Daniel and Azar, Mohammad Gheshlaghi and others},
  booktitle={NeurIPS},
  year={2020}
}

@inproceedings{qiao2021uncertainty,
  title={Uncertainty-guided Model Generalization to Unseen Domains},
  author={Qiao, Fengchun and Peng, Xi},
  booktitle={CVPR},
  year={2021}
}

@article{srinivas2021bottleneck,
  title={Bottleneck Transformers for Visual Recognition},
  author={Srinivas, Aravind and Lin, Tsung-Yi and Parmar, Niki and Shlens, Jonathon and Abbeel, Pieter and Vaswani, Ashish},
  journal={arXiv preprint arXiv:2101.11605},
  year={2021}
}

@article{rahman2020correlation,
  title={Correlation-aware adversarial domain adaptation and generalization},
  author={Rahman, Mohammad Mahfujur and Fookes, Clinton and Baktashmotlagh, Mahsa and Sridharan, Sridha},
  journal={PR},
  year={2020},
}

@article{lecun2015deep,
  title={Deep learning},
  author={LeCun, Yann and Bengio, Yoshua and Hinton, Geoffrey},
  journal={Nature},
  year={2015},
}

@inproceedings{caron2018deep,
  title={Deep clustering for unsupervised learning of visual features},
  author={Caron, Mathilde and Bojanowski, Piotr and Joulin, Armand and Douze, Matthijs},
  booktitle={ECCV},
  year={2018}
}

@inproceedings{chao2016empirical,
  title={An empirical study and analysis of generalized zero-shot learning for object recognition in the wild},
  author={Chao, Wei-Lun and Changpinyo, Soravit and Gong, Boqing and Sha, Fei},
  booktitle={ECCV},
  year={2016},
}

@article{li2017learning,
  title={Learning without forgetting},
  author={Li, Zhizhong and Hoiem, Derek},
  journal={TPAMI},
  year={2017},
}

@InProceedings{song2019generalizable,
author = {Song, Jifei and Yang, Yongxin and Song, Yi-Zhe and Xiang, Tao and Hospedales, Timothy M.},
title = {Generalizable Person Re-Identification by Domain-Invariant Mapping Network},
booktitle = {CVPR},
year = {2019}
}

@inproceedings{zhou2020deep,
  title={Deep Domain-Adversarial Image Generation for Domain Generalisation.},
  author={Zhou, Kaiyang and Yang, Yongxin and Hospedales, Timothy M and Xiang, Tao},
  booktitle={AAAI},
  year={2020}
}

@inproceedings{akuzawa2019adversarial,
  title={Adversarial invariant feature learning with accuracy constraint for domain generalization},
  author={Akuzawa, Kei and Iwasawa, Yusuke and Matsuo, Yutaka},
  booktitle={ECMLPKDD},
  year={2019},
}

@article{mancini2018robust,
  title={Robust place categorization with deep domain generalization},
  author={Mancini, Massimiliano and Bulo, Samuel Rota and Caputo, Barbara and Ricci, Elisa},
  journal={RA-L},
  year={2018},
}

@inproceedings{you2019universal,
  title={Universal domain adaptation},
  author={You, Kaichao and Long, Mingsheng and Cao, Zhangjie and Wang, Jianmin and Jordan, Michael I},
  booktitle={CVPR},
  year={2019}
}

@inproceedings{peng2018zero,
  title={Zero-shot deep domain adaptation},
  author={Peng, Kuan-Chuan and Wu, Ziyan and Ernst, Jan},
  booktitle={ECCV},
  year={2018}
}

@inproceedings{cao2018partial,
  title={Partial adversarial domain adaptation},
  author={Cao, Zhangjie and Ma, Lijia and Long, Mingsheng and Wang, Jianmin},
  booktitle={ECCV},
  year={2018}
}

@inproceedings{maniyar2020zero,
  title={Zero Shot Domain Generalization},
  author={Maniyar, Udit and Deshmukh, Aniket Anand and Dogan, Urun and Balasubramanian, Vineeth N},
  booktitle={BMVC},
  year={2020}
}

@article{ye2021towards,
  title={Towards a theoretical framework of out-of-distribution generalization},
  author={Ye, Haotian and Xie, Chuanlong and Cai, Tianle and Li, Ruichen and Li, Zhenguo and Wang, Liwei},
  journal={NeurIPS},
  year={2021}
}

@inproceedings{mancini2020towards,
  title={Towards recognizing unseen categories in unseen domains},
  author={Mancini, Massimiliano and Akata, Zeynep and Ricci, Elisa and Caputo, Barbara},
  booktitle={ECCV},
  year={2020},
}

@inproceedings{zhou2020learning,
  title={Learning to Generate Novel Domains for Domain Generalization},
  author={Zhou, Kaiyang and Yang, Yongxin and Hospedales, Timothy and Xiang, Tao},
  booktitle={ECCV},
  year={2020}
}

@article{wang2020dofe,
  title={DoFE: Domain-oriented Feature Embedding for Generalizable Fundus Image Segmentation on Unseen Datasets},
  author={Wang, Shujun and Yu, Lequan and Li, Kang and Yang, Xin and Fu, Chi-Wing and Heng, Pheng-Ann},
  journal={TMI},
  year={2020},
}

@article{zhou2020domain,
  title={Domain Adaptive Ensemble Learning},
  author={Zhou, Kaiyang and Yang, Yongxin and Qiao, Yu and Xiang, Tao},
  journal={arXiv preprint arXiv:2003.07325},
  year={2020}
}

@inproceedings{pan2018two,
  title={Two at once: Enhancing learning and generalization capacities via ibn-net},
  author={Pan, Xingang and Luo, Ping and Shi, Jianping and Tang, Xiaoou},
  booktitle={ECCV},
  year={2018}
}

@article{torchreid,
  title={Torchreid: A Library for Deep Learning Person Re-Identification in Pytorch},
  author={Zhou, Kaiyang and Xiang, Tao},
  journal={arXiv preprint arXiv:1910.10093},
  year={2019}
}

@inproceedings{lee2013pseudo,
  title={Pseudo-label: The simple and efficient semi-supervised learning method for deep neural networks},
  author={Lee, Dong-Hyun},
  booktitle={ICML-W},
  year={2013}
}

@inproceedings{grandvalet2005semi,
  title={Semi-supervised learning by entropy minimization},
  author={Grandvalet, Yves and Bengio, Yoshua},
  booktitle={NeurIPS},
  year={2005}
}

@article{hospedales2020meta,
  title={Meta-learning in neural networks: A survey},
  author={Hospedales, Timothy and Antoniou, Antreas and Micaelli, Paul and Storkey, Amos},
  journal={arXiv preprint arXiv:2004.05439},
  year={2020}
}

@inproceedings{zhao2020domain,
  title={Domain Generalization via Entropy Regularization},
  author={Zhao, Shanshan and Gong, Mingming and Liu, Tongliang and Fu, Huan and Tao, Dacheng},
  booktitle={NeurIPS},
  year={2020}
}

@inproceedings{li2020domain,
  title={Domain Generalization for Medical Imaging Classification with Linear-Dependency Regularization},
  author={Li, Haoliang and Wang, YuFei and Wan, Renjie and Wang, Shiqi and Li, Tie-Qiang and Kot, Alex C},
  booktitle={NeurIPS},
  year={2020}
}

@inproceedings{tobin2017domain,
  title={Domain randomization for transferring deep neural networks from simulation to the real world},
  author={Tobin, Josh and Fong, Rachel and Ray, Alex and Schneider, Jonas and Zaremba, Wojciech and Abbeel, Pieter},
  booktitle={IROS},
  year={2017},
}

@article{justesen2018illuminating,
  title={Illuminating generalization in deep reinforcement learning through procedural level generation},
  author={Justesen, Niels and Torrado, Ruben Rodriguez and Bontrager, Philip and Khalifa, Ahmed and Togelius, Julian and Risi, Sebastian},
  journal={arXiv preprint arXiv:1806.10729},
  year={2018}
}

@article{schulman2017proximal,
  title={Proximal policy optimization algorithms},
  author={Schulman, John and Wolski, Filip and Dhariwal, Prafulla and Radford, Alec and Klimov, Oleg},
  journal={arXiv preprint arXiv:1707.06347},
  year={2017}
}

@inproceedings{laskin2020curl,
  title={Curl: Contrastive unsupervised representations for reinforcement learning},
  author={Laskin, Michael and Srinivas, Aravind and Abbeel, Pieter},
  booktitle={ICML},
  year={2020},
}

@inproceedings{impala2018,
  title={IMPALA: Scalable Distributed Deep-RL with Importance Weighted Actor-Learner Architectures},
  author={Espeholt, Lasse and Soyer, Hubert and Munos, Remi and Simonyan, Karen and Mnih, Volodymir and Ward, Tom and Doron, Yotam and Firoiu, Vlad and Harley, Tim and Dunning, Iain and others},
  booktitle={ICML},
  year={2018}
}

@inproceedings{cobbe2019quantifying,
  title={Quantifying generalization in reinforcement learning},
  author={Cobbe, Karl and Klimov, Oleg and Hesse, Chris and Kim, Taehoon and Schulman, John},
  booktitle={ICML},
  year={2019}
}

@inproceedings{yarats2021improving,
  title={Improving sample efficiency in model-free reinforcement learning from images},
  author={Yarats, Denis and Zhang, Amy and Kostrikov, Ilya and Amos, Brandon and Pineau, Joelle and Fergus, Rob},
  booktitle={AAAI},
  year={2021}
}

@inproceedings{igl2019generalization,
  title={Generalization in reinforcement learning with selective noise injection and information bottleneck},
  author={Igl, Maximilian and Ciosek, Kamil and Li, Yingzhen and Tschiatschek, Sebastian and Zhang, Cheng and Devlin, Sam and Hofmann, Katja},
  booktitle={NeurIPS},
  year={2019}
}

@inproceedings{xu2021robust,
title={Robust and Generalizable Visual Representation Learning via Random Convolutions},
author={Zhenlin Xu and Deyi Liu and Junlin Yang and Colin Raffel and Marc Niethammer},
booktitle={ICLR},
year={2021},
}

@inproceedings{hendrycks2020augmix,
  title={Augmix: A simple data processing method to improve robustness and uncertainty},
  author={Hendrycks, Dan and Mu, Norman and Cubuk, Ekin D and Zoph, Barret and Gilmer, Justin and Lakshminarayanan, Balaji},
  booktitle={ICLR},
  year={2020}
}

@inproceedings{zhong2020random,
  title={Random Erasing Data Augmentation.},
  author={Zhong, Zhun and Zheng, Liang and Kang, Guoliang and Li, Shaozi and Yang, Yi},
  booktitle={AAAI},
  year={2020}
}

@article{cubuk2019randaugment,
  title={RandAugment: Practical data augmentation with no separate search},
  author={Cubuk, Ekin D and Zoph, Barret and Shlens, Jonathon and Le, Quoc V},
  journal={arXiv preprint arXiv:1909.13719},
  year={2019}
}

@inproceedings{liu2017unsupervised,
  title={Unsupervised image-to-image translation networks},
  author={Liu, Ming-Yu and Breuel, Thomas and Kautz, Jan},
  booktitle={NeurIPS},
  year={2017}
}

@inproceedings{liu2016coupled,
  title={Coupled generative adversarial networks},
  author={Liu, Ming-Yu and Tuzel, Oncel},
  booktitle={NeurIPS},
  year={2016}
}

@inproceedings{yue2019domain,
  title={Domain Randomization and Pyramid Consistency: Simulation-to-Real Generalization without Accessing Target Domain Data},
  author={Yue, Xiangyu and Zhang, Yang and Zhao, Sicheng and Sangiovanni-Vincentelli, Alberto and Keutzer, Kurt and Gong, Boqing},
  booktitle={ICCV},
  year={2019}
}

@InProceedings{zakharov2019deceptionnet,
author = {Zakharov, Sergey and Kehl, Wadim and Ilic, Slobodan},
title = {DeceptionNet: Network-Driven Domain Randomization},
booktitle = {ICCV},
year = {2019}
}

@inproceedings{tzeng2015simultaneous,
  title={Simultaneous deep transfer across domains and tasks},
  author={Tzeng, Eric and Hoffman, Judy and Darrell, Trevor and Saenko, Kate},
  booktitle={ICCV},
  year={2015}
}

@inproceedings{lee2020network,
  title={Network randomization: A simple technique for generalization in deep reinforcement learning},
  author={Lee, Kimin and Lee, Kibok and Shin, Jinwoo and Lee, Honglak},
  booktitle={ICLR},
  year={2020}
}

@article{kostrikov2020image,
  title={Image augmentation is all you need: Regularizing deep reinforcement learning from pixels},
  author={Kostrikov, Ilya and Yarats, Denis and Fergus, Rob},
  journal={arXiv preprint arXiv:2004.13649},
  year={2020}
}

@InProceedings{office_home,
author = {Venkateswara, Hemanth and Eusebio, Jose and Chakraborty, Shayok and Panchanathan, Sethuraman},
title = {Deep Hashing Network for Unsupervised Domain Adaptation},
booktitle = {CVPR},
year = {2017}
}

@article{yang2014learn,
  title={Learn convolutional neural network for face anti-spoofing},
  author={Yang, Jianwei and Lei, Zhen and Li, Stan Z},
  journal={arXiv preprint arXiv:1408.5601},
  year={2014}
}

@inproceedings{wen2016discriminative,
  title={A discriminative feature learning approach for deep face recognition},
  author={Wen, Yandong and Zhang, Kaipeng and Li, Zhifeng and Qiao, Yu},
  booktitle={ECCV},
  year={2016},
}

@inproceedings{gidaris2018unsupervised,
  title={Unsupervised representation learning by predicting image rotations},
  author={Gidaris, Spyros and Singh, Praveer and Komodakis, Nikos},
  booktitle={ICLR},
  year={2018}
}

@inproceedings{sun2014deep,
  title={Deep learning face representation from predicting 10,000 classes},
  author={Sun, Yi and Wang, Xiaogang and Tang, Xiaoou},
  booktitle={CVPR},
  year={2014}
}

@inproceedings{girshick2015fast,
  title={Fast r-cnn},
  author={Girshick, Ross},
  booktitle={ICCV},
  year={2015}
}

@InProceedings{cvpr19jigen,
author = {Carlucci, Fabio M. and D'Innocente, Antonio and Bucci, Silvia and Caputo, Barbara and Tommasi, Tatiana},
title = {Domain Generalization by Solving Jigsaw Puzzles},
booktitle = {CVPR},
year = {2019}
}

@book{villani2008optimal,
  title={Optimal transport: old and new},
  author={Villani, C{\'e}dric},
  year={2008},
  publisher={Springer Science \& Business Media}
}

@InProceedings{feature_critic,
  title = 	 {Feature-Critic Networks for Heterogeneous Domain Generalization},
  author = 	 {Li, Yiying and Yang, Yongxin and Zhou, Wei and Hospedales, Timothy},
  booktitle = 	 {ICML},
  year = 	 {2019}
}

@inproceedings{jigsaw_puzzles,
  title={Unsupervised learning of visual representations by solving jigsaw puzzles},
  author={Noroozi, Mehdi and Favaro, Paolo},
  booktitle={ECCV},
  year={2016},
}

@inproceedings{zhou2019osnet,
  title={Omni-Scale Feature Learning for Person Re-Identification},
  author={Zhou, Kaiyang and Yang, Yongxin and Cavallaro, Andrea and Xiang, Tao},
  booktitle={ICCV},
  year={2019}
}

@article{zhou2021osnet,
  title={Learning Generalisable Omni-Scale Representations for Person Re-Identification},
  author={Zhou, Kaiyang and Yang, Yongxin and Cavallaro, Andrea and Xiang, Tao},
  journal={TPAMI},
  year={2021}
}

@inproceedings{malisiewicz2011ensemble,
  title={Ensemble of exemplar-svms for object detection and beyond},
  author={Malisiewicz, Tomasz and Gupta, Abhinav and Efros, Alexei A},
  booktitle={ICCV},
  year={2011},
}

@inproceedings{d2018domain,
  title={Domain generalization with domain-specific aggregation modules},
  author={D'Innocente, Antonio and Caputo, Barbara},
  booktitle={GCPR},
  year={2018},
}

@InProceedings{li2019episodic,
  title={Episodic training for domain generalization},
  author={Li, Da and Zhang, Jianshu and Yang, Yongxin and Liu, Cong and Song, Yi-Zhe and Hospedales, Timothy M},
  booktitle={ICCV},
  year={2019}
}

@inproceedings{rosenfeld2022online,
  title={An online learning approach to interpolation and extrapolation in domain generalization},
  author={Rosenfeld, Elan and Ravikumar, Pradeep and Risteski, Andrej},
  booktitle={AISTATS},
  year={2022}
}

@inproceedings{hu2020domain,
  title={Domain generalization via multidomain discriminant analysis},
  author={Hu, Shoubo and Zhang, Kun and Chen, Zhitang and Chan, Laiwan},
  booktitle={UAI},
  year={2020},
}

@inproceedings{zhao2018adversarial,
  title={Adversarial multiple source domain adaptation},
  author={Zhao, Han and Zhang, Shanghang and Wu, Guanhang and Moura, Jos{\'e} MF and Costeira, Joao P and Gordon, Geoffrey J},
  booktitle={NeurIPS},
  year={2018}
}

@inproceedings{balaji2019normalized,
  title={Normalized wasserstein for mixture distributions with applications in adversarial learning and domain adaptation},
  author={Balaji, Yogesh and Chellappa, Rama and Feizi, Soheil},
  booktitle={ICCV},
  year={2019}
}

@inproceedings{long2016unsupervised,
  title={Unsupervised domain adaptation with residual transfer networks},
  author={Long, Mingsheng and Zhu, Han and Wang, Jianmin and Jordan, Michael I},
  booktitle={NeurIPS},
  year={2016}
}

@inproceedings{wang2020heterogeneous,
  title={Heterogeneous domain generalization via domain mixup},
  author={Wang, Yufei and Li, Haoliang and Kot, Alex C},
  booktitle={ICASSP},
  year={2020},
}

@inproceedings{mancini2018best,
  title={Best sources forward: domain generalization through source-specific nets},
  author={Mancini, Massimiliano and Bul{\`o}, Samuel Rota and Caputo, Barbara and Ricci, Elisa},
  booktitle={ICIP},
  year={2018},
}

@article{zhang2020generalizing,
  title={Generalizing deep learning for medical image segmentation to unseen domains via deep stacked transformation},
  author={Zhang, Ling and Wang, Xiaosong and Yang, Dong and Sanford, Thomas and Harmon, Stephanie and Turkbey, Baris and Wood, Bradford J and Roth, Holger and Myronenko, Andriy and Xu, Daguang and others},
  journal={TMI},
  year={2020},
}

@inproceedings{wang2020learning,
  title={Learning from Extrinsic and Intrinsic Supervisions for Domain Generalization},
  author={Wang, Shujun and Yu, Lequan and Li, Caizi and Fu, Chi-Wing and Heng, Pheng-Ann},
  booktitle={ECCV},
  year={2020},
}

@inproceedings{li2018extracting,
  title={Extracting relationships by multi-domain matching},
  author={Li, Yitong and Carlson, David E and others},
  booktitle={NeurIPS},
  year={2018}
}

@inproceedings{xu2018deep,
  title={Deep cocktail network: Multi-source unsupervised domain adaptation with category shift},
  author={Xu, Ruijia and Chen, Ziliang and Zuo, Wangmeng and Yan, Junjie and Lin, Liang},
  booktitle={CVPR},
  year={2018}
}

@inproceedings{krizhevsky2012imagenet,
	Author = {Krizhevsky, Alex and Sutskever, Ilya and Hinton, Geoffrey E},
	Booktitle = {NeurIPS},
	Title = {Imagenet classification with deep convolutional neural networks},
	Year = {2012}}

@inproceedings{yang2020curriculum,
  title={Curriculum Manager for Source Selection in Multi-Source Domain Adaptation},
  author={Yang, Luyu and Balaji, Yogesh and Lim, Ser-Nam and Shrivastava, Abhinav},
  booktitle={ECCV},
  year={2020}
}

@inproceedings{seo2020learning,
  title={Learning to optimize domain specific normalization for domain generalization},
  author={Seo, Seonguk and Suh, Yumin and Kim, Dongwan and Han, Jongwoo and Han, Bohyung},
  booktitle={ECCV},
  year={2020}
}

@article{cha2021swad,
  title={Swad: Domain generalization by seeking flat minima},
  author={Cha, Junbum and Chun, Sanghyuk and Lee, Kyungjae and Cho, Han-Cheol and Park, Seunghyun and Lee, Yunsung and Park, Sungrae},
  journal={NeurIPS},
  year={2021}
}

@inproceedings{volpi2021continual,
  title={Continual adaptation of visual representations via domain randomization and meta-learning},
  author={Volpi, Riccardo and Larlus, Diane and Rogez, Gr{\'e}gory},
  booktitle={CVPR},
  year={2021}
}

@article{liu2020ms,
  title={MS-net: Multi-site network for improving prostate segmentation with heterogeneous MRI data},
  author={Liu, Quande and Dou, Qi and Yu, Lequan and Heng, Pheng Ann},
  journal={TMI},
  year={2020},
}

@inproceedings{gong2018dlow,
  title={DLOW: Domain Flow for Adaptation and Generalization},
  author={Gong, Rui and Li, Wen and Chen, Yuhua and Van Gool, Luc},
  booktitle={CVPR},
  year={2019}
}

@inproceedings{gong2012geodesic,
  title={Geodesic flow kernel for unsupervised domain adaptation},
  author={Gong, Boqing and Shi, Yuan and Sha, Fei and Grauman, Kristen},
  booktitle={CVPR},
  year={2012},
}



@inproceedings{wang2018fast,
  title={Fast Online Object Tracking and Segmentation: A Unifying Approach},
  author={Wang, Qiang and Zhang, Li and Bertinetto, Luca and Hu, Weiming and Torr, Philip HS},
  booktitle={CVPR},
  year={2019}
}

@inproceedings{bertinetto2016fully,
  title={Fully-convolutional siamese networks for object tracking},
  author={Bertinetto, Luca and Valmadre, Jack and Henriques, Joao F and Vedaldi, Andrea and Torr, Philip HS},
  booktitle={ECCVW},
  year={2016},
}


@inproceedings{ren2015faster,
  title={Faster r-cnn: Towards real-time object detection with region proposal networks},
  author={Ren, Shaoqing and He, Kaiming and Girshick, Ross and Sun, Jian},
  booktitle={NeurIPS},
  year={2015}
}

@article{ding2017deep,
  title={Deep domain generalization with structured low-rank constraint},
  author={Ding, Zhengming and Fu, Yun},
  journal={TIP},
  year={2017},
}

@article{jing2020self,
  title={Self-supervised visual feature learning with deep neural networks: A survey},
  author={Jing, Longlong and Tian, Yingli},
  journal={TPAMI},
  year={2020},
}

@inproceedings{prakash2019structured,
  title={Structured domain randomization: Bridging the reality gap by context-aware synthetic data},
  author={Prakash, Aayush and Boochoon, Shaad and Brophy, Mark and Acuna, David and Cameracci, Eric and State, Gavriel and Shapira, Omer and Birchfield, Stan},
  booktitle={ICRA},
  year={2019},
}
	
@inproceedings{Facenet,
  author    = {Florian Schroff and
               Dmitry Kalenichenko and
               James Philbin},
  title     = {FaceNet: {A} unified embedding for face recognition and clustering},
  booktitle = {CVPR},
  year      = {2015}
}

@article{xie2019unsupervised,
  title={Unsupervised Data Augmentation for Consistency Training},
  author={Xie, Qizhe and Dai, Zihang and Hovy, Eduard and Luong, Minh-Thang and Le, Quoc V},
  journal={arXiv preprint arXiv:1904.12848},
  year={2019}
}

@inproceedings{zhang2018collaborative,
  title={Collaborative and adversarial network for unsupervised domain adaptation},
  author={Zhang, Weichen and Ouyang, Wanli and Li, Wen and Xu, Dong},
  booktitle={CVPR},
  year={2018}
}

@inproceedings{zhang2018mixup,
title={mixup: Beyond Empirical Risk Minimization},
author={Hongyi Zhang and Moustapha Cisse and Yann N. Dauphin and David Lopez-Paz},
booktitle={ICLR},
year={2018},
}

@inproceedings{berthelot2019mixmatch,
  title={Mixmatch: A holistic approach to semi-supervised learning},
  author={Berthelot, David and Carlini, Nicholas and Goodfellow, Ian and Papernot, Nicolas and Oliver, Avital and Raffel, Colin A},
  booktitle={NeurIPS},
  year={2019}
}

@inproceedings{saito2017asymmetric,
  title={Asymmetric tri-training for unsupervised domain adaptation},
  author={Saito, Kuniaki and Ushiku, Yoshitaka and Harada, Tatsuya},
  booktitle={ICML},
  year={2017},
}

@inproceedings{berthelot2020remixmatch,
title={ReMixMatch: Semi-Supervised Learning with Distribution Matching and Augmentation Anchoring},
author={David Berthelot and Nicholas Carlini and Ekin D. Cubuk and Alex Kurakin and Kihyuk Sohn and Han Zhang and Colin Raffel},
booktitle={ICLR},
year={2020}
}

@article{albuquerque2019generalizing,
  title={Generalizing to unseen domains via distribution matching},
  author={Albuquerque, Isabela and Monteiro, Jo{\~a}o and Darvishi, Mohammad and Falk, Tiago H and Mitliagkas, Ioannis},
  journal={arXiv preprint arXiv:1911.00804},
  year={2019}
}

@inproceedings{sohn2020fixmatch,
  title={FixMatch: Simplifying Semi-Supervised Learning with Consistency and Confidence},
  author={Sohn, Kihyuk and Berthelot, David and Li, Chun-Liang and Zhang, Zizhao and Carlini, Nicholas and Cubuk, Ekin D and Kurakin, Alex and Zhang, Han and Raffel, Colin},
  booktitle={NeurIPS},
  year={2020}
}

@InProceedings{volpi2019addressing,
author = {Volpi, Riccardo and Murino, Vittorio},
title = {Addressing Model Vulnerability to Distributional Shifts Over Image Transformation Sets},
booktitle = {ICCV},
year = {2019}
}

@inproceedings{ryu2019generalized,
  title={Generalized convolutional forest networks for domain generalization and visual recognition},
  author={Ryu, Jongbin and Kwon, Gitaek and Yang, Ming-Hsuan and Lim, Jongwoo},
  booktitle={ICLR},
  year={2019}
}

@article{cha2021domain,
  title={Domain Generalization Needs Stochastic Weight Averaging for Robustness on Domain Shifts},
  author={Cha, Junbum and Cho, Hancheol and Lee, Kyungjae and Park, Seunghyun and Lee, Yunsung and Park, Sungrae},
  journal={arXiv preprint arXiv:2102.08604},
  year={2021}
}

@inproceedings{kim2019learning,
  title={Learning not to learn: Training deep neural networks with biased data},
  author={Kim, Byungju and Kim, Hyunwoo and Kim, Kyungsu and Kim, Sungjin and Kim, Junmo},
  booktitle={CVPR},
  year={2019}
}

@article{geirhos2020shortcut,
  title={Shortcut learning in deep neural networks},
  author={Geirhos, Robert and Jacobsen, J{\"o}rn-Henrik and Michaelis, Claudio and Zemel, Richard and Brendel, Wieland and Bethge, Matthias and Wichmann, Felix A},
  journal={Nature Machine Intelligence},
  year={2020},
}

@article{blanchard2021domain,
  title={Domain generalization by marginal transfer learning},
  author={Blanchard, Gilles and Deshmukh, Aniket Anand and Dogan, Urun and Lee, Gyemin and Scott, Clayton},
  journal={JMLR},
  year={2021}
}

@inproceedings{chen2016infogan,
  title={Infogan: Interpretable representation learning by information maximizing generative adversarial nets},
  author={Chen, Xi and Duan, Yan and Houthooft, Rein and Schulman, John and Sutskever, Ilya and Abbeel, Pieter},
  booktitle={NeurIPS},
  year={2016}
}

@inproceedings{blanchard2011generalizing,
  title={Generalizing from several related classification tasks to a new unlabeled sample},
  author={Blanchard, Gilles and Lee, Gyemin and Scott, Clayton},
  booktitle={NeurIPS},
  year={2011}
}

@inproceedings{shankar2018generalizing,
  title={Generalizing Across Domains via Cross-Gradient Training},
  author={Shiv Shankar and Vihari Piratla and Soumen Chakrabarti and Siddhartha Chaudhuri and Preethi Jyothi and Sunita Sarawagi},
  booktitle={ICLR},
  year={2018},
}

@inproceedings{balaji2018metareg,
  title={MetaReg: Towards Domain Generalization using Meta-Regularization},
  author={Yogesh Balaji and Swami Sankaranarayanan and Rama Chellappa},
  booktitle={NeurIPS},
  year={2018},
}

@inproceedings{chen2020simple,
  title={A simple framework for contrastive learning of visual representations},
  author={Chen, Ting and Kornblith, Simon and Norouzi, Mohammad and Hinton, Geoffrey},
  booktitle={ICML},
  year={2020},
}

@inproceedings{he2020momentum,
  title={Momentum contrast for unsupervised visual representation learning},
  author={He, Kaiming and Fan, Haoqi and Wu, Yuxin and Xie, Saining and Girshick, Ross},
  booktitle={CVPR},
  year={2020}
}

@inproceedings{li2018learning,
  title={Learning to Generalize: Meta-Learning for Domain Generalization},
  author={Da Li and Yongxin Yang and Yi-Zhe Song and Timothy M. Hospedales},
  booktitle={AAAI},
  year={2018},
}

@inproceedings{song2018collaborative,
  title={Collaborative learning for deep neural networks},
  author={Song, Guocong and Chai, Wei},
  booktitle={NeurIPS},
  year={2018}
}

@inproceedings{romero2015fitnets,
  title={Fitnets: Hints for thin deep nets},
  author={Romero, Adriana and Ballas, Nicolas and Kahou, Samira Ebrahimi and Chassang, Antoine and Gatta, Carlo and Bengio, Yoshua},
  booktitle={ICLR},
  year={2015}
}

@inproceedings{zagoruyko2017paying,
  title={Paying more attention to attention: Improving the performance of convolutional neural networks via attention transfer},
  author={Zagoruyko, Sergey and Komodakis, Nikos},
  booktitle={ICLR},
  year={2017}
}

@inproceedings{cordts2016cityscapes,
  title={The cityscapes dataset for semantic urban scene understanding},
  author={Cordts, Marius and Omran, Mohamed and Ramos, Sebastian and Rehfeld, Timo and Enzweiler, Markus and Benenson, Rodrigo and Franke, Uwe and Roth, Stefan and Schiele, Bernt},
  booktitle={CVPR},
  year={2016}
}

@article{sagawa2019distributionally,
  title={Distributionally robust neural networks for group shifts: On the importance of regularization for worst-case generalization},
  author={Sagawa, Shiori and Koh, Pang Wei and Hashimoto, Tatsunori B and Liang, Percy},
  journal={arXiv preprint arXiv:1911.08731},
  year={2019}
}

@inproceedings{richter2016playing,
  title={Playing for data: Ground truth from computer games},
  author={Richter, Stephan R and Vineet, Vibhav and Roth, Stefan and Koltun, Vladlen},
  booktitle={ECCV},
  year={2016},
}

@inproceedings{qiao2020learning,
  title={Learning to learn single domain generalization},
  author={Qiao, Fengchun and Zhao, Long and Peng, Xi},
  booktitle={CVPR},
  year={2020}
}

@inproceedings{du2020learning,
  title={Learning to learn with variational information bottleneck for domain generalization},
  author={Du, Yingjun and Xu, Jun and Xiong, Huan and Qiu, Qiang and Zhen, Xiantong and Snoek, Cees GM and Shao, Ling},
  booktitle={ECCV},
  year={2020},
}

@inproceedings{ahn2019variational,
  title={Variational information distillation for knowledge transfer},
  author={Ahn, Sungsoo and Hu, Shell Xu and Damianou, Andreas and Lawrence, Neil D and Dai, Zhenwen},
  booktitle={CVPR},
  year={2019}
}

@article{tian2019contrastive,
  title={Contrastive representation distillation},
  author={Tian, Yonglong and Krishnan, Dilip and Isola, Phillip},
  journal={arXiv preprint arXiv:1910.10699},
  year={2019}
}

@inproceedings{li2020sequential,
  title={Sequential Learning for Domain Generalization},
  author={Li, Da and Yang, Yongxin and Song, Yi-Zhe and Hospedales, Timothy},
  booktitle={ECCV-W},
  year={2020},
}

@inproceedings{finn2017model,
  title={Model-Agnostic Meta-Learning for Fast Adaptation of Deep Networks},
  author={Chelsea Finn and Pieter Abbeel and Sergey Levine },
  booktitle={ICML},
  year={2017},
}

@inproceedings{zhao2019multi,
  title={Multi-source domain adaptation for semantic segmentation},
  author={Zhao, Sicheng and Li, Bo and Yue, Xiangyu and Gu, Yang and Xu, Pengfei and Hu, Runbo and Chai, Hua and Keutzer, Kurt},
  booktitle={NeurIPS},
  year={2019}
}

@inproceedings{rahman2019multi,
  title={Multi-component image translation for deep domain generalization},
  author={Rahman, Mohammad Mahfujur and Fookes, Clinton and Baktashmotlagh, Mahsa and Sridharan, Sridha},
  booktitle={WACV},
  year={2019},
}

@inproceedings{szegedy2015going,
  title={Going deeper with convolutions},
  author={Szegedy, Christian and Liu, Wei and Jia, Yangqing and Sermanet, Pierre and Reed, Scott and Anguelov, Dragomir and Erhan, Dumitru and Vanhoucke, Vincent and Rabinovich, Andrew},
  booktitle={CVPR},
  year={2015}
}

@inproceedings{izmailov2018averaging,
  title={Averaging weights leads to wider optima and better generalization},
  author={Izmailov, Pavel and Podoprikhin, Dmitrii and Garipov, Timur and Vetrov, Dmitry and Wilson, Andrew Gordon},
  booktitle={UAI},
  year={2018}
}

@article{somavarapu2020frustratingly,
  title={Frustratingly Simple Domain Generalization via Image Stylization},
  author={Somavarapu, Nathan and Ma, Chih-Yao and Kira, Zsolt},
  journal={arXiv preprint arXiv:2006.11207},
  year={2020}
}

@book{zhou2012ensemble,
  title={Ensemble methods: foundations and algorithms},
  author={Zhou, Zhi-Hua},
  year={2012},
  publisher={Chapman and Hall/CRC}
}

@inproceedings{carlucci2019hallucinating,
  title={Hallucinating Agnostic Images to Generalize Across Domains.},
  author={Carlucci, Fabio Maria and Russo, Paolo and Tommasi, Tatiana and Caputo, Barbara},
  booktitle={ICCV-W},
  year={2019}
}

@inproceedings{anil2018large,
title={Large scale distributed neural network training through online distillation},
author={Rohan Anil and Gabriel Pereyra and Alexandre Passos and Robert Ormandi and George E. Dahl and Geoffrey E. Hinton},
booktitle={ICLR},
year={2018}
}

@article{hinton2015distilling,
  title={Distilling the knowledge in a neural network},
  author={Hinton, Geoffrey and Vinyals, Oriol and Dean, Jeff},
  journal={arXiv preprint arXiv:1503.02531},
  year={2015}
}

@inproceedings{zhao2020multi,
  title={Multi-source Distilling Domain Adaptation},
  author={Zhao, Sicheng and Wang, Guangzhi and Zhang, Shanghang and Gu, Yang and Li, Yaxian and Song, Zhichao and Xu, Pengfei and Hu, Runbo and Chai, Hua and Keutzer, Kurt},
  booktitle={AAAI},
  year={2020}
}

@inproceedings{zhang2021deep,
  title={Deep Stable Learning for Out-Of-Distribution Generalization},
  author={Zhang, Xingxuan and Cui, Peng and Xu, Renzhe and Zhou, Linjun and He, Yue and Shen, Zheyan},
  booktitle={CVPR},
  year={2021}
}

@article{geng2020recent,
  title={Recent advances in open set recognition: A survey},
  author={Geng, Chuanxing and Huang, Sheng-jun and Chen, Songcan},
  journal={TPAMI},
  year={2020},
}

@inproceedings{shu2021open,
  title={Open Domain Generalization with Domain-Augmented Meta-Learning},
  author={Shu, Yang and Cao, Zhangjie and Wang, Chenyu and Wang, Jianmin and Long, Mingsheng},
  booktitle={CVPR},
  year={2021}
}

@inproceedings{pandey2021domain,
  title={Domain Generalization via Inference-time Label-Preserving Target Projections},
  author={Pandey, Prashant and Raman, Mrigank and Varambally, Sumanth and AP, Prathosh},
  booktitle={CVPR},
  year={2021}
}

@inproceedings{huang2021fsdr,
  title={Fsdr: Frequency space domain randomization for domain generalization},
  author={Huang, Jiaxing and Guan, Dayan and Xiao, Aoran and Lu, Shijian},
  booktitle={CVPR},
  year={2021}
}

@InProceedings{iccv19domainnet,
author = {Peng, Xingchao and Bai, Qinxun and Xia, Xide and Huang, Zijun and Saenko, Kate and Wang, Bo},
title = {Moment Matching for Multi-Source Domain Adaptation},
booktitle = {ICCV},
year = {2019}
}

@InProceedings{Cicek_2019_ICCV,
author = {Cicek, Safa and Soatto, Stefano},
title = {Unsupervised Domain Adaptation via Regularized Conditional Alignment},
booktitle = {ICCV},
year = {2019}
}

@InProceedings{iccv19MME,
author = {Saito, Kuniaki and Kim, Donghyun and Sclaroff, Stan and Darrell, Trevor and Saenko, Kate},
title = {Semi-Supervised Domain Adaptation via Minimax Entropy},
booktitle = {ICCV},
year = {2019}
}


@inproceedings{huang2017arbitrary,
  title={Arbitrary style transfer in real-time with adaptive instance normalization},
  author={Huang, Xun and Belongie, Serge},
  booktitle={ICCV},
  year={2017}
}

@article{deng2020representation,
  title={Representation via representations: Domain generalization via adversarially learned invariant representations},
  author={Deng, Zhun and Ding, Frances and Dwork, Cynthia and Hong, Rachel and Parmigiani, Giovanni and Patil, Prasad and Sur, Pragya},
  journal={arXiv preprint arXiv:2006.11478},
  year={2020}
}

@article{jin2020feature,
  title={Feature Alignment and Restoration for Domain Generalization and Adaptation},
  author={Jin, Xin and Lan, Cuiling and Zeng, Wenjun and Chen, Zhibo},
  journal={arXiv preprint arXiv:2006.12009},
  year={2020}
}

@inproceedings{mansour2009domain,
  title={Domain adaptation with multiple sources},
  author={Mansour, Yishay and Mohri, Mehryar and Rostamizadeh, Afshin},
  booktitle={NeurIPS},
  year={2009}
}

@inproceedings{long2018conditional,
  title={Conditional adversarial domain adaptation},
  author={Long, Mingsheng and Cao, Zhangjie and Wang, Jianmin and Jordan, Michael I},
  booktitle={NeurIPS},
  year={2018}
}

@article{jacobs1991adaptive,
  title={Adaptive mixtures of local experts},
  author={Jacobs, Robert A and Jordan, Michael I and Nowlan, Steven J and Hinton, Geoffrey E},
  journal={Neural computation},
  year={1991}
}

@inproceedings{ahmed2016network,
  title={Network of experts for large-scale image categorization},
  author={Ahmed, Karim and Baig, Mohammad Haris and Torresani, Lorenzo},
  booktitle={ECCV},
  year={2016},
}

@inproceedings{zhang2018deep,
  title={Deep mutual learning},
  author={Zhang, Ying and Xiang, Tao and Hospedales, Timothy M and Lu, Huchuan},
  booktitle={CVPR},
  year={2018}
}

@inproceedings{shazeer2017moe,
title	= {Outrageously Large Neural Networks: The Sparsely-Gated Mixture-of-Experts Layer},
author	= {Noam Shazeer and Azalia Mirhoseini and Krzysztof Maziarz and Andy Davis and Quoc Le and Geoffrey Hinton and Jeff Dean},
booktitle={ICLR},
year	= {2017},
}


@article{gulrajani2020search,
  title={In search of lost domain generalization},
  author={Gulrajani, Ishaan and Lopez-Paz, David},
  journal={arXiv preprint arXiv:2007.01434},
  year={2020}
}

@inproceedings{arpit2017closer,
  title={A closer look at memorization in deep networks},
  author={Arpit, Devansh and Jastrzebski, Stanislaw and Ballas, Nicolas and Krueger, David and Bengio, Emmanuel and Kanwal, Maxinder S and Maharaj, Tegan and Fischer, Asja and Courville, Aaron and Bengio, Yoshua and others},
  booktitle={ICML},
  year={2017}
}

@article{ben2010theory,
  title={A theory of learning from different domains},
  author={Ben-David, Shai and Blitzer, John and Crammer, Koby and Kulesza, Alex and Pereira, Fernando and Vaughan, Jennifer Wortman},
  journal={ML},
  year={2010},
}

@inproceedings{beery2018recognition,
  title={Recognition in terra incognita},
  author={Beery, Sara and Van Horn, Grant and Perona, Pietro},
  booktitle={ECCV},
  year={2018}
}

@inproceedings{li2017deeper,
  title={Deeper, Broader and Artier Domain Generalization},
  author={Da Li and Yongxin Yang and Yi-Zhe Song and Timothy M. Hospedales},
  booktitle={ICCV},
  year={2017},
}

@article{warden2018speech,
  title={Speech Commands: A Dataset for Limited-Vocabulary Speech Recognition},
  author={Pete Warden},
  journal={arXiv:1804.03209},
  year={2018},
}

@inproceedings{feydy2019interpolating,
  title={Interpolating between optimal transport and MMD using Sinkhorn divergences},
  author={Feydy, Jean and S{\'e}journ{\'e}, Thibault and Vialard, Fran{\c{c}}ois-Xavier and Amari, Shun-Ichi and Trouv{\'e}, Alain and Peyr{\'e}, Gabriel},
  booktitle={AISTATS},
  year={2019}
}

@inproceedings{sainath2015conv,
  title={Convolutional neural networks for small-footprint keyword spottingt},
  author={Tara N. Sainath and Carolina Parada},
  booktitle={INTERSPEECH},
  year={2015}
}

@inproceedings{kang2018deep,
  title={Deep adversarial attention alignment for unsupervised domain adaptation: the benefit of target expectation maximization},
  author={Kang, Guoliang and Zheng, Liang and Yan, Yan and Yang, Yi},
  booktitle={ECCV},
  year={2018}
}

@inproceedings{pinheiro2018unsupervised,
  title={Unsupervised domain adaptation with similarity learning},
  author={Pinheiro, Pedro O},
  booktitle={CVPR},
  year={2018}
}

@inproceedings{rozantsev2018residual,
  title={Residual parameter transfer for deep domain adaptation},
  author={Rozantsev, Artem and Salzmann, Mathieu and Fua, Pascal},
  booktitle={CVPR},
  year={2018}
}

@inproceedings{he2016deep,
  title={Deep residual learning for image recognition},
  author={He, Kaiming and Zhang, Xiangyu and Ren, Shaoqing and Sun, Jian},
  booktitle={CVPR},
  year={2016}
}

@inproceedings{gopalan2011domain,
  title={Domain adaptation for object recognition: An unsupervised approach},
  author={Gopalan, Raghuraman and Li, Ruonan and Chellappa, Rama},
  booktitle={ICCV},
  year={2011},
}


@article{ghifary2017scatter,
  title={Scatter component analysis: A unified framework for domain adaptation and domain generalization},
  author={Ghifary, Muhammad and Balduzzi, David and Kleijn, W Bastiaan and Zhang, Mengjie},
  journal={TPAMI},
  year={2017},
}

@inproceedings{zheng2015scalable,
  title={Scalable person re-identification: A benchmark},
  author={Zheng, Liang and Shen, Liyue and Tian, Lu and Wang, Shengjin and Wang, Jingdong and Tian, Qi},
  booktitle={ICCV},
  year={2015}
}

@inproceedings{ristani2016performance,
  title={Performance measures and a data set for multi-target, multi-camera tracking},
  author={Ristani, Ergys and Solera, Francesco and Zou, Roger and Cucchiara, Rita and Tomasi, Carlo},
  booktitle={ECCV},
  year={2016}
}
@inproceedings{zheng2017unlabeled,
  title={Unlabeled Samples Generated by GAN Improve the Person Re-identification Baseline in Vitro},
  author={Zhedong Zheng and Liang Zheng and Yi Yang},
  booktitle={ICCV},
  year={2017},
}

@inproceedings{pan2008transfer,
	author    = {Sinno Jialin Pan and
	James T. Kwok and
	Qiang Yang},
	title     = {Transfer Learning via Dimensionality Reduction},
	booktitle = {AAAI},
	year      = {2008}
}

@inproceedings{ganin2015unsupervised,
  title={Unsupervised Domain Adaptation by Backpropagation},
  author={Yaroslav Ganin and Victor S. Lempitsky},
  booktitle={ICML},
  year={2015}
}

@inproceedings{wang2019transferable,
  title={Transferable Normalization: Towards Improving Transferability of Deep Neural Networks},
  author={Wang, Ximei and Jin, Ying and Long, Mingsheng and Wang, Jianmin and Jordan, Michael I},
  booktitle={NeurIPS},
  year={2019}
}

@inproceedings{lee2019sliced,
  title={Sliced wasserstein discrepancy for unsupervised domain adaptation},
  author={Lee, Chen-Yu and Batra, Tanmay and Baig, Mohammad Haris and Ulbricht, Daniel},
  booktitle={CVPR},
  year={2019}
}

@inproceedings{long2015learning,
  title={Learning transferable features with deep adaptation networks},
  author={Long, Mingsheng and Cao, Yue and Wang, Jianmin and Jordan, Michael I},
  booktitle={ICML},
  year={2015}
}

@article{redko2020survey,
  title={A survey on domain adaptation theory: learning bounds and theoretical guarantees},
  author={Redko, Ievgen and Morvant, Emilie and Habrard, Amaury and Sebban, Marc and Bennani, Youn{\`e}s},
  journal={arXiv preprint arXiv:2004.11829},
  year={2020}
}

@inproceedings{liu2020shape,
  title={Shape-aware meta-learning for generalizing prostate mri segmentation to unseen domains},
  author={Liu, Quande and Dou, Qi and Heng, Pheng-Ann},
  booktitle={MICCAI},
  year={2020},
}

@inproceedings{kulis2011you,
  title={What you saw is not what you get: Domain adaptation using asymmetric kernel transforms},
  author={Kulis, Brian and Saenko, Kate and Darrell, Trevor},
  booktitle={CVPR},
  year={2011},
}

@inproceedings{saenko2010adapting,
  title={Adapting visual category models to new domains},
  author={Saenko, Kate and Kulis, Brian and Fritz, Mario and Darrell, Trevor},
  booktitle={ECCV},
  year={2010}
}

@article{deshmukh2019generalization,
  title={A generalization error bound for multi-class domain generalization},
  author={Deshmukh, Aniket Anand and Lei, Yunwen and Sharma, Srinagesh and Dogan, Urun and Cutler, James W and Scott, Clayton},
  journal={arXiv preprint arXiv:1905.10392},
  year={2019}
}

@inproceedings{liu2019transferable,
  title={Transferable Adversarial Training: A General Approach to Adapting Deep Classifiers},
  author={Liu, Hong and Long, Mingsheng and Wang, Jianmin and Jordan, Michael},
  booktitle={ICML},
  year={2019}
}

@inproceedings{Zhang2020Consistency,
title={Consistency Regularization for Generative Adversarial Networks},
author={Han Zhang and Zizhao Zhang and Augustus Odena and Honglak Lee},
booktitle={ICLR},
year={2020},
}

@article{devries2017cutout,
  title={Improved regularization of convolutional neural networks with cutout},
  author={DeVries, Terrance and Taylor, Graham W},
  journal={arXiv preprint arXiv:1708.04552},
  year={2017}
}

@article{peyre2019computational,
  title={Computational optimal transport},
  author={Peyr{\'e}, Gabriel and Cuturi, Marco and others},
  journal={Foundations and Trends{\textregistered} in Machine Learning},
  year={2019},
}

@article{ganin2016domain,
  author={Yaroslav Ganin and Evgeniya Ustinova and Hana Ajakan and Pascal Germain and Hugo Larochelle and Fran\c{c}ois Laviolette and Mario Marchand and Victor Lempitsky},
  title={Domain-Adversarial Training of Neural Networks},
  journal={JMLR},
  year={2016},
}

@inproceedings{bousmalis2016domain,
  title={Domain Separation Networks},
  author={Konstantinos Bousmalis and George Trigeorgis and Nathan Silberman and Dilip Krishnan and Dumitru Erhan},
  booktitle={NeurIPS},
  year={2016}
}

@inproceedings{sajjadi2016regularization,
  title={Regularization with stochastic transformations and perturbations for deep semi-supervised learning},
  author={Sajjadi, Mehdi and Javanmardi, Mehran and Tasdizen, Tolga},
  booktitle={NeurIPS},
  year={2016}
}

@article{miyato2018virtual,
  title={Virtual adversarial training: a regularization method for supervised and semi-supervised learning},
  author={Miyato, Takeru and Maeda, Shin-ichi and Koyama, Masanori and Ishii, Shin},
  journal={TPAMI},
  year={2018},
}

@inproceedings{muandet2013domain,
  title={Domain Generalization via Invariant Feature Representation},
  author={Krikamol Muandet and David Balduzzi and Bernhard Scholkopf},
  booktitle={ICML},
  year={2013}
}

@inproceedings{karpathy2014large,
  title={Large-scale video classification with convolutional neural networks},
  author={Karpathy, Andrej and Toderici, George and Shetty, Sanketh and Leung, Thomas and Sukthankar, Rahul and Fei-Fei, Li},
  booktitle={CVPR},
  year={2014}
}

@inproceedings{taigman2014deepface,
  title={Deepface: Closing the gap to human-level performance in face verification},
  author={Taigman, Yaniv and Yang, Ming and Ranzato, Marc'Aurelio and Wolf, Lior},
  booktitle={CVPR},
  year={2014}
}

@inproceedings{long2015fully,
  title={Fully convolutional networks for semantic segmentation},
  author={Long, Jonathan and Shelhamer, Evan and Darrell, Trevor},
  booktitle={CVPR},
  year={2015}
}

@inproceedings{dong2014learning,
  title={Learning a deep convolutional network for image super-resolution},
  author={Dong, Chao and Loy, Chen Change and He, Kaiming and Tang, Xiaoou},
  booktitle={ECCV},
  year={2014},
}

@inproceedings{girshick2014rich,
  title={Rich feature hierarchies for accurate object detection and semantic segmentation},
  author={Girshick, Ross and Donahue, Jeff and Darrell, Trevor and Malik, Jitendra},
  booktitle={CVPR},
  year={2014}
}

@inproceedings{xu2019larger,
  title={Larger Norm More Transferable: An Adaptive Feature Norm Approach for Unsupervised Domain Adaptation},
  author={Xu, Ruijia and Li, Guanbin and Yang, Jihan and Lin, Liang},
  booktitle={ICCV},
  year={2019}
}

@inproceedings{panareda2017open,
  title={Open set domain adaptation},
  author={Panareda Busto, Pau and Gall, Juergen},
  booktitle={ICCV},
  year={2017}
}

@inproceedings{taori2020measuring,
  title={Measuring robustness to natural distribution shifts in image classification},
  author={Taori, Rohan and Dave, Achal and Shankar, Vaishaal and Carlini, Nicholas and Recht, Benjamin and Schmidt, Ludwig},
  booktitle={NeurIPS},
  year={2020}
}

@inproceedings{li2021learning,
  title={Learning Invariant Representations and Risks for Semi-supervised Domain Adaptation},
  author={Li, Bo and Wang, Yezhen and Zhang, Shanghang and Li, Dongsheng and Darrell, Trevor and Keutzer, Kurt and Zhao, Han},
  booktitle={CVPR},
  year={2021}
}

@inproceedings{gordon2019meta,
  title={Meta-learning probabilistic inference for prediction},
  author={Gordon, Jonathan and Bronskill, John and Bauer, Matthias and Nowozin, Sebastian and Turner, Richard E},
  booktitle={ICLR},
  year={2019}
}

@inproceedings{perez2020incremental,
  title={Incremental few-shot object detection},
  author={Perez-Rua, Juan-Manuel and Zhu, Xiatian and Hospedales, Timothy M and Xiang, Tao},
  booktitle={CVPR},
  year={2020}
}

@inproceedings{deng2019cluster,
  title={Cluster Alignment with a Teacher for Unsupervised Domain Adaptation},
  author={Deng, Zhijie and Luo, Yucen and Zhu, Jun},
  booktitle={ICCV},
  year={2019}
}

@inproceedings{zou2019confidence,
  title={Confidence regularized self-training},
  author={Zou, Yang and Yu, Zhiding and Liu, Xiaofeng and Kumar, BVK and Wang, Jinsong},
  booktitle={ICCV},
  year={2019}
}

@inproceedings{pan2019transferrable,
  title={Transferrable prototypical networks for unsupervised domain adaptation},
  author={Pan, Yingwei and Yao, Ting and Li, Yehao and Wang, Yu and Ngo, Chong-Wah and Mei, Tao},
  booktitle={CVPR},
  year={2019}
}

@inproceedings{chang2019domain,
  title={Domain-Specific Batch Normalization for Unsupervised Domain Adaptation},
  author={Chang, Woong-Gi and You, Tackgeun and Seo, Seonguk and Kwak, Suha and Han, Bohyung},
  booktitle={CVPR},
  year={2019}
}

@inproceedings{kang2019contrastive,
  title={Contrastive adaptation network for unsupervised domain adaptation},
  author={Kang, Guoliang and Jiang, Lu and Yang, Yi and Hauptmann, Alexander G},
  booktitle={CVPR},
  year={2019}
}

@article{rozantsev2018beyond,
  title={Beyond sharing weights for deep domain adaptation},
  author={Rozantsev, Artem and Salzmann, Mathieu and Fua, Pascal},
  journal={TPAMI},
  year={2018}
}

@inproceedings{reddi2018on,
  title={On the convergence of Adam and Beyond},
  author={Sashank J. Reddi and Satyen Kale and Sanjiv Kumar},
  booktitle={ICLR},
  year={2018}
}

@InProceedings{Ulyanov_2017_CVPR,
author = {Ulyanov, Dmitry and Vedaldi, Andrea and Lempitsky, Victor},
title = {Improved Texture Networks: Maximizing Quality and Diversity in Feed-Forward Stylization and Texture Synthesis},
booktitle = {CVPR},
year = {2017}
}

@inproceedings{salimans2018improving,
  title={Improving GANs using optimal transport},
  author={Salimans, Tim and Zhang, Han and Radford, Alec and Metaxas, Dimitris},
  booktitle={ICLR},
  year={2018}
}


@InProceedings{WGAN,
  title = 	 {Wasserstein Generative Adversarial Networks},
  author = 	 {Martin Arjovsky and Soumith Chintala and L{\'e}on Bottou},
  booktitle = 	 {ICML},
  year = 	 {2017}
}

@article{CramerDistance,
  title={The cramer distance as a solution to biased wasserstein gradients},
  author={Bellemare, Marc G and Danihelka, Ivo and Dabney, Will and Mohamed, Shakir and Lakshminarayanan, Balaji and Hoyer, Stephan and Munos, R{\'e}mi},
  journal={arXiv preprint arXiv:1705.10743},
  year={2017}
}


@inproceedings{SinkhornAutoDiff,
  title={Learning generative models with sinkhorn divergences},
  author={Genevay, Aude and Peyr{\'e}, Gabriel and Cuturi, Marco},
  booktitle={AISTATS},
  year={2018}
}

@article{moreno2012unifying,
  title={A unifying view on dataset shift in classification},
  author={Moreno-Torres, Jose G and Raeder, Troy and Alaiz-Rodr{\'\i}guez, Roc{\'\i}o and Chawla, Nitesh V and Herrera, Francisco},
  journal={PR},
  year={2012},
}


@inproceedings{cuturi2013sinkhorn,
  title={Sinkhorn distances: Lightspeed computation of optimal transport},
  author={Cuturi, Marco},
  booktitle={NeurIPS},
  year={2013}
}

@inproceedings{ioffe2015batch,
  title={Batch normalization: Accelerating deep network training by reducing internal covariate shift},
  author={Sergey Ioffe and Christian Szegedy},
  booktitle={ICML},
  year={2015}
}

@inproceedings{zhong2018generalizing,
  title={Generalizing a person retrieval model hetero-and homogeneously},
  author={Zhong, Zhun and Zheng, Liang and Li, Shaozi and Yang, Yi},
  booktitle={ECCV},
  year={2018}
}

@InProceedings{liu2019adaptive,
author = {Liu, Jiawei and Zha, Zheng-Jun and Chen, Di and Hong, Richang and Wang, Meng},
title = {Adaptive Transfer Network for Cross-Domain Person Re-Identification},
booktitle = {CVPR},
year = {2019}
}

@inproceedings{kuehne2011hmdb,
  title={HMDB: a large video database for human motion recognition},
  author={Kuehne, Hildegard and Jhuang, Hueihan and Garrote, Est{\'\i}baliz and Poggio, Tomaso and Serre, Thomas},
  booktitle={ICCV},
  year={2011},
}

@InProceedings{zhong2018gen,
author = {Zhong, Zhun and Zheng, Liang and Li, Shaozi and Yang, Yi},
title = {Generalizing A Person Retrieval Model Hetero- and Homogeneously},
booktitle = {ECCV},
year = {2018}
}

@article{zhong2019camstyle,
  title={CamStyle: A Novel Data Augmentation Method for Person Re-identification},
  author={Zhong, Zhun and Zheng, Liang and Zheng, Zhedong and Li, Shaozi and Yang, Yi},
  journal={TIP},
  year={2019}
}

@article{bucci2020self,
  title={Self-Supervised Learning Across Domains},
  author={Bucci, Silvia and D'Innocente, Antonio and Liao, Yujun and Carlucci, Fabio Maria and Caputo, Barbara and Tommasi, Tatiana},
  journal={arXiv preprint arXiv:2007.12368},
  year={2020}
}

@inproceedings{yosinski2014transferable,
  title={How transferable are features in deep neural networks?},
  author={Yosinski, Jason and Clune, Jeff and Bengio, Yoshua and Lipson, Hod},
  booktitle={NeurIPS},
  year={2014}
}

@inproceedings{deng2018image,
  Author = {Deng, Weijian and Zheng, Liang and Ye, Qixiang and Kang, Guoliang and Yang, Yi and Jiao, Jianbin},
  Booktitle = {CVPR},
  Title = {Image-Image Domain Adaptation With Preserved Self-Similarity and Domain-Dissimilarity for Person Re-Identification},
  Year = {2018}
}

@article{albuquerque2020improving,
  title={Improving out-of-distribution generalization via multi-task self-supervised pretraining},
  author={Albuquerque, Isabela and Naik, Nikhil and Li, Junnan and Keskar, Nitish and Socher, Richard},
  journal={arXiv preprint arXiv:2003.13525},
  year={2020}
}

@article{courty2016optimal,
  title={Optimal transport for domain adaptation},
  author={Courty, Nicolas and Flamary, R{\'e}mi and Tuia, Devis and Rakotomamonjy, Alain},
  journal={TPAMI},
  year={2016}
}

@inproceedings{shen2018wasserstein,
  title={Wasserstein distance guided representation learning for domain adaptation},
  author={Shen, Jian and Qu, Yanru and Zhang, Weinan and Yu, Yong},
  booktitle={AAAI},
  year={2018}
}

@inproceedings{bhushan2018deepjdot,
  title={Deepjdot: Deep joint distribution optimal transport for unsupervised domain adaptation},
  author={Bhushan Damodaran, Bharath and Kellenberger, Benjamin and Flamary, R{\'e}mi and Tuia, Devis and Courty, Nicolas},
  booktitle={ECCV},
  year={2018}
}

@inproceedings{jia2020single,
  title={Single-side domain generalization for face anti-spoofing},
  author={Jia, Yunpei and Zhang, Jie and Shan, Shiguang and Chen, Xilin},
  booktitle={CVPR},
  year={2020}
}

@inproceedings{matsuura2020domain,
  title={Domain Generalization Using a Mixture of Multiple Latent Domains},
  author={Matsuura, Toshihiko and Harada, Tatsuya},
  booktitle={AAAI},
  year={2020}
}

@inproceedings{liu2020open,
  title={Open compound domain adaptation},
  author={Liu, Ziwei and Miao, Zhongqi and Pan, Xingang and Zhan, Xiaohang and Lin, Dahua and Yu, Stella X and Gong, Boqing},
  booktitle={CVPR},
  year={2020}
}

@inproceedings{verma2019manifold,
  title={Manifold mixup: Better representations by interpolating hidden states},
  author={Verma, Vikas and Lamb, Alex and Beckham, Christopher and Najafi, Amir and Mitliagkas, Ioannis and Lopez-Paz, David and Bengio, Yoshua},
  booktitle={ICML},
  year={2019}
}

@inproceedings{long2017deep,
  title={Deep transfer learning with joint adaptation networks},
  author={Long, Mingsheng and Zhu, Han and Wang, Jianmin and Jordan, Michael I},
  booktitle={ICML},
  year={2017}
}

@inproceedings{wang2019robust,
  title={Learning robust global representations by penalizing local predictive power},
  author={Wang, Haohan and Ge, Songwei and Lipton, Zachary and Xing, Eric P},
  booktitle={NeurIPS},
  year={2019}
}

@inproceedings{wang2018transferable,
  title={Transferable Joint Attribute-Identity Deep Learning for Unsupervised Person Re-Identification},
  author={Jingya Wang and Xiatian Zhu and Shaogang Gong and Wei Li},
  booktitle={CVPR},
  year={2018}
}

@inproceedings{shu2018dirt,
  title={A DIRT-T APPROACH TO UNSUPERVISED DOMAIN ADAPTATION},
  author={Shu, Rui and Bui, Hung H and Narui, Hirokazu and Ermon, Stefano},
  booktitle={ICLR},
  year={2018}
}

@article{devries2017improved,
  title={Improved regularization of convolutional neural networks with cutout},
  author={DeVries, Terrance and Taylor, Graham W},
  journal={arXiv preprint arXiv:1708.04552},
  year={2017}
}

@inproceedings{yun2019cutmix,
  title={Cutmix: Regularization strategy to train strong classifiers with localizable features},
  author={Yun, Sangdoo and Han, Dongyoon and Oh, Seong Joon and Chun, Sanghyuk and Choe, Junsuk and Yoo, Youngjoon},
  booktitle={ICCV},
  year={2019}
}

@article{srivastava2014dropout,
  title={Dropout: a simple way to prevent neural networks from overfitting},
  author={Srivastava, Nitish and Hinton, Geoffrey and Krizhevsky, Alex and Sutskever, Ilya and Salakhutdinov, Ruslan},
  journal={JMLR},
  year={2014},
}

@inproceedings{ghiasi2018dropblock,
  title={Dropblock: A regularization method for convolutional networks},
  author={Ghiasi, Golnaz and Lin, Tsung-Yi and Le, Quoc V},
  booktitle={NeurIPS},
  year={2018}
}

@article{yao2019adversarial,
  title={Adversarial Pyramid Network for Video Domain Generalization},
  author={Yao, Zhiyu and Wang, Yunbo and Du, Xingqiang and Long, Mingsheng and Wang, Jianmin},
  journal={arXiv preprint arXiv:1912.03716},
  year={2019}
}

@inproceedings{saito2018adversarial,
title={Adversarial Dropout Regularization},
author={Kuniaki Saito and Yoshitaka Ushiku and Tatsuya Harada and Kate Saenko},
booktitle={ICLR},
year={2018},
}

@article{chen2020improving,
  title={Improving the generalizability of convolutional neural network-based segmentation on CMR images},
  author={Chen, Chen and Bai, Wenjia and Davies, Rhodri H and Bhuva, Anish N and Manisty, Charlotte H and Augusto, Joao B and Moon, James C and Aung, Nay and Lee, Aaron M and Sanghvi, Mihir M and others},
  journal={Frontiers in cardiovascular medicine},
  year={2020},
}

@article{otalora2019staining,
  title={Staining invariant features for improving generalization of deep convolutional neural networks in computational pathology},
  author={Ot{\'a}lora, Sebastian and Atzori, Manfredo and Andrearczyk, Vincent and Khan, Amjad and M{\"u}ller, Henning},
  journal={Frontiers in bioengineering and biotechnology},
  year={2019},
}

@inproceedings{volpi2018generalizing,
  title={Generalizing to Unseen Domains via Adversarial Data Augmentation},
  author={Riccardo Volpi and Hongseok Namkoong and Ozan Sener and John Duchi and Vittorio Murino and Silvio Savarese},
  booktitle={NeurIPS},
  year={2018}
}

@inproceedings{khosla2012undoing,
  author = {Aditya Khosla and Tinghui Zhou and Tomasz Malisiewicz and Alexei Efros and Antonio Torralba},
  title = {Undoing the Damage of Dataset Bias},
  booktitle = {ECCV},
  year = {2012},
}

@inproceedings{xu2014exploiting,
  author = {Zheng Xu and Wen Li and Li Niu and Dong Xu},
  title = {Exploiting Low-Rank Structure from Latent Domains for Domain Generalization},
  booktitle = {ECCV},
  year = {2014},
}

@article{farebrother2018generalization,
  title={Generalization and regularization in DQN},
  author={Farebrother, Jesse and Machado, Marlos C and Bowling, Michael},
  journal={arXiv preprint arXiv:1810.00123},
  year={2018}
}

@article{li2018lowrank,
  author={Wen Li and Zheng Xu and Dong Xu and Dengxin Dai and Luc Van Gool},
  title={Domain Generalization and Adaptation using Low Rank Exemplar SVMs},
  journal={TPAMI},
  year={2018},
}

@inproceedings{bermudez2020domain,
  title={Domain-Adaptive Multibranch Networks},
  author={Berm{\'u}dez Chac{\'o}n, R{\'o}ger and Salzmann, Mathieu and Fua, Pascal},
  booktitle={ICLR},
  year={2020}
}

@inproceedings{zhou2021mixstyle,
title={Domain Generalization with MixStyle},
author={Kaiyang Zhou and Yongxin Yang and Yu Qiao and Tao Xiang},
booktitle={ICLR},
year={2021},
}

@article{segu2020batch,
  title={Batch Normalization Embeddings for Deep Domain Generalization},
  author={Seg{\`u}, Mattia and Tonioni, Alessio and Tombari, Federico},
  journal={arXiv preprint arXiv:2011.12672},
  year={2020}
}

@article{borlino2021rethinking,
  title={Rethinking Domain Generalization Baselines},
  author={Borlino, Francesco Cappio and D'Innocente, Antonio and Tommasi, Tatiana},
  journal={arXiv preprint arXiv:2101.09060},
  year={2021}
}

@inproceedings{szegedy2016rethinking,
  title={Rethinking the inception architecture for computer vision},
  author={Szegedy, Christian and Vanhoucke, Vincent and Ioffe, Sergey and Shlens, Jon and Wojna, Zbigniew},
  booktitle={CVPR},
  year={2016}
}

@inproceedings{zhang2017understanding,
  title={Understanding deep learning requires rethinking generalization},
  author={Zhang, Chiyuan and Bengio, Samy and Hardt, Moritz and Recht, Benjamin and Vinyals, Oriol},
  booktitle={ICLR},
  year={2017}
}

@inproceedings{ilse2019diva,
  title={DIVA: Domain Invariant Variational Autoencoder},
  author={Ilse, Maximilian and Tomczak, Jakub M and Louizos, Christos and Welling, Max},
  booktitle={ICLR-W},
  year={2019}
}

@inproceedings{wu2018unsupervised,
  title={Unsupervised feature learning via non-parametric instance discrimination},
  author={Wu, Zhirong and Xiong, Yuanjun and Yu, Stella X and Lin, Dahua},
  booktitle={CVPR},
  year={2018}
}

@inproceedings{erfani2016robust,
  title={Robust domain generalisation by enforcing distribution invariance},
  author={Erfani, Sarah and Baktashmotlagh, Mahsa and Moshtaghi, Masud and Nguyen, Xuan and Leckie, Christopher and Bailey, James and Kotagiri, Rao},
  booktitle={IJCAI},
  year={2016}
}

@inproceedings{ghifary2015domain,
  author = {Muhammad Ghifary and W. Bastiaan Kleijn and Mengjie Zhang and David Balduzzi},
  title = {Domain Generalization for Object Recognition with Multi-task Autoencoders},
  booktitle = {ICCV},
  year = {2015},
}

@article{li2016revisiting,
  title={Revisiting batch normalization for practical domain adaptation},
  author={Li, Yanghao and Wang, Naiyan and Shi, Jianping and Liu, Jiaying and Hou, Xiaodi},
  journal={arXiv preprint arXiv:1603.04779},
  year={2016}
}

@inproceedings{tzeng2017adversarial,
  author = {Eric Tzeng and Judy Hoffman and Kate Saenko and Trevor Darrell},
  title = {Adversarial Discriminative Domain Adaptation},
  booktitle = {CVPR},
  year = {2017},
}

@inproceedings{wang2019learning,
  author = {Haohan Wang and Zexue He and Zachary C. Lipton and Eric P. Xing},
  title = {Learning Robust Representations by Projecting Superficial Statistics Out},
  booktitle = {ICLR},
  year = {2019},
}

@inproceedings{motiian2017unified,
  author = {Saeid Motiian and Marco Piccirilli and Donald A. Adjeroh and Gianfranco Doretto},
  title = {Unified Deep Supervised Domain Adaptation and Generalization},
  booktitle = {ICCV},
  year = {2017},
}

@article{deecke2020latent,
  title={Latent domain learning with dynamic residual adapters},
  author={Deecke, Lucas and Hospedales, Timothy and Bilen, Hakan},
  journal={arXiv preprint arXiv:2006.00996},
  year={2020}
}

@article{tzeng2014deep,
  title={Deep domain confusion: Maximizing for domain invariance},
  author={Tzeng, Eric and Hoffman, Judy and Zhang, Ning and Saenko, Kate and Darrell, Trevor},
  journal={arXiv preprint arXiv:1412.3474},
  year={2014}
}

@inproceedings{donahue2014decaf,
  title={Decaf: A deep convolutional activation feature for generic visual recognition},
  author={Donahue, Jeff and Jia, Yangqing and Vinyals, Oriol and Hoffman, Judy and Zhang, Ning and Tzeng, Eric and Darrell, Trevor},
  booktitle={ICML},
  year={2014},
}

@inproceedings{piratla2020efficient,
  title={Efficient domain generalization via common-specific low-rank decomposition},
  author={Piratla, Vihari and Netrapalli, Praneeth and Sarawagi, Sunita},
  booktitle={ICML},
  year={2020},
}

@inproceedings{chattopadhyay2020learning,
  title={Learning to balance specificity and invariance for in and out of domain generalization},
  author={Chattopadhyay, Prithvijit and Balaji, Yogesh and Hoffman, Judy},
  booktitle={ECCV},
  year={2020},
}

@inproceedings{yoon2019generalizable,
  title={Generalizable feature learning in the presence of data bias and domain class imbalance with application to skin lesion classification},
  author={Yoon, Chris and Hamarneh, Ghassan and Garbi, Rafeef},
  booktitle={MICCAI},
  year={2019}
}

@inproceedings{chen2019abd,
  title={Abd-net: Attentive but diverse person re-identification},
  author={Chen, Tianlong and Ding, Shaojin and Xie, Jingyi and Yuan, Ye and Chen, Wuyang and Yang, Yang and Ren, Zhou and Wang, Zhangyang},
  booktitle={ICCV},
  year={2019}
}

@article{li2019scalable,
  title={Scalable person re-identification by harmonious attention},
  author={Li, Wei and Zhu, Xiatian and Gong, Shaogang},
  journal={IJCV},
  year={2019},
}

@article{sun2019learning,
  title={Learning part-based convolutional features for person re-identification},
  author={Sun, Yifan and Zheng, Liang and Li, Yali and Yang, Yi and Tian, Qi and Wang, Shengjin},
  journal={TPAMI},
  year={2019},
}

@inproceedings{du2021metanorm,
title={MetaNorm: Learning to Normalize Few-Shot Batches Across Domains},
author={Yingjun Du and Xiantong Zhen and Ling Shao and Cees G. M. Snoek},
booktitle={ICLR},
year={2021},
}

@article{lampert2014attribute,
  title={Attribute-based classification for zero-shot visual object categorization},
  author={Lampert, Christoph H and Nickisch, Hannes and Harmeling, Stefan},
  journal={TPAMI},
  year={2014},
}

@inproceedings{hoffman2016learning,
  title={Learning with side information through modality hallucination},
  author={Hoffman, Judy and Gupta, Saurabh and Darrell, Trevor},
  booktitle={CVPR},
  year={2016}
}

@inproceedings{zhou2017detecting,
  title={Detecting humans in RGB-D data with CNNs},
  author={Zhou, Kaiyang and Paiement, Adeline and Mirmehdi, Majid},
  booktitle={MVA},
  year={2017},
}

@article{xian2018zero,
  title={Zero-shot learning—a comprehensive evaluation of the good, the bad and the ugly},
  author={Xian, Yongqin and Lampert, Christoph H and Schiele, Bernt and Akata, Zeynep},
  journal={TPAMI},
  year={2018},
}

@inproceedings{dosovitskiy2021an,
title={An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale},
author={Alexey Dosovitskiy and Lucas Beyer and Alexander Kolesnikov and Dirk Weissenborn and Xiaohua Zhai and Thomas Unterthiner and Mostafa Dehghani and Matthias Minderer and Georg Heigold and Sylvain Gelly and Jakob Uszkoreit and Neil Houlsby},
booktitle={ICLR},
year={2021}
}

@article{sinha2017certifying,
  title={Certifying some distributional robustness with principled adversarial training},
  author={Sinha, Aman and Namkoong, Hongseok and Volpi, Riccardo and Duchi, John},
  journal={arXiv preprint arXiv:1710.10571},
  year={2017}
}

@inproceedings{park2019semantic,
  title={Semantic image synthesis with spatially-adaptive normalization},
  author={Park, Taesung and Liu, Ming-Yu and Wang, Ting-Chun and Zhu, Jun-Yan},
  booktitle={CVPR},
  year={2019}
}

@inproceedings{luo2019differentiable,
title={Differentiable Learning-to-Normalize via Switchable Normalization},
author={Ping Luo and Jiamin Ren and Zhanglin Peng and Ruimao Zhang and Jingyu Li},
booktitle={ICLR},
year={2019},
}

@article{ba2016layer,
  title={Layer normalization},
  author={Ba, Jimmy Lei and Kiros, Jamie Ryan and Hinton, Geoffrey E},
  journal={arXiv preprint arXiv:1607.06450},
  year={2016}
}

@inproceedings{yang2019condconv,
  title={Condconv: Conditionally parameterized convolutions for efficient inference},
  author={Yang, Brandon and Bender, Gabriel and Le, Quoc V and Ngiam, Jiquan},
  booktitle={NeurIPS},
  year={2019}
}

@inproceedings{jia2016dynamic,
  title={Dynamic filter networks},
  author={Jia, Xu and De Brabandere, Bert and Tuytelaars, Tinne and Van Gool, Luc},
  booktitle={NeurIPS},
  year={2016}
}

@inproceedings{gulrajani2021in,
title={In Search of Lost Domain Generalization},
author={Ishaan Gulrajani and David Lopez-Paz},
booktitle={ICLR},
year={2021},
}

@article{krueger2020out,
  title={Out-of-distribution generalization via risk extrapolation (rex)},
  author={Krueger, David and Caballero, Ethan and Jacobsen, Joern-Henrik and Zhang, Amy and Binas, Jonathan and Priol, Remi Le and Courville, Aaron},
  journal={arXiv preprint arXiv:2003.00688},
  year={2020}
}

@article{bellot2021accounting,
      title={Accounting for Unobserved Confounding in Domain Generalization}, 
      author={Alexis Bellot and Mihaela van der Schaar},
      journal={arXiv preprint arXiv:2007.10653},
      year={2021},
}

@article{arjovsky2019invariant,
  title={Invariant risk minimization},
  author={Arjovsky, Martin and Bottou, L{\'e}on and Gulrajani, Ishaan and Lopez-Paz, David},
  journal={arXiv preprint arXiv:1907.02893},
  year={2019}
}

@inproceedings{li2018ciddg,
  author = {Ya Li and Xinmei Tiana and Mingming Gong and Yajing Liu and Tongliang Liu and Kun Zhang and Dacheng Tao},
  title = {Deep Domain Generalization via Conditional Invariant Adversarial Networks},
  booktitle = {ECCV},
  year = {2018},
}

@inproceedings{shao2019multi,
  title={Multi-adversarial discriminative deep domain generalization for face presentation attack detection},
  author={Shao, Rui and Lan, Xiangyuan and Li, Jiawei and Yuen, Pong C},
  booktitle={CVPR},
  year={2019}
}

@inproceedings{scholkopf2012causal,
  title={On causal and anticausal learning},
  author={Sch{\"o}lkopf, Bernhard and Janzing, Dominik and Peters, Jonas and Sgouritsa, Eleni and Zhang, Kun and Mooij, Joris},
  booktitle={ICML},
  year={2012}
}

@inproceedings{li2018domain,
  title={Domain generalization via conditional invariant representations},
  author={Li, Ya and Gong, Mingming and Tian, Xinmei and Liu, Tongliang and Tao, Dacheng},
  booktitle={AAAI},
  year={2018}
}

@article{weinland2006free,
  title={Free viewpoint action recognition using motion history volumes},
  author={Weinland, Daniel and Ronfard, Remi and Boyer, Edmond},
  journal={CVIU},
  year={2006},
}

@inproceedings{li2018mmdaae,
  author = {Li, Haoliang and Jialin Pan, Sinno and Wang, Shiqi and Kot, Alex C.},
  title = {Domain Generalization with Adversarial Feature Learning},
  booktitle = {CVPR},
  year = {2018},
}

@book{goodfellow2016deep,
    title={Deep Learning},
    author={Ian Goodfellow and Yoshua Bengio and Aaron Courville},
    publisher={MIT Press},
    year={2016}
}

@inproceedings{goodfellow2014generative,
  title={Generative adversarial nets},
  author={Goodfellow, Ian and Pouget-Abadie, Jean and Mirza, Mehdi and Xu, Bing and Warde-Farley, David and Ozair, Sherjil and Courville, Aaron and Bengio, Yoshua},
  booktitle={NeurIPS},
  year={2014}
}

@inproceedings{chang2018multi,
  author = {Chang, Xiaobin and Hospedales, Timothy M and Xiang, Tao},
  title = {Multi-Level Factorisation Net for Person Re-Identification},
  booktitle = {CVPR},
  year = {2018},
}

@inproceedings{wu2019large,
  title={Large scale incremental learning},
  author={Wu, Yue and Chen, Yinpeng and Wang, Lijuan and Ye, Yuancheng and Liu, Zicheng and Guo, Yandong and Fu, Yun},
  booktitle={CVPR},
  year={2019}
}

@inproceedings{zhu2021deformable,
  title={Deformable DETR: Deformable Transformers for End-to-End Object Detection},
  author={Zhu, Xizhou and Su, Weijie and Lu, Lewei and Li, Bin and Wang, Xiaogang and Dai, Jifeng},
  booktitle={ICLR},
  year={2021}
}

@article{han2021dynamic,
  title={Dynamic Neural Networks: A Survey},
  author={Han, Yizeng and Huang, Gao and Song, Shiji and Yang, Le and Wang, Honghui and Wang, Yulin},
  journal={arXiv preprint arXiv:2102.04906},
  year={2021}
}

@inproceedings{carion2020end,
  title={End-to-end object detection with transformers},
  author={Carion, Nicolas and Massa, Francisco and Synnaeve, Gabriel and Usunier, Nicolas and Kirillov, Alexander and Zagoruyko, Sergey},
  booktitle={ECCV},
  year={2020},
}

@inproceedings{vaswani2017attention,
  title={Attention is all you need},
  author={Vaswani, Ashish and Shazeer, Noam and Parmar, Niki and Uszkoreit, Jakob and Jones, Llion and Gomez, Aidan N and Kaiser, Lukasz and Polosukhin, Illia},
  booktitle={NeurIPS},
  year={2017}
}

@inproceedings{li2018harmonious,
  author = {Li, Wei and Zhu, Xiatian and Gong, Shaogang},
  title = {Harmonious attention network for person re-identification},
  booktitle = {CVPR},
  year = {2018},
}

@inproceedings{almahairi2018augmented,
  title={Augmented cyclegan: Learning many-to-many mappings from unpaired data},
  author={Almahairi, Amjad and Rajeswar, Sai and Sordoni, Alessandro and Bachman, Philip and Courville, Aaron},
  booktitle={ICML},
  year={2018}
}

@inproceedings{xu2021how,
title={How Neural Networks Extrapolate: From Feedforward to Graph Neural Networks},
author={Keyulu Xu and Mozhi Zhang and Jingling Li and Simon Shaolei Du and Ken-Ichi Kawarabayashi and Stefanie Jegelka},
booktitle={ICLR},
year={2021},
}

@inproceedings{xiao2010sun,
  title={Sun database: Large-scale scene recognition from abbey to zoo},
  author={Xiao, Jianxiong and Hays, James and Ehinger, Krista A and Oliva, Aude and Torralba, Antonio},
  booktitle={CVPR},
  year={2010},
}

@article{russell2008labelme,
  title={LabelMe: a database and web-based tool for image annotation},
  author={Russell, Bryan C and Torralba, Antonio and Murphy, Kevin P and Freeman, William T},
  journal={IJCV},
  year={2008},
}

@article{everingham2010pascal,
  title={The pascal visual object classes (voc) challenge},
  author={Everingham, Mark and Van Gool, Luc and Williams, Christopher KI and Winn, John and Zisserman, Andrew},
  journal={IJCV},
  year={2010},
}

@inproceedings{fei2004learning,
  title={Learning generative visual models from few training examples: An incremental bayesian approach tested on 101 object categories},
  author={Fei-Fei, Li and Fergus, Rob and Perona, Pietro},
  booktitle={CVPR-W},
  year={2004},
}

@article{bengio2019meta,
  title={A meta-transfer objective for learning to disentangle causal mechanisms},
  author={Bengio, Yoshua and Deleu, Tristan and Rahaman, Nasim and Ke, Rosemary and Lachapelle, S{\'e}bastien and Bilaniuk, Olexa and Goyal, Anirudh and Pal, Christopher},
  journal={arXiv preprint arXiv:1901.10912},
  year={2019}
}

@article{scholkopf2021towards,
  title={Towards Causal Representation Learning},
  author={Scholkopf, Bernhard and Locatello, Francesco and Bauer, Stefan and Ke, Nan Rosemary and Kalchbrenner, Nal and Goyal, Anirudh and Bengio, Yoshua},
  journal={arXiv preprint arXiv:2102.11107},
  year={2021}
}


@inproceedings{torralba2011unbiased,
  author = {Antonio Torralba and Alexei A. Efros},
  title = {Unbiased Look at Dataset Bias},
  booktitle = {CVPR},
  year = {2011},
}

@article{rojas2018invariant,
  title={Invariant models for causal transfer learning},
  author={Rojas-Carulla, Mateo and Sch{\"o}lkopf, Bernhard and Turner, Richard and Peters, Jonas},
  journal={JMLR},
  year={2018},
}

@article{zunino2020explainable,
  title={Explainable deep classification models for domain generalization},
  author={Zunino, Andrea and Bargal, Sarah Adel and Volpi, Riccardo and Sameki, Mehrnoosh and Zhang, Jianming and Sclaroff, Stan and Murino, Vittorio and Saenko, Kate},
  journal={arXiv preprint arXiv:2003.06498},
  year={2020}
}

@inproceedings{goodfellow2015explaining,
  title={Explaining and Harnessing Adversarial Examples},
  author={Ian J. Goodfellow and Jonathon Shlens and Christian Szegedy},
  booktitle={ICLR},
  year={2015},
}

@inproceedings{guo2020learning,
  title={Learning to branch for multi-task learning},
  author={Guo, Pengsheng and Lee, Chen-Yu and Ulbricht, Daniel},
  booktitle={ICML},
  year={2020},
}

@inproceedings{chingovska2012effectiveness,
  title={On the effectiveness of local binary patterns in face anti-spoofing},
  author={Chingovska, Ivana and Anjos, Andr{\'e} and Marcel, S{\'e}bastien},
  booktitle={BIOSIG},
  year={2012},
}

@article{wen2015face,
  title={Face spoof detection with image distortion analysis},
  author={Wen, Di and Han, Hu and Jain, Anil K},
  journal={TIFS},
  year={2015},
}

@inproceedings{boulkenafet2017oulu,
  title={OULU-NPU: A mobile face presentation attack database with real-world variations},
  author={Boulkenafet, Zinelabinde and Komulainen, Jukka and Li, Lei and Feng, Xiaoyi and Hadid, Abdenour},
  booktitle={FG},
  year={2017},
}

@inproceedings{zhang2012face,
  title={A face antispoofing database with diverse attacks},
  author={Zhang, Zhiwei and Yan, Junjie and Liu, Sifei and Lei, Zhen and Yi, Dong and Li, Stan Z},
  booktitle={ICB},
  year={2012},
}

@inproceedings{real2017youtube,
  title={Youtube-boundingboxes: A large high-precision human-annotated data set for object detection in video},
  author={Real, Esteban and Shlens, Jonathon and Mazzocchi, Stefano and Pan, Xin and Vanhoucke, Vincent},
  booktitle={CVPR},
  year={2017}
}

@inproceedings{lin2014microsoft,
  title={Microsoft coco: Common objects in context},
  author={Lin, Tsung-Yi and Maire, Michael and Belongie, Serge and Hays, James and Perona, Pietro and Ramanan, Deva and Doll{\'a}r, Piotr and Zitnick, C Lawrence},
  booktitle={ECCV},
  year={2014},
}

@inproceedings{sun2020adashare,
  title={Adashare: Learning what to share for efficient deep multi-task learning},
  author={Sun, Ximeng and Panda, Rameswar and Feris, Rogerio and Saenko, Kate},
  booktitle={NeurIPS},
  year={2020}
}

@inproceedings{mallya2018piggyback,
  title={Piggyback: Adapting a single network to multiple tasks by learning to mask weights},
  author={Mallya, Arun and Davis, Dillon and Lazebnik, Svetlana},
  booktitle={ECCV},
  year={2018}
}

@inproceedings{liu2019end,
  title={End-to-end multi-task learning with attention},
  author={Liu, Shikun and Johns, Edward and Davison, Andrew J},
  booktitle={CVPR},
  year={2019}
}

@inproceedings{rebuffi2017learning,
  title={Learning multiple visual domains with residual adapters},
  author={Rebuffi, Sylvestre-Alvise and Bilen, Hakan and Vedaldi, Andrea},
  booktitle={NeurIPS},
  year={2017}
}

@inproceedings{nilsback2008automated,
  title={Automated flower classification over a large number of classes},
  author={Nilsback, Maria-Elena and Zisserman, Andrew},
  booktitle={ICCVGIP},
  year={2008},
}

@article{stallkamp2012man,
  title={Man vs. computer: Benchmarking machine learning algorithms for traffic sign recognition},
  author={Stallkamp, Johannes and Schlipsing, Marc and Salmen, Jan and Igel, Christian},
  journal={NN},
  year={2012},
}

@inproceedings{cimpoi2014describing,
  title={Describing textures in the wild},
  author={Cimpoi, Mircea and Maji, Subhransu and Kokkinos, Iasonas and Mohamed, Sammy and Vedaldi, Andrea},
  booktitle={CVPR},
  year={2014}
}

@article{munder2006experimental,
  title={An experimental study on pedestrian classification},
  author={Munder, Stefan and Gavrila, Dariu M},
  journal={TPAMI},
  year={2006},
}

@article{krizhevsky2009learning,
  title={Learning multiple layers of features from tiny images},
  author={Krizhevsky, Alex and Hinton, Geoffrey},
  year={2009},
}

@article{maji2013fine,
  title={Fine-grained visual classification of aircraft},
  author={Maji, Subhransu and Rahtu, Esa and Kannala, Juho and Blaschko, Matthew and Vedaldi, Andrea},
  journal={arXiv preprint arXiv:1306.5151},
  year={2013}
}

@inproceedings{meyerson2019modular,
  title={Modular universal reparameterization: Deep multi-task learning across diverse domains},
  author={Meyerson, Elliot and Miikkulainen, Risto},
  booktitle={NeurIPS},
  year={2019}
}

@inproceedings{yang2017deep,
  title={Deep multi-task representation learning: A tensor factorisation approach},
  author={Yang, Yongxin and Hospedales, Timothy},
  booktitle={ICLR},
  year={2017}
}

@article{zhang2017survey,
  title={A survey on multi-task learning},
  author={Zhang, Yu and Yang, Qiang},
  journal={arXiv preprint arXiv:1707.08114},
  year={2017}
}

@inproceedings{vapnik1992principles,
  title={Principles of risk minimization for learning theory},
  author={Vapnik, Vladimir},
  booktitle={NeurIPS},
  year={1992}
}

@inproceedings{zhu2015aligning,
  title={Aligning books and movies: Towards story-like visual explanations by watching movies and reading books},
  author={Zhu, Yukun and Kiros, Ryan and Zemel, Rich and Salakhutdinov, Ruslan and Urtasun, Raquel and Torralba, Antonio and Fidler, Sanja},
  booktitle={ICCV},
  year={2015}
}

@article{pan2009survey,
  title={A survey on transfer learning},
  author={Pan, Sinno Jialin and Yang, Qiang},
  journal={TKDE},
  year={2009},
}

@inproceedings{gamrian2019transfer,
  title={Transfer learning for related reinforcement learning tasks via image-to-image translation},
  author={Gamrian, Shani and Goldberg, Yoav},
  booktitle={ICML},
  year={2019}
}

@InProceedings{RelGAN,
author = {Wu, Po-Wei and Lin, Yu-Jing and Chang, Che-Han and Chang, Edward Y. and Liao, Shih-Wei},
title = {RelGAN: Multi-Domain Image-to-Image Translation via Relative Attributes},
booktitle = {ICCV},
year = {2019}
}

@InProceedings{liu2019fewshot,
author = {Liu, Ming-Yu and Huang, Xun and Mallya, Arun and Karras, Tero and Aila, Timo and Lehtinen, Jaakko and Kautz, Jan},
title = {Few-Shot Unsupervised Image-to-Image Translation},
booktitle = {ICCV},
year = {2019}
}

@article{zhang2018study,
  title={A study on overfitting in deep reinforcement learning},
  author={Zhang, Chiyuan and Vinyals, Oriol and Munos, Remi and Bengio, Samy},
  journal={arXiv preprint arXiv:1804.06893},
  year={2018}
}

@inproceedings{szegedy2014intriguing,
  title={Intriguing properties of neural networks},
  author={Christian Szegedy and Wojciech Zaremba and Ilya Sutskever and Joan Bruna and Dumitru Erhan and Ian J. Goodfellow and Rob Fergus},
  booktitle={ICLR},
  year={2014},
}

@inproceedings{sengupta2016frontal,
  title={Frontal to profile face verification in the wild},
  author={Sengupta, Soumyadip and Chen, Jun-Cheng and Castillo, Carlos and Patel, Vishal M and Chellappa, Rama and Jacobs, David W},
  booktitle={WACV},
  year={2016},
}

@inproceedings{cheng2018low,
  title={Low-resolution face recognition},
  author={Cheng, Zhiyi and Zhu, Xiatian and Gong, Shaogang},
  booktitle={ACCV},
  year={2018},
}

@inproceedings{kalka2018ijb,
  title={IJB--S: IARPA Janus surveillance video benchmark},
  author={Kalka, Nathan D and Maze, Brianna and Duncan, James A and O’Connor, Kevin and Elliott, Stephen and Hebert, Kaleb and Bryan, Julia and Jain, Anil K},
  booktitle={BTAS},
  year={2018},
}

@inproceedings{maze2018iarpa,
  title={Iarpa janus benchmark-c: Face dataset and protocol},
  author={Maze, Brianna and Adams, Jocelyn and Duncan, James A and Kalka, Nathan and Miller, Tim and Otto, Charles and Jain, Anil K and Niggel, W Tyler and Anderson, Janet and Cheney, Jordan and others},
  booktitle={ICB},
  year={2018},
}

@inproceedings{klare2015pushing,
  title={Pushing the frontiers of unconstrained face detection and recognition: Iarpa janus benchmark a},
  author={Klare, Brendan F and Klein, Ben and Taborsky, Emma and Blanton, Austin and Cheney, Jordan and Allen, Kristen and Grother, Patrick and Mah, Alan and Jain, Anil K},
  booktitle={CVPR},
  year={2015}
}

@inproceedings{kemelmacher2016megaface,
  title={The megaface benchmark: 1 million faces for recognition at scale},
  author={Kemelmacher-Shlizerman, Ira and Seitz, Steven M and Miller, Daniel and Brossard, Evan},
  booktitle={CVPR},
  year={2016}
}

@inproceedings{wolf2011face,
  title={Face recognition in unconstrained videos with matched background similarity},
  author={Wolf, Lior and Hassner, Tal and Maoz, Itay},
  booktitle={CVPR},
  year={2011},
}

@TechReport{lfw,
  author = {Gary B. Huang and Manu Ramesh and Tamara Berg and Erik Learned-Miller},
  title = {Labeled Faces in the Wild: A Database for Studying Face Recognition in Unconstrained Environments},
  institution =  {University of Massachusetts, Amherst},
  year = 2007,
}

@inproceedings{wei2018person,
  title={Person transfer gan to bridge domain gap for person re-identification},
  author={Wei, Longhui and Zhang, Shiliang and Gao, Wen and Tian, Qi},
  booktitle={CVPR},
  year={2018}
}

@inproceedings{guo2016ms,
  title={Ms-celeb-1m: A dataset and benchmark for large-scale face recognition},
  author={Guo, Yandong and Zhang, Lei and Hu, Yuxiao and He, Xiaodong and Gao, Jianfeng},
  booktitle={ECCV},
  year={2016},
}

@inproceedings{li2014deepreid,
  title={Deepreid: Deep filter pairing neural network for person re-identification},
  author={Li, Wei and Zhao, Rui and Xiao, Tong and Wang, Xiaogang},
  booktitle={CVPR},
  year={2014}
}

@inproceedings{shi2020towards,
  title={Towards universal representation learning for deep face recognition},
  author={Shi, Yichun and Yu, Xiang and Sohn, Kihyuk and Chandraker, Manmohan and Jain, Anil K},
  booktitle={CVPR},
  year={2020}
}

@inproceedings{madry2018towards,
  title={Towards Deep Learning Models Resistant to Adversarial Attacks},
  author={Aleksander Madry and Aleksandar Makelov and Ludwig Schmidt and Dimitris Tsipras and Adrian Vladu},
  booktitle={ICLR},
  year={2018},
}

@inproceedings{xie2018learning,
  title={Learning Semantic Representations for Unsupervised Domain Adaptation},
  author={Shaoan Xie and Zibin Zheng and Liang Chen and Chuan Chen},
  booktitle={ICML},
  year={2018},
}

@inproceedings{hoffman2018cycada,
  title={CyCADA: Cycle-Consistent Adversarial Domain Adaptation},
  author={Judy Hoffman and Eric Tzeng and Taesung Park and Jun-Yan Zhu and Phillip Isola and Kate Saenko and Alexei Efros and Trevor Darrell},
  booktitle={ICML},
  year={2018},
}

@inproceedings{chen2020automated,
  title={Automated synthetic-to-real generalization},
  author={Chen, Wuyang and Yu, Zhiding and Wang, Zhangyang and Anandkumar, Animashree},
  booktitle={ICML},
  year={2020},
}

@inproceedings{chen2021contrastive,
title={Contrastive Syn-to-Real Generalization},
author={Wuyang Chen and Zhiding Yu and Shalini De Mello and Sifei Liu and Jose M. Alvarez and Zhangyang Wang and Anima Anandkumar},
booktitle={ICLR},
year={2021},
}

@inproceedings{gan2016learning,
  title={Learning Attributes Equals Multi-Source Domain Generalization},
  author={Chuang Gan and Tianbao Yang and Boqing Gong},
  booktitle={CVPR},
  year={2016},
}

@inproceedings{niu2015multiview,
  title={Multi-View Domain Generalization for Visual Recognition},
  author={Li Niu and Wen Li and Dong Xu},
  booktitle={ICCV},
  year={2015},
}

@inproceedings{baluja2018learning,
  title={Learning to Attack: Adversarial Transformation Networks},
  author={Shumeet Baluja and Ian Fischer},
  booktitle={AAAI},
  year={2018},
}

@inproceedings{qian2018pose,
  author = {Qian, Xuelin and Fu, Yanwei and Xiang, Tao and Wang, Wenxuan and Qiu, Jie and Wu, Yang and Jiang, Yu-Gang and Xue, Xiangyang},
  title = {Pose-Normalized Image Generation for Person Re-identification},
  booktitle = {ECCV},
  year = {2018},
}

@InProceedings{li2017diversified,
author = {Li, Yijun and Fang, Chen and Yang, Jimei and Wang, Zhaowen and Lu, Xin and Yang, Ming-Hsuan},
title = {Diversified Texture Synthesis With Feed-Forward Networks},
booktitle = {CVPR},
year = {2017}
}

@inproceedings{iccv17cyclegan,
  title={Unpaired Image-to-Image Translation using Cycle-Consistent Adversarial Networks},
  author={Zhu, Jun-Yan and Park, Taesung and Isola, Phillip and Efros, Alexei A},
  booktitle={ICCV},
  year={2017}
}

@InProceedings{StarGAN,
author = {Choi, Yunjey and Choi, Minje and Kim, Munyoung and Ha, Jung-Woo and Kim, Sunghun and Choo, Jaegul},
title = {StarGAN: Unified Generative Adversarial Networks for Multi-Domain Image-to-Image Translation},
booktitle = {CVPR},
year = {2018}
}

@inproceedings{hoffman2018algorithms,
  title={Algorithms and theory for multiple-source adaptation},
  author={Hoffman, Judy and Mohri, Mehryar and Zhang, Ningshan},
  booktitle={NeurIPS},
  year={2018}
}

@inproceedings{wang2020cross,
  title={Cross-domain face presentation attack detection via multi-domain disentangled representation learning},
  author={Wang, Guoqing and Han, Hu and Shan, Shiguang and Chen, Xilin},
  booktitle={CVPR},
  year={2020}
}

@inproceedings{DiscoGAN,
  title={Learning to discover cross-domain relations with generative adversarial networks},
  author={Kim, Taeksoo and Cha, Moonsu and Kim, Hyunsoo and Lee, Jung Kwon and Kim, Jiwon},
  booktitle={ICML},
  year={2017},
}

@inproceedings{dualGAN,
  title={Dualgan: Unsupervised dual learning for image-to-image translation},
  author={Yi, Zili and Zhang, Hao and Tan, Ping and Gong, Minglun},
  booktitle={ICCV},
  year={2017}
}

@inproceedings{bousmalis2017unsupervised,
  title={Unsupervised pixel-level domain adaptation with generative adversarial networks},
  author={Bousmalis, Konstantinos and Silberman, Nathan and Dohan, David and Erhan, Dumitru and Krishnan, Dilip},
  booktitle={CVPR},
  year={2017}
}

@inproceedings{pix2pix,
  title={Image-to-Image Translation with Conditional Adversarial Networks},
  author={Isola, Phillip and Zhu, Jun-Yan and Zhou, Tinghui and Efros, Alexei A},
  booktitle={CVPR},
  year={2017}
}

@inproceedings{xie2019snas,
  title={SNAS: stochastic neural architecture search},
  author={Xie, Sirui and Zheng, Hehui and Liu, Chunxiao and Lin, Liang},
  booktitle={ICLR},
  year={2019}
}

@inproceedings{liu2019darts,
  title={Darts: Differentiable architecture search},
  author={Liu, Hanxiao and Simonyan, Karen and Yang, Yiming},
  booktitle={ICLR},
  year={2019}
}

@article{ulyanov2016instance,
  title={Instance Normalization: The Missing Ingredient for Fast Stylization},
  author={Dmitry Ulyanov and Andrea Vedaldi and Victor Lempitsky},
  journal={arXiv:1607.08022},
  year={2016}
}

@inproceedings{dumoulin2017learned,
  title={A learned representation for artistic style},
  author={Dumoulin, Vincent and Shlens, Jonathon and Kudlur, Manjunath},
  booktitle={ICLR},
  year={2017}
}

@inproceedings{zhao2017pyramid,
  title={Pyramid scene parsing network},
  author={Zhao, Hengshuang and Shi, Jianping and Qi, Xiaojuan and Wang, Xiaogang and Jia, Jiaya},
  booktitle={CVPR},
  year={2017}
}

@inproceedings{lu2020stochastic,
  title={Stochastic classifiers for unsupervised domain adaptation},
  author={Lu, Zhihe and Yang, Yongxin and Zhu, Xiatian and Liu, Cong and Song, Yi-Zhe and Xiang, Tao},
  booktitle={CVPR},
  year={2020}
}

@inproceedings{kingma2014adam,
  title={Adam: A method for stochastic optimization},
  author={Kingma, Diederik P and Ba, Jimmy},
  booktitle={ICLR},
  year={2014}
}

@inproceedings{simonyan2013deep,
  title={Deep inside convolutional networks: Visualising image classification models and saliency maps},
  author={Simonyan, Karen and Vedaldi, Andrea and Zisserman, Andrew},
  booktitle={ICLR-W},
  year={2014}
}

@inproceedings{cariucci2017autodial,
  title={Autodial: Automatic domain alignment layers},
  author={Cariucci, Fabio Maria and Porzi, Lorenzo and Caputo, Barbara and Ricci, Elisa and Bulo, Samuel Rota},
  booktitle={ICCV},
  year={2017}
}

@inproceedings{paszke2017pytorch,
  title={Automatic differentiation in PyTorch},
  author={Paszke, Adam and Gross, Sam and Chintala, Soumith and Chanan, Gregory and Yang, Edward and DeVito, Zachary and Lin, Zeming and Desmaison, Alban and Antiga, Luca and Lerer, Adam},
  booktitle={NeurIPS-W},
  year={2017}
}

@inproceedings{liu2016parsenet,
  title={ParseNet: Looking Wider to See Better},
  author={Wei Liu and Andrew Rabinovich and Alexander C. Berg},
  booktitle={ICLR-W},
  year={2016},
}

@inproceedings{zhu2018hidden,
  title={HiDDeN: Hiding Data With Deep Networks},
  author={Jiren Zhu and Russell Kaplan and Justin Johnson and Li Fei-Fei},
  booktitle={ECCV},
  year={2018},
}

@article{mnih2013playing,
  title={Playing atari with deep reinforcement learning},
  author={Mnih, Volodymyr and Kavukcuoglu, Koray and Silver, David and Graves, Alex and Antonoglou, Ioannis and Wierstra, Daan and Riedmiller, Martin},
  journal={arXiv preprint arXiv:1312.5602},
  year={2013}
}

@inproceedings{fang2013unbiased,
  title={Unbiased metric learning: On the utilization of multiple datasets and web images for softening bias},
  author={Fang, Chen and Xu, Ye and Rockmore, Daniel N},
  booktitle={ICCV},
  year={2013}
}

@inproceedings{hendrycks2019benchmarking,
  title={Benchmarking neural network robustness to common corruptions and perturbations},
  author={Hendrycks, Dan and Dietterich, Thomas},
  booktitle={ICLR},
  year={2019}
}

@inproceedings{ros2016synthia,
  title={The synthia dataset: A large collection of synthetic images for semantic segmentation of urban scenes},
  author={Ros, German and Sellart, Laura and Materzynska, Joanna and Vazquez, David and Lopez, Antonio M},
  booktitle={CVPR},
  year={2016}
}

@article{soomro2012ucf101,
  title={UCF101: A dataset of 101 human actions classes from videos in the wild},
  author={Soomro, Khurram and Zamir, Amir Roshan and Shah, Mubarak},
  journal={arXiv preprint arXiv:1212.0402},
  year={2012}
}

@inproceedings{netzer2011svhn,
  title={Reading Digits in Natural Images with Unsupervised Feature Learning},
  author={Yuval Netzer and Tao Wang and Adam Coates and Alessandro Bissacco and Bo Wu and Andrew Y. Ng},
  booktitle={NeurIPS-W},
  year={2011},
}

@inproceedings{zeiler2014visualizing,
  title={Visualizing and understanding convolutional networks},
  author={Zeiler, Matthew D and Fergus, Rob},
  booktitle={ECCV},
  year={2014},
}

@article{tsne,
  title={Visualizing data using t-SNE},
  author={Maaten, Laurens van der and Hinton, Geoffrey},
  journal={JMLR},
  year={2008}
}

@inproceedings{lecun1998mnist,
  title={Gradient-based learning applied to document recognition},
  author={Yann LeCun and Leon Bottou and Yoshua Bengio and Patrick Haffner},
  booktitle={IEEE},
  year={1998},
}

@inproceedings{nam2018batch,
  title={Batch-instance normalization for adaptively style-invariant neural networks},
  author={Nam, Hyeonseob and Kim, Hyo-Eun},
  booktitle={NeurIPS},
  year={2018}
}

@article{choi2020meta,
  title={Meta Batch-Instance Normalization for Generalizable Person Re-Identification},
  author={Choi, Seokeon and Kim, Taekyung and Jeong, Minki and Park, Hyoungseob and Kim, Changick},
  journal={arXiv preprint arXiv:2011.14670},
  year={2020}
}

@article{wilds2020,
  title = {WILDS: A Benchmark of in-the-Wild Distribution Shifts},
  author = {Pang Wei Koh and Shiori Sagawa and Henrik Marklund and Sang Michael Xie and Marvin Zhang and Akshay Balsubramani and Weihua Hu and Michihiro Yasunaga and Richard Lanas Phillips and Sara Beery and Jure Leskovec and Anshul Kundaje and Emma Pierson and Sergey Levine and Chelsea Finn and Percy Liang},
  journal = {arXiv preprint arXiv:2012.07421},
  year = {2020}
}

@inproceedings{zhao2021learning,
  title={Learning to Generalize Unseen Domains via Memory-based Multi-Source Meta-Learning for Person Re-Identification},
  author={Zhao, Yuyang and Zhong, Zhun and Yang, Fengxiang and Luo, Zhiming and Lin, Yaojin and Li, Shaozi and Sebe, Nicu},
  booktitle={CVPR},
  year={2021}
}

@inproceedings{dou2019domain,
  title={Domain Generalization via Model-Agnostic Learning of Semantic Features},
  author={Dou, Qi and Castro, Daniel C and Kamnitsas, Konstantinos and Glocker, Ben},
  booktitle={NeurIPS},
  year={2019}
}

@article{mahajan2020domain,
  title={Domain generalization using causal matching},
  author={Mahajan, Divyat and Tople, Shruti and Sharma, Amit},
  journal={arXiv preprint arXiv:2006.07500},
  year={2020}
}

@inproceedings{vinyals2016matching,
  title={Matching networks for one shot learning},
  author={Vinyals, Oriol and Blundell, Charles and Lillicrap, Timothy and Wierstra, Daan and others},
  booktitle={NeurIPS},
  year={2016}
}

@inproceedings{snell2017prototypical,
  title={Prototypical networks for few-shot learning},
  author={Snell, Jake and Swersky, Kevin and Zemel, Richard},
  booktitle={NeurIPS},
  year={2017}
}

@article{gretton2012kernel,
 author = {Gretton, Arthur and Borgwardt, Karsten M. and Rasch, Malte J. and Sch\"{o}lkopf, Bernhard and Smola, Alexander},
 title = {A Kernel Two-sample Test},
 journal = {JMLR},
 year = {2012},
} 

@inproceedings{geirhos2019imagenettrained,
title={ImageNet-trained {CNN}s are biased towards texture; increasing shape bias improves accuracy and robustness.},
author={Robert Geirhos and Patricia Rubisch and Claudio Michaelis and Matthias Bethge and Felix A. Wichmann and Wieland Brendel},
booktitle={ICLR},
year={2019},
}

@article{lake2015human,
  title={Human-level concept learning through probabilistic program induction},
  author={Lake, Brenden M and Salakhutdinov, Ruslan and Tenenbaum, Joshua B},
  journal={Science},
  year={2015},
}

@inproceedings{deng2009imagenet,
  title={Imagenet: A large-scale hierarchical image database},
  author={Deng, Jia and Dong, Wei and Socher, Richard and Li, Li-Jia and Li, Kai and Fei-Fei, Li},
  booktitle={CVPR},
  year={2009},
}

@inproceedings{li2015generative,
 author = {Li, Yujia and Swersky, Kevin and Zemel, Richard},
 title = {Generative Moment Matching Networks},
 booktitle = {ICML},
 year = {2015}
} 

@inproceedings{dziugaite2015training,
 author = {Dziugaite, Gintare Karolina and Roy, Daniel M. and Ghahramani, Zoubin},
 title = {Training Generative Neural Networks via Maximum Mean Discrepancy Optimization},
 booktitle = {UAI},
 year = {2015},
} 

@InProceedings{saito2018maximum,
author = {Saito, Kuniaki and Watanabe, Kohei and Ushiku, Yoshitaka and Harada, Tatsuya},
title = {Maximum Classifier Discrepancy for Unsupervised Domain Adaptation},
booktitle = {CVPR},
year = {2018}
}

@article{tang2021selfnorm,
  title={SelfNorm and CrossNorm for Out-of-Distribution Robustness},
  author={Tang, Zhiqiang and Gao, Yunhe and Zhu, Yi and Zhang, Zhi and Li, Mu and Metaxas, Dimitris},
  journal={arXiv preprint arXiv:2102.02811},
  year={2021}
}

@inproceedings{huang2020self,
  title={Self-challenging improves cross-domain generalization},
  author={Huang, Zeyi and Wang, Haohan and Xing, Eric P and Huang, Dong},
  booktitle={ECCV},
  year={2020},
}

@inproceedings{fan2021adversarially,
  title={Adversarially Adaptive Normalization for Single Domain Generalization},
  author={Fan, Xinjie and Wang, Qifei and Ke, Junjie and Yang, Feng and Gong, Boqing and Zhou, Mingyuan},
  booktitle={CVPR},
  year={2021}
}

@article{sharifi2020domain,
  title={Domain generalization via semi-supervised meta learning},
  author={Sharifi-Noghabi, Hossein and Asghari, Hossein and Mehrasa, Nazanin and Ester, Martin},
  journal={arXiv preprint arXiv:2009.12658},
  year={2020}
}

@article{zhou2021mixstylenn,
  title={MixStyle Neural Networks for Domain Generalization and Adaptation},
  author={Zhou, Kaiyang and Yang, Yongxin and Qiao, Yu and Xiang, Tao},
  journal={arXiv:2107.02053},
  year={2021}
}

@inproceedings{iclr18SE,
title={Self-ensembling for visual domain adaptation},
author={Geoff French and Michal Mackiewicz and Mark Fisher},
booktitle={ICLR},
year={2018}
}

@article{zhou2021stylematch,
    title={Semi-Supervised Domain Generalization with Stochastic StyleMatch},
    author={Zhou, Kaiyang and Loy, Chen Change and Liu, Ziwei},
    journal={arXiv preprint arXiv:2106.00592},
    year={2021}
}

@inproceedings{tarvainen2017mean,
  title={Mean teachers are better role models: Weight-averaged consistency targets improve semi-supervised deep learning results},
  author={Tarvainen, Antti and Valpola, Harri},
  booktitle={NeurIPS},
  year={2017}
}

@inproceedings{niu2015visual,
  title={Visual recognition by learning from web data: A weakly supervised domain generalization approach},
  author={Niu, Li and Li, Wen and Xu, Dong},
  booktitle={CVPR},
  year={2015}
}

@inproceedings{aslani2020scanner,
  title={Scanner invariant multiple sclerosis lesion segmentation from MRI},
  author={Aslani, Shahab and Murino, Vittorio and Dayan, Michael and Tam, Roger and Sona, Diego and Hamarneh, Ghassan},
  booktitle={ISBI},
  year={2020}
}

@article{wang2020respecting,
  title={Respecting Domain Relations: Hypothesis Invariance for Domain Generalization},
  author={Wang, Ziqi and Loog, Marco and van Gemert, Jan},
  journal={arXiv preprint arXiv:2010.07591},
  year={2020}
}

@inproceedings{laine2016temporal,
  title={Temporal ensembling for semi-supervised learning},
  author={Laine, Samuli and Aila, Timo},
  booktitle={ICLR},
  year={2017}
}

@article{peng2017visda,
  title={Visda: The visual domain adaptation challenge},
  author={Peng, Xingchao and Usman, Ben and Kaushik, Neela and Hoffman, Judy and Wang, Dequan and Saenko, Kate},
  journal={arXiv preprint arXiv:1710.06924},
  year={2017}
}

@inproceedings{loshchilov2016sgdr,
  title={Sgdr: Stochastic gradient descent with warm restarts},
  author={Loshchilov, Ilya and Hutter, Frank},
  booktitle={ICLR},
  year={2017}
}



@ARTICLE{DA_Patel_SPM15,
	author={V. M. {Patel} and R. {Gopalan} and R. {Li} and R. {Chellappa}},
	journal={IEEE Signal Processing Magazine}, 
	title={Visual Domain Adaptation: A survey of recent advances}, 
	year={2015},
	volume={32},
	number={3},
	pages={53-69}}


@inproceedings{Sindagi_DA_Detection_ECCV2020,
    title={Prior-based domain adaptive object detection for hazy and rainy conditions},
    author={V. A. Sindagi and P. Oza nad R. Yasarla and V. M. Patel},
    booktitle={European Conference on Computer Vision (ECCV)},
    pages={},
    year={2020}
}


@article{vs2021mega,
	title={MeGA-CDA: Memory Guided Attention for Category-Aware Unsupervised Domain Adaptive Object Detection},
	author={VS, Vibashan and Oza, Poojan and Sindagi, Vishwanath A and Gupta, Vikram and Patel, Vishal M},
    booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	year={2021}
}







@inproceedings{zhang2019few,
  title={Few-shot structured domain adaptation for virtual-to-real scene parsing},
  author={Zhang, Junyi and Chen, Ziliang and Huang, Junying and Lin, Liang and Zhang, Dongyu},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision Workshops},
  pages={0--0},
  year={2019}
}


@inproceedings{motiian2017few,
  title={Few-Shot Adversarial Domain Adaptation},
  author={Motiian, Saeid and Jones, Quinn and Iranmanesh, Seyed Mehdi and Doretto, Gianfranco},
  booktitle={NIPS},
  year={2017}
}


@inproceedings{zhang2019few,
  title={Few-shot domain adaptation for semantic segmentation},
  author={Zhang, Junyi and Chen, Ziliang and Huang, Junying and Zhuang, Jingyu and Zhang, Dongyu},
  booktitle={Proceedings of the ACM Turing Celebration Conference-China},
  pages={1--6},
  year={2019}
}


@inproceedings{shu2018dirt,
  title={A DIRT-T APPROACH TO UNSUPERVISED DOMAIN ADAPTATION},
  author={Shu, Rui and Bui, Hung H and Narui, Hirokazu and Ermon, Stefano},
  booktitle={Proc. 6th International Conference on Learning Representations},
  year={2018}
}


@article{laine2016temporal,
  title={Temporal Ensembling for Semi-Supervised Learning},
  author={Laine, Samuli and Aila, Timo},
  year={2016}
}


@inproceedings{deng2019cluster,
  title={Cluster alignment with a teacher for unsupervised domain adaptation},
  author={Deng, Zhijie and Luo, Yucen and Zhu, Jun},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={9944--9953},
  year={2019}
}


@inproceedings{french2018self,
  title={Self-ensembling for visual domain adaptation},
  author={French, Geoffrey and Mackiewicz, Michal and Fisher, Mark},
  booktitle={International Conference on Learning Representations},
  number={6},
  year={2018}
}


@article{sohn2020fixmatch,
  title={FixMatch: Simplifying Semi-Supervised Learning with Consistency and Confidence},
  author={Sohn, Kihyuk and Berthelot, David and Carlini, Nicholas and Zhang, Zizhao and Zhang, Han and Raffel, Colin A and Cubuk, Ekin Dogus and Kurakin, Alexey and Li, Chun-Liang},
  journal={Advances in Neural Information Processing Systems},
  volume={33},
  year={2020}
}


@article{berthelot2019mixmatch,
  title={Mixmatch: A holistic approach to semi-supervised learning},
  author={Berthelot, David and Carlini, Nicholas and Goodfellow, Ian and Papernot, Nicolas and Oliver, Avital and Raffel, Colin},
  journal={arXiv preprint arXiv:1905.02249},
  year={2019}
}



@article{liuunbiased,
  title={UNBIASED TEACHER FOR SEMI-SUPERVISED OBJECT DETECTION},
  author={Liu, Yen-Cheng and Ma, Chih-Yao and He, Zijian and Kuo, Chia-Wen and Chen, Kan and Zhang, Peizhao and Wu, Bichen and Kira, Zsolt and Vajda, Peter},
  journal={International Conference on Learning Representations},
  year={2021}
}


@inproceedings{tarvainen2017mean,
  title={Mean teachers are better role models: Weight-averaged consistency targets improve semi-supervised deep learning results},
  author={Tarvainen, Antti and Valpola, Harri},
  booktitle={Proceedings of the 31st International Conference on Neural Information Processing Systems},
  pages={1195--1204},
  year={2017}
}


@inproceedings{gupta2016cross,
  title={Cross modal distillation for supervision transfer},
  author={Gupta, Saurabh and Hoffman, Judy and Malik, Jitendra},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={2827--2836},
  year={2016}
}


@article{hinton2015distilling,
  title={Distilling the knowledge in a neural network},
  author={Hinton, Geoffrey and Vinyals, Oriol and Dean, Jeff},
  journal={arXiv preprint arXiv:1503.02531},
  year={2015}
}


@article{kipf2016semi,
  title={Semi-Supervised Classification with Graph Convolutional Networks},
  author={Kipf, Thomas N and Welling, Max},
  year={2016}
}


@inproceedings{ma2019gcan,
  title={Gcan: Graph convolutional adversarial network for unsupervised domain adaptation},
  author={Ma, Xinhong and Zhang, Tianzhu and Xu, Changsheng},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={8266--8276},
  year={2019}
}


@inproceedings{mancini2019adagraph,
  title={Adagraph: Unifying predictive and continuous domain adaptation through graphs},
  author={Mancini, Massimiliano and Bulo, Samuel Rota and Caputo, Barbara and Ricci, Elisa},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={6568--6577},
  year={2019}
}


@article{yan2019fine,
  title={Fine-grained video captioning via graph-based multi-granularity interaction learning},
  author={Yan, Yichao and Zhuang, Ning and Zhang, Jian and Xu, Minghao and Zhang, Qiang and ZHENG, ZHANG and Cheng, Shuo and Tian, Qi and Yang, Xiaokang and Zhang, Wenjun and others},
  journal={IEEE transactions on pattern analysis and machine intelligence},
  year={2019},
  publisher={IEEE}
}


@inproceedings{yan2018spatial,
  title={Spatial temporal graph convolutional networks for skeleton-based action recognition},
  author={Yan, Sijie and Xiong, Yuanjun and Lin, Dahua},
  booktitle={Proceedings of the AAAI conference on artificial intelligence},
  volume={32},
  number={1},
  year={2018}
}

@inproceedings{yan2019learning,
  title={Learning context graph for person search},
  author={Yan, Yichao and Zhang, Qiang and Ni, Bingbing and Zhang, Wendong and Xu, Minghao and Yang, Xiaokang},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2158--2167},
  year={2019}
}




@inproceedings{liu2017unsupervised,
  title={Unsupervised image-to-image translation networks},
  author={Liu, Ming-Yu and Breuel, Thomas and Kautz, Jan},
  booktitle={Proceedings of the 31st International Conference on Neural Information Processing Systems},
  pages={700--708},
  year={2017}
}


@inproceedings{huang2018multimodal,
  title={Multimodal unsupervised image-to-image translation},
  author={Huang, Xun and Liu, Ming-Yu and Belongie, Serge and Kautz, Jan},
  booktitle={Proceedings of the European conference on computer vision (ECCV)},
  pages={172--189},
  year={2018}
}

@inproceedings{yi2017dualgan,
  title={Dualgan: Unsupervised dual learning for image-to-image translation},
  author={Yi, Zili and Zhang, Hao and Tan, Ping and Gong, Minglun},
  booktitle={Proceedings of the IEEE international conference on computer vision},
  pages={2849--2857},
  year={2017}
}


@article{belghazi2018mine,
  title={MINE: Mutual Information Neural Estimation},
  author={Belghazi, Mohamed Ishmael and Rajeswar, Sai and Baratin, Aristide and Hjelm, Devon and Courville, Aaron},
  year={2018}
}


@inproceedings{zhu2017unpaired,
	title={Unpaired image-to-image translation using cycle-consistent adversarial networks},
	author={Zhu, Jun-Yan and Park, Taesung and Isola, Phillip and Efros, Alexei A},
	booktitle={Proceedings of the IEEE international conference on computer vision},
	pages={2223--2232},
	year={2017}
}


@inproceedings{goodfellow2014generative,
	title={Generative adversarial nets},
	author={Goodfellow, Ian J and Pouget-Abadie, Jean and Mirza, Mehdi and Xu, Bing and Warde-Farley, David and Ozair, Sherjil and Courville, Aaron and Bengio, Yoshua},
	booktitle={Proceedings of the 27th International Conference on Neural Information Processing Systems-Volume 2},
	pages={2672--2680},
	year={2014}
}


@inproceedings{lin2017focal,
	title={Focal loss for dense object detection},
	author={Lin, Tsung-Yi and Goyal, Priya and Girshick, Ross and He, Kaiming and Doll{\'a}r, Piotr},
	booktitle={Proceedings of the IEEE international conference on computer vision},
	pages={2980--2988},
	year={2017}
}





@article{liu2020deep,
	title={Deep learning for generic object detection: A survey},
	author={Liu, Li and Ouyang, Wanli and Wang, Xiaogang and Fieguth, Paul and Chen, Jie and Liu, Xinwang and Pietik{\"a}inen, Matti},
	journal={International journal of computer vision},
	volume={128},
	number={2},
	pages={261--318},
	year={2020},
	publisher={Springer}
}


@article{zhao2019object,
	title={Object detection with deep learning: A review},
	author={Zhao, Zhong-Qiu and Zheng, Peng and Xu, Shou-tao and Wu, Xindong},
	journal={IEEE transactions on neural networks and learning systems},
	volume={30},
	number={11},
	pages={3212--3232},
	year={2019},
	publisher={IEEE}
}


@article{zou2019object,
	title={Object detection in 20 years: A survey},
	author={Zou, Zhengxia and Shi, Zhenwei and Guo, Yuhong and Ye, Jieping},
	journal={arXiv preprint arXiv:1905.05055},
	year={2019}
}





@article{wang2018deep,
	title={Deep visual domain adaptation: A survey},
	author={Wang, Mei and Deng, Weihong},
	journal={Neurocomputing},
	volume={312},
	pages={135--153},
	year={2018},
	publisher={Elsevier}
}


@article{toldo2020unsupervised,
	title={Unsupervised domain adaptation in semantic segmentation: a review},
	author={Toldo, Marco and Maracani, Andrea and Michieli, Umberto and Zanuttigh, Pietro},
	journal={Technologies},
	volume={8},
	number={2},
	pages={35},
	year={2020},
	publisher={Multidisciplinary Digital Publishing Institute}
}


@article{zhao2020review,
	title={A Review of Single-Source Deep Unsupervised Visual Domain Adaptation},
	author={Zhao, Sicheng and Yue, Xiangyu and Zhang, Shanghang and Li, Bo and Zhao, Han and Wu, Bichen and Krishna, Ravi and Gonzalez, Joseph E and Sangiovanni-Vincentelli, Alberto L and Seshia, Sanjit A and others},
	journal={IEEE Transactions on Neural Networks and Learning Systems},
	year={2020},
	publisher={IEEE}
}


@article{zhuang2020comprehensive,
	title={A comprehensive survey on transfer learning},
	author={Zhuang, Fuzhen and Qi, Zhiyuan and Duan, Keyu and Xi, Dongbo and Zhu, Yongchun and Zhu, Hengshu and Xiong, Hui and He, Qing},
	journal={Proceedings of the IEEE},
	volume={109},
	number={1},
	pages={43--76},
	year={2020},
	publisher={IEEE}
}


@inproceedings{redmon2016you,
	title={You only look once: Unified, real-time object detection},
	author={Redmon, Joseph and Divvala, Santosh and Girshick, Ross and Farhadi, Ali},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	pages={779--788},
	year={2016}
}


@inproceedings{liu2016ssd,
	title={Ssd: Single shot multibox detector},
	author={Liu, Wei and Anguelov, Dragomir and Erhan, Dumitru and Szegedy, Christian and Reed, Scott and Fu, Cheng-Yang and Berg, Alexander C},
	booktitle={European conference on computer vision},
	pages={21--37},
	year={2016},
	organization={Springer}
}


@inproceedings{tarvainen2017mean,
	title={Mean teachers are better role models: Weight-averaged consistency targets improve semi-supervised deep learning results},
	author={Tarvainen, Antti and Valpola, Harri},
	booktitle={Advances in neural information processing systems},
	pages={1195--1204},
	year={2017}
}



@inproceedings{vaswani2017attention,
	title={Attention is all you need},
	author={Vaswani, Ashish and Shazeer, Noam and Parmar, Niki and Uszkoreit, Jakob and Jones, Llion and Gomez, Aidan N and Kaiser, {\L}ukasz and Polosukhin, Illia},
	booktitle={Advances in neural information processing systems},
	pages={5998--6008},
	year={2017}
}


@inproceedings{devlin2019bert,
	title={BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding},
	author={Devlin, Jacob and Chang, Ming-Wei and Lee, Kenton and Toutanova, Kristina},
	booktitle={Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long and Short Papers)},
	pages={4171--4186},
	year={2019}
}



@article{brown2020language,
	title={Language models are few-shot learners},
	author={Brown, Tom B and Mann, Benjamin and Ryder, Nick and Subbiah, Melanie and Kaplan, Jared and Dhariwal, Prafulla and Neelakantan, Arvind and Shyam, Pranav and Sastry, Girish and Askell, Amanda and others},
	journal={arXiv preprint arXiv:2005.14165},
	year={2020}
}


@article{mnih2013playing,
	title={Playing atari with deep reinforcement learning},
	author={Mnih, Volodymyr and Kavukcuoglu, Koray and Silver, David and Graves, Alex and Antonoglou, Ioannis and Wierstra, Daan and Riedmiller, Martin},
	journal={arXiv preprint arXiv:1312.5602},
	year={2013}
}


@article{everingham2010pascal,
	title={The pascal visual object classes (voc) challenge},
	author={Everingham, Mark and Van Gool, Luc and Williams, Christopher KI and Winn, John and Zisserman, Andrew},
	journal={International journal of computer vision},
	volume={88},
	number={2},
	pages={303--338},
	year={2010},
	publisher={Springer}
}


@inproceedings{lin2014microsoft,
	title={Microsoft coco: Common objects in context},
	author={Lin, Tsung-Yi and Maire, Michael and Belongie, Serge and Hays, James and Perona, Pietro and Ramanan, Deva and Doll{\'a}r, Piotr and Zitnick, C Lawrence},
	booktitle={European conference on computer vision},
	pages={740--755},
	year={2014},
	organization={Springer}
}

@inproceedings{shrivastava2014unsupervised,
  title={Unsupervised domain adaptation using parallel transport on Grassmann manifold},
  author={Shrivastava, Ashish and Shekhar, Sumit and Patel, Vishal M},
  booktitle={IEEE winter conference on applications of computer vision},
  pages={277--284},
  year={2014},
  organization={IEEE}
}


@article{schrittwieser2019mastering,
	title={Mastering atari, go, chess and shogi by planning with a learned model},
	author={Schrittwieser, Julian and Antonoglou, Ioannis and Hubert, Thomas and Simonyan, Karen and Sifre, Laurent and Schmitt, Simon and Guez, Arthur and Lockhart, Edward and Hassabis, Demis and Graepel, Thore and others},
	journal={arXiv preprint arXiv:1911.08265},
	year={2019}
}


@inproceedings{xiao2019thinking,
	title={Thinking While Moving: Deep Reinforcement Learning with Concurrent Control},
	author={Xiao, Ted and Jang, Eric and Kalashnikov, Dmitry and Levine, Sergey and Ibarz, Julian and Hausman, Karol and Herzog, Alexander},
	booktitle={International Conference on Learning Representations},
	year={2019}
}


@inproceedings{krizhevsky2012imagenet,
	title={Imagenet classification with deep convolutional neural networks},
	author={Krizhevsky, Alex and Sutskever, Ilya and Hinton, Geoffrey E},
	booktitle={Advances in neural information processing systems},
	pages={1097--1105},
	year={2012}
}



@article{lecun1998gradient,
	title={Gradient-based learning applied to document recognition},
	author={LeCun, Yann and Bottou, L{\'e}on and Bengio, Yoshua and Haffner, Patrick},
	journal={Proceedings of the IEEE},
	volume={86},
	number={11},
	pages={2278--2324},
	year={1998},
	publisher={Ieee}
}


@inproceedings{hu2018squeeze,
	title={Squeeze-and-excitation networks},
	author={Hu, Jie and Shen, Li and Sun, Gang},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	pages={7132--7141},
	year={2018}
}


@inproceedings{huang2017densely,
	title={Densely connected convolutional networks},
	author={Huang, Gao and Liu, Zhuang and Van Der Maaten, Laurens and Weinberger, Kilian Q},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	pages={4700--4708},
	year={2017}
}


@inproceedings{long2015fully,
	title={Fully convolutional networks for semantic segmentation},
	author={Long, Jonathan and Shelhamer, Evan and Darrell, Trevor},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	pages={3431--3440},
	year={2015}
}


@article{chen2017deeplab,
	title={Deeplab: Semantic image segmentation with deep convolutional nets, atrous convolution, and fully connected crfs},
	author={Chen, Liang-Chieh and Papandreou, George and Kokkinos, Iasonas and Murphy, Kevin and Yuille, Alan L},
	journal={IEEE transactions on pattern analysis and machine intelligence},
	volume={40},
	number={4},
	pages={834--848},
	year={2017},
	publisher={IEEE}
}


@inproceedings{zhao2017pyramid,
	title={Pyramid scene parsing network},
	author={Zhao, Hengshuang and Shi, Jianping and Qi, Xiaojuan and Wang, Xiaogang and Jia, Jiaya},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	pages={2881--2890},
	year={2017}
}


@inproceedings{redmon2016you,
	title={You only look once: Unified, real-time object detection},
	author={Redmon, Joseph and Divvala, Santosh and Girshick, Ross and Farhadi, Ali},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	pages={779--788},
	year={2016}
}


@inproceedings{liu2016ssd,
	title={Ssd: Single shot multibox detector},
	author={Liu, Wei and Anguelov, Dragomir and Erhan, Dumitru and Szegedy, Christian and Reed, Scott and Fu, Cheng-Yang and Berg, Alexander C},
	booktitle={European conference on computer vision},
	pages={21--37},
	year={2016},
	organization={Springer}
}


@inproceedings{deng2009imagenet,
	title={Imagenet: A large-scale hierarchical image database},
	author={Deng, Jia and Dong, Wei and Socher, Richard and Li, Li-Jia and Li, Kai and Fei-Fei, Li},
	booktitle={2009 IEEE conference on computer vision and pattern recognition},
	pages={248--255},
	year={2009},
	organization={Ieee}
}





@inproceedings{carion2020end,
	title={End-to-end object detection with transformers},
	author={Carion, Nicolas and Massa, Francisco and Synnaeve, Gabriel and Usunier, Nicolas and Kirillov, Alexander and Zagoruyko, Sergey},
	booktitle={European Conference on Computer Vision},
	pages={213--229},
	year={2020},
	organization={Springer}
}


@inproceedings{tian2019fcos,
	title={Fcos: Fully convolutional one-stage object detection},
	author={Tian, Zhi and Shen, Chunhua and Chen, Hao and He, Tong},
	booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
	pages={9627--9636},
	year={2019}
}







@inproceedings{daume2010frustratingly,
  title={Frustratingly easy semi-supervised domain adaptation},
  author={Daum{\'e} III, Hal and Kumar, Abhishek and Saha, Avishek},
  booktitle={Proceedings of the 2010 Workshop on Domain Adaptation for Natural Language Processing},
  pages={53--59},
  year={2010}
}

@inproceedings{saito2019semi,
  title={Semi-supervised domain adaptation via minimax entropy},
  author={Saito, Kuniaki and Kim, Donghyun and Sclaroff, Stan and Darrell, Trevor and Saenko, Kate},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={8050--8058},
  year={2019}
}

@inproceedings{xu2019d,
  title={d-sne: Domain adaptation using stochastic neighborhood embedding},
  author={Xu, Xiang and Zhou, Xiong and Venkatesan, Ragav and Swaminathan, Gurumurthy and Majumder, Orchid},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2497--2506},
  year={2019}
}


@inproceedings{sun2016return,
	title={Return of frustratingly easy domain adaptation},
	author={Sun, Baochen and Feng, Jiashi and Saenko, Kate},
	booktitle={Thirtieth AAAI Conference on Artificial Intelligence},
	year={2016}
}




@inproceedings{tzeng2017adversarial,
	title={Adversarial discriminative domain adaptation},
	author={Tzeng, Eric and Hoffman, Judy and Saenko, Kate and Darrell, Trevor},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	pages={7167--7176},
	year={2017}
}



@inproceedings{saito2017asymmetric,
	title={Asymmetric tri-training for unsupervised domain adaptation},
	author={Saito, Kuniaki and Ushiku, Yoshitaka and Harada, Tatsuya},
	booktitle={Proceedings of the 34th International Conference on Machine Learning-Volume 70},
	pages={2988--2997},
	year={2017},
	organization={JMLR. org}
}




@inproceedings{hoffman2018cycada,
	title={CyCADA: Cycle-Consistent Adversarial Domain Adaptation},
	author={Hoffman, Judy and Tzeng, Eric and Park, Taesung and Zhu, Jun-Yan and Isola, Phillip and Saenko, Kate and Efros, Alexei and Darrell, Trevor},
	booktitle={International Conference on Machine Learning},
	pages={1989--1998},
	year={2018}
}


@inproceedings{lee2019sliced,
	title={Sliced wasserstein discrepancy for unsupervised domain adaptation},
	author={Lee, Chen-Yu and Batra, Tanmay and Baig, Mohammad Haris and Ulbricht, Daniel},
	booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
	pages={10285--10295},
	year={2019}
}


@inproceedings{chang2019domain,
	title={Domain-specific batch normalization for unsupervised domain adaptation},
	author={Chang, Woong-Gi and You, Tackgeun and Seo, Seonguk and Kwak, Suha and Han, Bohyung},
	booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
	pages={7354--7362},
	year={2019}
}


@inproceedings{roy2019unsupervised,
	title={Unsupervised domain adaptation using feature-whitening and consensus loss},
	author={Roy, Subhankar and Siarohin, Aliaksandr and Sangineto, Enver and Bulo, Samuel Rota and Sebe, Nicu and Ricci, Elisa},
	booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
	pages={9471--9480},
	year={2019}
}


@inproceedings{long2016unsupervised,
	title={Unsupervised Domain Adaptation with Residual Transfer Networks},
	author={Long, Mingsheng and Zhu, Han and Wang, Jianmin and Jordan, Michael I},
	booktitle={NIPS},
	year={2016}
}


@inproceedings{saito2018adversarial,
	title={Adversarial Dropout Regularization},
	author={Saito, Kuniaki and Ushiku, Yoshitaka and Harada, Tatsuya and Saenko, Kate},
	booktitle={International Conference on Learning Representations},
	year={2018}
}


@inproceedings{lee2019drop,
	title={Drop to adapt: Learning discriminative features for unsupervised domain adaptation},
	author={Lee, Seungmin and Kim, Dongwan and Kim, Namil and Jeong, Seong-Gyun},
	booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
	pages={91--100},
	year={2019}
}


@inproceedings{murez2018image,
	title={Image to image translation for domain adaptation},
	author={Murez, Zak and Kolouri, Soheil and Kriegman, David and Ramamoorthi, Ravi and Kim, Kyungnam},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={4500--4509},
	year={2018}
}

@inproceedings{sankaranarayanan2018generate,
	title={Generate to adapt: Aligning domains using generative adversarial networks},
	author={Sankaranarayanan, Swami and Balaji, Yogesh and Castillo, Carlos D and Chellappa, Rama},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={8503--8512},
	year={2018}
}


@article{sun2019unsupervised,
	title={Unsupervised Domain Adaptation through Self-Supervision},
	author={Sun, Yu and Tzeng, Eric and Darrell, Trevor and Efros, Alexei A},
	year={2019}
}


@inproceedings{volpi2018adversarial,
	title={Adversarial feature augmentation for unsupervised domain adaptation},
	author={Volpi, Riccardo and Morerio, Pietro and Savarese, Silvio and Murino, Vittorio},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={5495--5504},
	year={2018}
}


@inproceedings{hu2018duplex,
	title={Duplex generative adversarial network for unsupervised domain adaptation},
	author={Hu, Lanqing and Kan, Meina and Shan, Shiguang and Chen, Xilin},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={1498--1507},
	year={2018}
}


@inproceedings{kurmi2019attending,
	title={Attending to discriminative certainty for domain adaptation},
	author={Kurmi, Vinod Kumar and Kumar, Shanu and Namboodiri, Vinay P},
	booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
	pages={491--500},
	year={2019}
}






@inproceedings{chen2017no,
  title={No more discrimination: Cross city adaptation of road scene segmenters},
  author={Chen, Yi-Hsin and Chen, Wei-Yu and Chen, Yu-Ting and Tsai, Bo-Cheng and Frank Wang, Yu-Chiang and Sun, Min},
  booktitle={Proceedings of the IEEE International Conference on Computer Vision},
  pages={1992--2001},
  year={2017}
}


@article{hoffman2016fcns,
	title={Fcns in the wild: Pixel-level adversarial and constraint-based adaptation},
	author={Hoffman, Judy and Wang, Dequan and Yu, Fisher and Darrell, Trevor},
	journal={arXiv preprint arXiv:1612.02649},
	year={2016}
}


@inproceedings{zou2018unsupervised,
	title={Unsupervised domain adaptation for semantic segmentation via class-balanced self-training},
	author={Zou, Yang and Yu, Zhiding and Kumar, BVK and Wang, Jinsong},
	booktitle={Proceedings of the European conference on computer vision (ECCV)},
	pages={289--305},
	year={2018}
}


@inproceedings{tsai2018learning,
	title={Learning to adapt structured output space for semantic segmentation},
	author={Tsai, Yi-Hsuan and Hung, Wei-Chih and Schulter, Samuel and Sohn, Kihyuk and Yang, Ming-Hsuan and Chandraker, Manmohan},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={7472--7481},
	year={2018}
}


@inproceedings{sankaranarayanan2018learning,
	title={Learning from synthetic data: Addressing domain shift for semantic segmentation},
	author={Sankaranarayanan, Swami and Balaji, Yogesh and Jain, Arpit and Nam Lim, Ser and Chellappa, Rama},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={3752--3761},
	year={2018}
}


@inproceedings{hong2018conditional,
	title={Conditional generative adversarial network for structured domain adaptation},
	author={Hong, Weixiang and Wang, Zhenzhen and Yang, Ming and Yuan, Junsong},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={1335--1344},
	year={2018}
}


@inproceedings{zou2018unsupervised,
	title={Unsupervised domain adaptation for semantic segmentation via class-balanced self-training},
	author={Zou, Yang and Yu, Zhiding and Vijaya Kumar, BVK and Wang, Jinsong},
	booktitle={Proceedings of the European conference on computer vision (ECCV)},
	pages={289--305},
	year={2018}
}


@inproceedings{chen2018road,
	title={Road: Reality oriented adaptation for semantic segmentation of urban scenes},
	author={Chen, Yuhua and Li, Wen and Van Gool, Luc},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={7892--7901},
	year={2018}
}


@inproceedings{luo2019taking,
	title={Taking a closer look at domain shift: Category-level adversaries for semantics consistent domain adaptation},
	author={Luo, Yawei and Zheng, Liang and Guan, Tao and Yu, Junqing and Yang, Yi},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={2507--2516},
	year={2019}
}


@inproceedings{tsai2019domain,
	title={Domain adaptation for structured output via discriminative patch representations},
	author={Tsai, Yi-Hsuan and Sohn, Kihyuk and Schulter, Samuel and Chandraker, Manmohan},
	booktitle={Proceedings of the IEEE International Conference on Computer Vision},
	pages={1456--1465},
	year={2019}
}


@article{licontent,
	title={Content-Consistent Matching for Domain Adaptive Semantic Segmentation},
	author={Li, Guangrui and Kang, Guoliang and Liu, Wu and Wei, Yunchao and Yang, Yi},
	booktitle={Proceedings of the European Conference on Computer Vision},
	year={2020}
}


@inproceedings{tsai2019domain,
	title={Domain adaptation for structured output via discriminative patch representations},
	author={Tsai, Yi-Hsuan and Sohn, Kihyuk and Schulter, Samuel and Chandraker, Manmohan},
	booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
	pages={1456--1465},
	year={2019}
}

@inproceedings{du2019ssf,
	title={Ssf-dan: Separated semantic feature based domain adaptation network for semantic segmentation},
	author={Du, Liang and Tan, Jingang and Yang, Hongye and Feng, Jianfeng and Xue, Xiangyang and Zheng, Qibao and Ye, Xiaoqing and Zhang, Xiaolin},
	booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
	pages={982--991},
	year={2019}
}


@inproceedings{chang2019all,
	title={All about structure: Adapting structural information across domains for boosting semantic segmentation},
	author={Chang, Wei-Lun and Wang, Hui-Po and Peng, Wen-Hsiao and Chiu, Wei-Chen},
	booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
	pages={1900--1909},
	year={2019}
}


@inproceedings{li2019bidirectional,
	title={Bidirectional learning for domain adaptation of semantic segmentation},
	author={Li, Yunsheng and Yuan, Lu and Vasconcelos, Nuno},
	booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
	pages={6936--6945},
	year={2019}
}


@article{paul2020domain,
	title={Domain adaptive semantic segmentation using weak labels},
	author={Paul, Sujoy and Tsai, Yi-Hsuan and Schulter, Samuel and Roy-Chowdhury, Amit K and Chandraker, Manmohan},
	journal={arXiv preprint arXiv:2007.15176},
	year={2020}
}


@article{zhang2021prototypical,
	title={Prototypical Pseudo Label Denoising and Target Structure Learning for Domain Adaptive Semantic Segmentation},
	author={Zhang, Pan and Zhang, Bo and Zhang, Ting and Chen, Dong and Wang, Yong and Wen, Fang},
	journal={arXiv preprint arXiv:2101.10979},
	year={2021}
}




@inproceedings{chen2018domain,
	title={Domain adaptive faster r-cnn for object detection in the wild},
	author={Chen, Yuhua and Li, Wen and Sakaridis, Christos and Dai, Dengxin and Van Gool, Luc},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	pages={3339--3348},
	year={2018}
}


@inproceedings{saito2019strong,
	title={Strong-weak distribution alignment for adaptive object detection},
	author={Saito, Kuniaki and Ushiku, Yoshitaka and Harada, Tatsuya and Saenko, Kate},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={6956--6965},
	year={2019}
}


@inproceedings{inoue2018cross,
	title={Cross-domain weakly-supervised object detection through progressive domain adaptation},
	author={Inoue, Naoto and Furuta, Ryosuke and Yamasaki, Toshihiko and Aizawa, Kiyoharu},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	pages={5001--5009},
	year={2018}
}


@inproceedings{zhu2019adapting,
	title={Adapting object detectors via selective cross-domain alignment},
	author={Zhu, Xinge and Pang, Jiangmiao and Yang, Ceyuan and Shi, Jianping and Lin, Dahua},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={687--696},
	year={2019}
}


@inproceedings{kim2019diversify,
	title={Diversify and match: A domain adaptive representation learning paradigm for object detection},
	author={Kim, Taekyung and Jeong, Minki and Kim, Seunghyeon and Choi, Seokeon and Kim, Changick},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={12456--12465},
	year={2019}
}


@inproceedings{cai2019exploring,
	title={Exploring object relation in mean teacher for cross-domain detection},
	author={Cai, Qi and Pan, Yingwei and Ngo, Chong-Wah and Tian, Xinmei and Duan, Lingyu and Yao, Ting},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={11457--11466},
	year={2019}
}


@inproceedings{he2019multi,
	title={Multi-adversarial faster-rcnn for unrestricted object detection},
	author={He, Zhenwei and Zhang, Lei},
	booktitle={Proceedings of the IEEE International Conference on Computer Vision},
	pages={6668--6677},
	year={2019}
}


@inproceedings{roychowdhury2019automatic,
	title={Automatic adaptation of object detectors to new domains using self-training},
	author={RoyChowdhury, Aruni and Chakrabarty, Prithvijit and Singh, Ashish and Jin, SouYoung and Jiang, Huaizu and Cao, Liangliang and Learned-Miller, Erik},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={780--790},
	year={2019}
}


@inproceedings{khodabandeh2019robust,
	title={A robust learning approach to domain adaptive object detection},
	author={Khodabandeh, Mehran and Vahdat, Arash and Ranjbar, Mani and Macready, William G},
	booktitle={Proceedings of the IEEE International Conference on Computer Vision},
	pages={480--490},
	year={2019}
}


@inproceedings{wang2019few,
	title={Few-shot adaptive faster r-cnn},
	author={Wang, Tao and Zhang, Xiaopeng and Yuan, Li and Feng, Jiashi},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={7173--7182},
	year={2019}
}



@inproceedings{su2020active,
	title={Active adversarial domain adaptation},
	author={Su, Jong-Chyi and Tsai, Yi-Hsuan and Sohn, Kihyuk and Liu, Buyu and Maji, Subhransu and Chandraker, Manmohan},
	booktitle={The IEEE Winter Conference on Applications of Computer Vision},
	pages={739--748},
	year={2020}
}


@inproceedings{kim2019self,
	title={Self-training and adversarial background regularization for unsupervised domain adaptive one-stage object detection},
	author={Kim, Seunghyeon and Choi, Jaehoon and Kim, Taekyung and Kim, Changick},
	booktitle={Proceedings of the IEEE International Conference on Computer Vision},
	pages={6092--6101},
	year={2019}
}


@article{shan2019pixel,
	title={Pixel and feature level based domain adaptation for object detection in autonomous driving},
	author={Shan, Yuhu and Lu, Wen Feng and Chew, Chee Meng},
	journal={Neurocomputing},
	volume={367},
	pages={31--38},
	year={2019},
	publisher={Elsevier}
}


@inproceedings{xie2019multi,
	title={Multi-Level Domain Adaptive Learning for Cross-Domain Detection},
	author={Xie, Rongchang and Yu, Fei and Wang, Jiachao and Wang, Yizhou and Zhang, Li},
	booktitle={Proceedings of the IEEE International Conference on Computer Vision Workshops},
	pages={0--0},
	year={2019}
}


@article{xu2019adversarial,
	title={Adversarial adaptation from synthesis to reality in fast detector for smoke detection},
	author={Xu, Gao and Zhang, Qixing and Liu, Dongcai and Lin, Gaohua and Wang, Jinjun and Zhang, Yongming},
	journal={IEEE Access},
	volume={7},
	pages={29471--29483},
	year={2019},
	publisher={IEEE}
}


@inproceedings{hsu2020progressive,
	title={Progressive domain adaptation for object detection},
	author={Hsu, Han-Kai and Yao, Chun-Han and Tsai, Yi-Hsuan and Hung, Wei-Chih and Tseng, Hung-Yu and Singh, Maneesh and Yang, Ming-Hsuan},
	booktitle={The IEEE Winter Conference on Applications of Computer Vision},
	pages={749--757},
	year={2020}
}


@article{rodriguez2019domain,
	title={Domain adaptation for object detection via style consistency},
	author={Rodriguez, Adrian Lopez and Mikolajczyk, Krystian},
	journal={British Machine Vision Conference},
	year={2019}
}



@article{tzeng2018splat,
	title={SPLAT: semantic pixel-level adaptation transforms for detection},
	author={Tzeng, Eric and Burns, Kaylee and Saenko, Kate and Darrell, Trevor},
	journal={arXiv preprint arXiv:1812.00929},
	year={2018}
}


@inproceedings{arruda2019cross,
	title={Cross-domain car detection using unsupervised image-to-image translation: From day to night},
	author={Arruda, Vinicius F and Paix{\~a}o, Thiago M and Berriel, Rodrigo F and De Souza, Alberto F and Badue, Claudine and Sebe, Nicu and Oliveira-Santos, Thiago},
	booktitle={2019 International Joint Conference on Neural Networks (IJCNN)},
	pages={1--8},
	year={2019},
	organization={IEEE}
}


@inproceedings{zhuang2020ifan,
	title={ifan: Image-instance full alignment networks for adaptive object detection},
	author={Zhuang, Chenfan and Han, Xintong and Huang, Weilin and Scott, Matthew},
	booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
	volume={34},
	number={07},
	pages={13122--13129},
	year={2020}
}




@article{yu2019dalocnet,
	title={DALocNet: Improving localization accuracy for domain adaptive object detection},
	author={Yu, Yushan and Xu, Xuemiao and Hu, Xiaowei and Heng, Pheng-Ann},
	journal={IEEE Access},
	volume={7},
	pages={63155--63163},
	year={2019},
	publisher={IEEE}
}


@article{jiang2020blockchain,
	title={Blockchain-enabled cross-domain object detection for autonomous driving: A model sharing approach},
	author={Jiang, Xiantao and Yu, F Richard and Song, Tian and Ma, Zhaowei and Song, Yanxing and Zhu, Daqi},
	journal={IEEE Internet of Things Journal},
	volume={7},
	number={5},
	pages={3681--3692},
	year={2020},
	publisher={IEEE}
}


@inproceedings{guo2019domain,
	title={Domain-Adaptive Pedestrian Detection in Thermal Images},
	author={Guo, Tiantong and Huynh, Cong Phuoc and Solh, Mashhour},
	booktitle={2019 IEEE International Conference on Image Processing (ICIP)},
	pages={1660--1664},
	year={2019},
	organization={IEEE}
}




@article{yu2019unsupervised,
	title={Unsupervised Domain Adaptation for Object Detection via Cross-Domain Semi-Supervised Learning},
	author={Yu, Fuxun and Wang, Di and Chen, Yinpeng and Karianakis, Nikolaos and Yu, Pei and Lymberopoulos, Dimitrios and Chen, Xiang},
	journal={arXiv preprint arXiv:1911.07158},
	year={2019}
}


@inproceedings{ye2019cap2det,
	title={Cap2det: Learning to amplify weak caption supervision for object detection},
	author={Ye, Keren and Zhang, Mingda and Kovashka, Adriana and Li, Wei and Qin, Danfeng and Berent, Jesse},
	booktitle={Proceedings of the IEEE International Conference on Computer Vision},
	pages={9686--9695},
	year={2019}
}


@inproceedings{xu2020cross,
	title={Cross-domain Detection via Graph-induced Prototype Alignment},
	author={Xu, Minghao and Wang, Hang and Ni, Bingbing and Tian, Qi and Zhang, Wenjun},
	booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
	pages={12355--12364},
	year={2020}
}


@article{li2020unsupervised,
	title={Unsupervised Image-generation Enhanced Adaptation for Object Detection in Thermal images},
	author={Li, Wanyi and Li, Fuyu and Luo, Yongkang and Wang, Peng},
	journal={arXiv preprint arXiv:2002.06770},
	year={2020}
}


@article{wu2019instance,
	title={Instance-Invariant Adaptive Object Detection via Progressive Disentanglement},
	author={Wu, Aming and Han, Yahong and Zhu, Linchao and Yang, Yi},
	journal={arXiv preprint arXiv:1911.08712},
	year={2019}
}


@inproceedings{yang2020unsupervised,
	title={Unsupervised domain adaptive object detection using forward-backward cyclic adaptation},
	author={Yang, Siqi and Wu, Lin and Wiliem, Arnold and Lovell, Brian C},
	booktitle={Proceedings of the Asian Conference on Computer Vision},
	year={2020}
}



@inproceedings{he2018domain,
	title={Domain Attention Model for Domain Generalization in Object Detection},
	author={He, Weixiong and Zheng, Huicheng and Lai, Jianhuang},
	booktitle={Chinese Conference on Pattern Recognition and Computer Vision (PRCV)},
	pages={27--39},
	year={2018},
	organization={Springer}
}


@article{liu2020domain,
	title={Domain Contrast for Domain Adaptive Object Detection},
	author={Liu, Feng and Zhang, Xiaoxong and Wan, Fang and Ji, Xiangyang and Ye, Qixiang},
	journal={arXiv preprint arXiv:2006.14863},
	year={2020}
}


@inproceedings{yang2020domain,
	title={Domain-Invariant Region Proposal Network For Cross-Domain Detection},
	author={Yang, Xuebin and Wan, Shouhong and Jin, Peiquan},
	booktitle={2020 IEEE International Conference on Multimedia and Expo (ICME)},
	pages={1--6},
	year={2020},
	organization={IEEE}
}


@inproceedings{sindagi2020prior,
	title={Prior-based domain adaptive object detection for hazy and rainy conditions},
	author={Sindagi, Vishwanath A and Oza, Poojan and Yasarla, Rajeev and Patel, Vishal M},
	booktitle={European Conference on Computer Vision},
	pages={763--780},
	year={2020},
	organization={Springer}
}



@article{munir2020thermal,
	title={Thermal Object Detection using Domain Adaptation through Style Consistency},
	author={Munir, Farzeen and Azam, Shoaib and Rafique, Muhammad Aasim and Sheri, Ahmad Muqeem and Jeon, Moongu},
	journal={arXiv preprint arXiv:2006.00821},
	year={2020}
}


@article{wen2020bridging,
	title={Bridging the Gap of Lane Detection Performance Between Different Datasets: Unified Viewpoint Transformation},
	author={Wen, Tuopu and Yang, Diange and Jiang, Kun and Yu, Chunlei and Lin, Jiaxin and Wijaya, Benny and Jiao, Xinyu},
	journal={IEEE Transactions on Intelligent Transportation Systems},
	year={2020},
	publisher={IEEE}
}


@inproceedings{li2020cross,
	title={Cross-Domain Document Object Detection: Benchmark Suite and Method},
	author={Li, Kai and Wigington, Curtis and Tensmeyer, Chris and Zhao, Handong and Barmpalios, Nikolaos and Morariu, Vlad I and Manjunatha, Varun and Sun, Tong and Fu, Yun},
	booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
	pages={12915--12924},
	year={2020}
}


@inproceedings{chen2020harmonizing,
	title={Harmonizing Transferability and Discriminability for Adapting Object Detectors},
	author={Chen, Chaoqi and Zheng, Zebiao and Ding, Xinghao and Huang, Yue and Dou, Qi},
	booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
	pages={8869--8878},
	year={2020}
}


@inproceedings{chen2019cross,
	title={Cross-Domain Scene Text Detection via Pixel and Image-Level Adaptation},
	author={Chen, Danlu and Lu, Lihua and Lu, Yao and Yu, Ruizhe and Wang, Shunzhou and Zhang, Lin and Liu, Tingxi},
	booktitle={International Conference on Neural Information Processing},
	pages={135--143},
	year={2019},
	organization={Springer}
}


@inproceedings{zheng2020cross,
	title={Cross-domain Object Detection through Coarse-to-Fine Feature Adaptation},
	author={Zheng, Yangtao and Huang, Di and Liu, Songtao and Wang, Yunhong},
	booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
	pages={13766--13775},
	year={2020}
}


@inproceedings{hsu2020every,
	title={Every Pixel Matters: Center-aware Feature Alignment for Domain Adaptive Object Detector},
	author={Hsu, Cheng-Chun and Tsai, Yi-Hsuan and Lin, Yen-Yu and Yang, Ming-Hsuan},
	booktitle={European Conference on Computer Vision},
	pages={733--748},
	year={2020},
	organization={Springer}
}



@inproceedings{pan2020multi,
	title={Multi-Scale Adversarial Cross-Domain Detection with Robust Discriminative Learning},
	author={Pan, YoungSun and Ma, Andy J and Gao, Yuan and Wang, JinPeng and Lin, Yiqi},
	booktitle={The IEEE Winter Conference on Applications of Computer Vision},
	pages={1324--1332},
	year={2020}
}


@article{hnewa2020object,
	title={Object Detection under Rainy Conditions for Autonomous Vehicles},
	author={Hnewa, Mazin and Radha, Hayder},
	journal={arXiv preprint arXiv:2006.16471},
	year={2020}
}


@inproceedings{xu2020exploring,
	title={Exploring Categorical Regularization for Domain Adaptive Object Detection},
	author={Xu, Chang-Dong and Zhao, Xing-Ran and Jin, Xin and Wei, Xiu-Shen},
	booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
	pages={11724--11733},
	year={2020}
}


@inproceedings{zhao2020adaptive,
	title={Adaptive object detection with dual multi-label prediction},
	author={Zhao, Zhen and Guo, Yuhong and Shen, Haifeng and Ye, Jieping},
	booktitle={European Conference on Computer Vision},
	pages={54--69},
	year={2020},
	organization={Springer}
}



@article{liu2020can,
	title={Can Synthetic Data Improve Object Detection Results for Remote Sensing Images?},
	author={Liu, Weixing and Liu, Jun and Luo, Bin},
	journal={arXiv preprint arXiv:2006.05015},
	year={2020}
}


@article{li2020object,
	title={Object detection based on semi-supervised domain adaptation for imbalanced domain resources},
	author={Li, Wei and Wang, Meng and Wang, Hongbin and Zhang, Yafei},
	journal={Machine Vision and Applications},
	volume={31},
	number={3},
	pages={1--18},
	year={2020},
	publisher={Springer}
}


@article{garnett2020synthetic,
	title={Synthetic-to-Real Domain Adaptation for Lane Detection},
	author={Garnett, Noa and Uziel, Roy and Efrat, Netalee and Levi, Dan},
	journal={arXiv preprint arXiv:2007.04023},
	year={2020}
}


@article{fu2020deeply,
	title={Deeply Aligned Adaptation for Cross-domain Object Detection},
	author={Fu, Minghao and Xie, Zhenshan and Li, Wen and Duan, Lixin},
	journal={arXiv preprint arXiv:2004.02093},
	year={2020}
}


@article{zhang2019cycle,
	title={Cycle-Consistent Domain Adaptive Faster RCNN},
	author={Zhang, Dan and Li, Jingjing and Xiong, Lin and Lin, Lan and Ye, Mao and Yang, Shangming},
	journal={IEEE Access},
	volume={7},
	pages={123903--123911},
	year={2019},
	publisher={IEEE}
}


@article{li2020deep,
	title={Deep Domain Adaptive Object Detection: a Survey},
	author={Li, Wanyi and Li, Fuyu and Luo, Yongkang and Wang, Peng},
	journal={arXiv preprint arXiv:2002.06797},
	year={2020}
}


@article{zhang2020virtual,
	title={A Virtual-Real Interaction Approach to Object Instance Segmentation in Traffic Scenes},
	author={Zhang, Hui and Luo, Guiyang and Tian, Yonglin and Wang, Kunfeng and He, Haibo and Wang, Fei-Yue},
	journal={IEEE Transactions on Intelligent Transportation Systems},
	year={2020},
	publisher={IEEE}
}


@inproceedings{alqasir2020region,
	title={Region Proposal Oriented Approach for Domain Adaptive Object Detection},
	author={Alqasir, Hiba and Muselet, Damien and Ducottet, Christophe},
	booktitle={International Conference on Advanced Concepts for Intelligent Vision Systems},
	pages={38--50},
	year={2020},
	organization={Springer}
}


@inproceedings{yang2020unsupervised,
	title={Unsupervised Domain Adaptation for Cross-Device OCT Lesion Detection via Learning Adaptive Features},
	author={Yang, Suhui and Zhou, Xia and Wang, Jun and Xie, Guotong and Lv, Chuanfeng and Gao, Peng and Lv, Bin},
	booktitle={2020 IEEE 17th International Symposium on Biomedical Imaging (ISBI)},
	pages={1570--1573},
	year={2020},
	organization={IEEE}
}


@inproceedings{zhao2020collaborative,
	title={Collaborative Training between Region Proposal Localization and Classification for Domain Adaptive Object Detection},
	author={Zhao, Ganlong and Li, Guanbin and Xu, Ruijia and Lin, Liang},
	booktitle={European Conference on Computer Vision},
	pages={86--102},
	year={2020},
	organization={Springer}
}



@article{tang2020learning,
	title={Learning a Domain Classifier Bank for Unsupervised Adaptive Object Detection},
	author={Tang, Sanli and Cheng, Zhanzhan and Pu, Shiliang and Guo, Dashan and Niu, Yi and Wu, Fei},
	journal={arXiv preprint arXiv:2007.02595},
	year={2020}
}


@article{abramov2020keep,
	title={Keep it Simple: Image Statistics Matching for Domain Adaptation},
	author={Abramov, Alexey and Bayer, Christopher and Heller, Claudio},
	journal={arXiv preprint arXiv:2005.12551},
	year={2020}
}

@inproceedings{nguyen2020domain,
	title={Domain-Adaptive Object Detection via Uncertainty-Aware Distribution Alignment},
	author={Nguyen, Dang-Khoa and Tseng, Wei-Lun and Shuai, Hong-Han},
	booktitle={Proceedings of the 28th ACM International Conference on Multimedia},
	pages={2499--2507},
	year={2020}
}


@article{soviany2021curriculum,
	title={Curriculum self-paced learning for cross-domain object detection},
	author={Soviany, Petru and Ionescu, Radu Tudor and Rota, Paolo and Sebe, Nicu},
	journal={Computer Vision and Image Understanding},
	volume={204},
	pages={103166},
	year={2021},
	publisher={Elsevier}
}



@inproceedings{luo2019joint,
	title={Joint Feature-level and Pixel-level Domain Adaption for Object Detection in the Wild},
	author={Luo, Qianhui and Wang, Yue and Li, Weijie and Xiong, Rong},
	booktitle={2019 IEEE 9th Annual International Conference on CYBER Technology in Automation, Control, and Intelligent Systems (CYBER)},
	pages={559--565},
	year={2019},
	organization={IEEE}
}



@inproceedings{su2020adapting,
	title={Adapting Object Detectors with Conditional Domain Normalization},
	author={Su, Peng and Wang, Kun and Zeng, Xingyu and Tang, Shixiang and Chen, Dapeng and Qiu, Di and Wang, Xiaogang},
	booktitle={European Conference on Computer Vision (ECCV)},
	pages={},
	year={2020}
}


@inproceedings{d2020one,
	title={One-Shot Unsupervised Cross-Domain Detection},
	author={D’Innocente, Antonio and Borlino, Francesco Cappio and Bucci, Silvia and Caputo, Barbara and Tommasi, Tatiana},
	booktitle={European Conference on Computer Vision},
	pages={732--748},
	year={2020},
	organization={Springer}
}


@article{liu2020wqt,
	title={WQT and DG-YOLO: towards domain generalization in underwater object detection},
	author={Liu, Hong and Song, Pinhao and Ding, Runwei},
	journal={arXiv preprint arXiv:2004.06333},
	year={2020}
}



@inproceedings{li2020spatial,
	title={Spatial Attention Pyramid Network for Unsupervised Domain Adaptation},
	author={Li, Congcong and Du, Dawei and Zhang, Libo and Wen, Longyin and Luo, Tiejian and Wu, Yanjun and Zhu, Pengfei},
	booktitle={European Conference on Computer Vision},
	year={2020},
	organization={Springer}
}



@article{pasqualino2020synthetic,
	title={Synthetic to Real Unsupervised Domain Adaptation for Single-Stage Artwork Recognition in Cultural Sites},
	author={Pasqualino, Giovanni and Furnari, Antonino and Signorello, Giovanni and Farinella, Giovanni Maria},
	journal={arXiv preprint arXiv:2008.01882},
	year={2020}
}


@article{deng2021unbiased,
	title={Unbiased Mean Teacher for Cross Domain Object Detection},
	author={Deng, Jinhong and Li, Wen and Chen, Yuhua and Duan, Lixin},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	year={2021}
}


@article{luo2020adversarial,
	title={Adversarial Style Mining for One-Shot Unsupervised Domain Adaptation},
	author={Luo, Yawei and Liu, Ping and Guan, Tao and Yu, Junqing and Yang, Yi},
	journal={Advances in Neural Information Processing Systems},
	volume={33},
	year={2020}
}



@inproceedings{he2020domain,
	title={Domain Adaptive Object Detection via Asymmetric Tri-way Faster-RCNN},
	author={He, Zhenwei and Zhang, Lei},
	booktitle={European Conference on Computer Vision},
	pages={481--497},
	year={2020},
	organization={Springer}
}


@article{zhang2021detr,
	title={DA-DETR: Domain Adaptive Detection Transformer by Hybrid Attention},
	author={Zhang, Jingyi and Huang, Jiaxing and Luo, Zhipeng and Zhang, Gongjie and Lu, Shijian},
	journal={arXiv preprint arXiv:2103.17084},
	year={2021}
}


@article{sun2021multi,
	title={Multi-Target Domain Adaptation via Unsupervised Domain Classification for Weather Invariant Object Detection},
	author={Sun, Ting and Chen, Jinlin and Ng, Francis},
	journal={arXiv preprint arXiv:2103.13970},
	year={2021}
}



@inproceedings{chen20213net,
	title={I\^{} 3Net: Implicit Instance-Invariant Network for Adapting One-Stage Object Detectors},
	author={Chen, Chaoqi and Zheng, Zebiao and Huang, Yue and Ding, Xinghao and Yu, Yizhou},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	year={2021}
}


@inproceedings{yang2020unsupervised,
  title={Unsupervised domain adaptive object detection using forward-backward cyclic adaptation},
  author={Yang, Siqi and Wu, Lin and Wiliem, Arnold and Lovell, Brian C},
  booktitle={Proceedings of the Asian Conference on Computer Vision},
  year={2020}
}

@article{munir2021sstn,
	title={SSTN: Self-Supervised Domain Adaptation Thermal Object Detection for Autonomous Driving},
	author={Munir, Farzeen and Azam, Shoaib and Jeon, Moongu},
	journal={arXiv preprint arXiv:2103.03150},
	year={2021}
}


@article{guan2021uncertainty,
	title={Uncertainty-Aware Unsupervised Domain Adaptation in Object Detection},
	author={Guan, Dayan and Huang, Jiaxing and Xiao, Aoran and Lu, Shijian and Cao, Yanpeng},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	year={2021}
}


@article{liang2020domain,
	title={Domain Adaptive Object Detection via Feature Separation and Alignment},
	author={Liang, Chengyang and Zhao, Zixiang and Liu, Junmin and Zhang, Jiangshe},
	journal={arXiv preprint arXiv:2012.08689},
	year={2020}
}


@article{li2020free,
	title={A Free Lunch for Unsupervised Domain Adaptive Object Detection without Source Data},
	author={Li, Xianfeng and Chen, Weijie and Xie, Di and Yang, Shicai and Yuan, Peng and Pu, Shiliang and Zhuang, Yueting},
	journal={arXiv preprint arXiv:2012.05400},
	year={2020}
}


@article{yang2020channel,
	title={Channel-wise Alignment for Adaptive Object Detection},
	author={Yang, Hang and Jiang, Shan and Zhu, Xinge and Huang, Mingyang and Shen, Zhiqiang and Liu, Chunxiao and Shi, Jianping},
	journal={arXiv preprint arXiv:2009.02862},
	year={2020}
}

@inproceedings{wang2021domain,
  title={Domain-Specific Suppression for Adaptive Object Detection},
  author={Wang, Yu and Zhang, Rui and Zhang, Shuo and Li, Miao and Xia, YangYang and Zhang, XiShan and Liu, ShaoLi},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={9603--9612},
  year={2021}
}


@article{scheck2020unsupervised,
	title={Unsupervised Domain Adaptation from Synthetic to Real Images for Anchorless Object Detection},
	author={Scheck, Tobias and Grassi, Ana Perez and Hirtz, Gangolf},
	journal={arXiv preprint arXiv:2012.08205},
	year={2020}
}



@article{chen2021scale,
  title={Scale-Aware Domain Adaptive Faster R-CNN},
  author={Chen, Yuhua and Wang, Haoran and Li, Wen and Sakaridis, Christos and Dai, Dengxin and Van Gool, Luc},
  journal={International Journal of Computer Vision},
  pages={1--21},
  year={2021},
  publisher={Springer}
}



@article{shen2021cdtd,
	title={CDTD: A Large-Scale Cross-Domain Benchmark for Instance-Level Image-to-Image Translation and Domain Adaptive Object Detection},
	author={Shen, Zhiqiang and Huang, Mingyang and Shi, Jianping and Liu, Zechun and Maheshwari, Harsh and Zheng, Yutong and Xue, Xiangyang and Savvides, Marios and Huang, Thomas S},
	journal={International Journal of Computer Vision},
	volume={129},
	number={3},
	pages={761--780},
	year={2021},
	publisher={Springer}
}

 


@inproceedings{jamal2020rethinking,
  title={Rethinking class-balanced methods for long-tailed visual recognition from a domain adaptation perspective},
  author={Jamal, Muhammad Abdullah and Brown, Matthew and Yang, Ming-Hsuan and Wang, Liqiang and Gong, Boqing},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={7610--7619},
  year={2020}
}

@inproceedings{cao2018partial,
  title={Partial adversarial domain adaptation},
  author={Cao, Zhangjie and Ma, Lijia and Long, Mingsheng and Wang, Jianmin},
  booktitle={Proceedings of the European Conference on Computer Vision (ECCV)},
  pages={135--150},
  year={2018}
}

@article{kim2020associative,
  title={Associative partial domain adaptation},
  author={Kim, Youngeun and Hong, Sungeun and Yang, Seunghan and Kang, Sungil and Jeon, Yunho and Kim, Jiwon},
  journal={arXiv preprint arXiv:2008.03111},
  year={2020}
}
@inproceedings{cao2019learning,
  title={Learning to transfer examples for partial domain adaptation},
  author={Cao, Zhangjie and You, Kaichao and Long, Mingsheng and Wang, Jianmin and Yang, Qiang},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2985--2994},
  year={2019}
}
@inproceedings{liu2019separate,
  title={Separate to adapt: Open set domain adaptation via progressive separation},
  author={Liu, Hong and Cao, Zhangjie and Long, Mingsheng and Wang, Jianmin and Yang, Qiang},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2927--2936},
  year={2019}
}
@inproceedings{panareda2017open,
  title={Open set domain adaptation},
  author={Panareda Busto, Pau and Gall, Juergen},
  booktitle={Proceedings of the IEEE International Conference on Computer Vision},
  pages={754--763},
  year={2017}
}
@inproceedings{kundu2020universal,
  title={Universal source-free domain adaptation},
  author={Kundu, Jogendra Nath and Venkat, Naveen and Babu, R Venkatesh and others},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={4544--4553},
  year={2020}
}
@inproceedings{nelakurthi2018source,
  title={Source free domain adaptation using an off-the-shelf classifier},
  author={Nelakurthi, Arun Reddy and Maciejewski, Ross and He, Jingrui},
  booktitle={2018 IEEE International Conference on Big Data (Big Data)},
  pages={140--145},
  year={2018},
  organization={IEEE}
}

@article{zhao2018adversarial,
  title={Adversarial multiple source domain adaptation},
  author={Zhao, Han and Zhang, Shanghang and Wu, Guanhang and Moura, Jos{\'e} MF and Costeira, Joao P and Gordon, Geoffrey J},
  journal={Advances in neural information processing systems},
  volume={31},
  pages={8559--8570},
  year={2018}
}
@inproceedings{peng2019moment,
  title={Moment matching for multi-source domain adaptation},
  author={Peng, Xingchao and Bai, Qinxun and Xia, Xide and Huang, Zijun and Saenko, Kate and Wang, Bo},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={1406--1415},
  year={2019}
}
 
@inproceedings{royer2015classifier,
  title={Classifier adaptation at prediction time},
  author={Royer, Amelie and Lampert, Christoph H},
  booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
  pages={1401--1409},
  year={2015}
}
@inproceedings{fredericks2014towards,
  title={Towards run-time adaptation of test cases for self-adaptive systems in the face of uncertainty},
  author={Fredericks, Erik M and DeVries, Byron and Cheng, Betty HC},
  booktitle={Proceedings of the 9th International Symposium on Software Engineering for Adaptive and Self-Managing Systems},
  pages={17--26},
  year={2014}
}
@inproceedings{wang2021tent,
  title={Tent: Fully test-time adaptation by entropy minimization},
  author={Wang, Dequan and Shelhamer, Evan and Liu, Shaoteng and Olshausen, Bruno and Darrell, Trevor and Berkeley, UC and Research, Adobe},
  booktitle={International Conference on Learning Representations},
  volume={4},
  pages={6},
  year={2021}
}

@inproceedings{hoffman2014continuous,
  title={Continuous manifold based adaptation for evolving visual domains},
  author={Hoffman, Judy and Darrell, Trevor and Saenko, Kate},
  booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
  pages={867--874},
  year={2014}
}
@inproceedings{wulfmeier2018incremental,
  title={Incremental adversarial domain adaptation for continually changing environments},
  author={Wulfmeier, Markus and Bewley, Alex and Posner, Ingmar},
  booktitle={2018 IEEE International conference on robotics and automation (ICRA)},
  pages={4489--4495},
  year={2018},
  organization={IEEE}
}

@inproceedings{wu2019ace,
  title={Ace: Adapting to changing environments for semantic segmentation},
  author={Wu, Zuxuan and Wang, Xin and Gonzalez, Joseph E and Goldstein, Tom and Davis, Larry S},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={2121--2130},
  year={2019}
}



@inproceedings{cordts2016cityscapes,
	title={The cityscapes dataset for semantic urban scene understanding},
	author={Cordts, Marius and Omran, Mohamed and Ramos, Sebastian and Rehfeld, Timo and Enzweiler, Markus and Benenson, Rodrigo and Franke, Uwe and Roth, Stefan and Schiele, Bernt},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	pages={3213--3223},
	year={2016}
}

@article{Sakaridis2018SemanticFS,
	title={Semantic Foggy Scene Understanding with Synthetic Data},
	author={Christos Sakaridis and Dengxin Dai and Luc Van Gool},
	journal={International Journal of Computer Vision},
	year={2018},
	volume={126},
	pages={973-992}
}

@article{johnson2016driving,
  title={Driving in the matrix: Can virtual worlds replace human-generated annotations for real world tasks?},
  author={Johnson-Roberson, Matthew and Barto, Charles and Mehta, Rounak and Sridhar, Sharath Nittur and Rosaen, Karl and Vasudevan, Ram},
  journal={arXiv preprint arXiv:1610.01983},
  year={2016}
}

@article{geiger2013vision,
	title={Vision meets robotics: The KITTI dataset},
	author={Geiger, Andreas and Lenz, Philip and Stiller, Christoph and Urtasun, Raquel},
	journal={The International Journal of Robotics Research},
	volume={32},
	number={11},
	pages={1231--1237},
	year={2013},
	publisher={Sage Publications Sage UK: London, England}
}

@article{everingham2010pascal,
  title={The pascal visual object classes (voc) challenge},
  author={Everingham, Mark and Van Gool, Luc and Williams, Christopher KI and Winn, John and Zisserman, Andrew},
  journal={International journal of computer vision},
  volume={88},
  number={2},
  pages={303--338},
  year={2010},
  publisher={Springer}
}

@inproceedings{inoue2018cross,
  title={Cross-domain weakly-supervised object detection through progressive domain adaptation},
  author={Inoue, Naoto and Furuta, Ryosuke and Yamasaki, Toshihiko and Aizawa, Kiyoharu},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={5001--5009},
  year={2018}
}

@inproceedings{yu2020bdd100k,
  title={Bdd100k: A diverse driving dataset for heterogeneous multitask learning},
  author={Yu, Fisher and Chen, Haofeng and Wang, Xin and Xian, Wenqi and Chen, Yingying and Liu, Fangchen and Madhavan, Vashisht and Darrell, Trevor},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={2636--2645},
  year={2020}
}

@inproceedings{yang2016wider,
  title={Wider face: A face detection benchmark},
  author={Yang, Shuo and Luo, Ping and Loy, Chen-Change and Tang, Xiaoou},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={5525--5533},
  year={2016}
}

@inproceedings{nada2018pushing,
  title={Pushing the limits of unconstrained face detection: a challenge dataset and baseline results},
  author={Nada, Hajime and Sindagi, Vishwanath A and Zhang, He and Patel, Vishal M},
  booktitle={2018 IEEE 9th International Conference on Biometrics Theory, Applications and Systems (BTAS)},
  pages={1--10},
  year={2018},
  organization={IEEE}
}

@article{li2019benchmarking,
title={Benchmarking Single-Image Dehazing and Beyond},
author={Li, Boyi and Ren, Wenqi and Fu, Dengpan and Tao, Dacheng and Feng, Dan and Zeng, Wenjun and Wang, Zhangyang},
journal={IEEE Transactions on Image Processing},
volume={28},
number={1},
pages={492--505},
year={2019},
publisher={IEEE}
}



@article{salzmann2021attention,
  title={Attention-based Domain Adaptation for Single Stage Detectors},
  author={Salzmann, Mathieu and others},
  journal={arXiv preprint arXiv:2106.07283},
  year={2021}
}

@article{wang2021afan,
  title={AFAN: Augmented Feature Alignment Network for Cross-Domain Object Detection},
  author={Wang, Hongsong and Liao, Shengcai and Shao, Ling},
  journal={IEEE Transactions on Image Processing},
  volume={30},
  pages={4046--4056},
  year={2021},
  publisher={IEEE}
}

@article{wang2021robust,
  title={Robust Object Detection via Instance-Level Temporal Cycle Confusion},
  author={Wang, Xin and Huang, Thomas E and Liu, Benlin and Yu, Fisher and Wang, Xiaolong and Gonzalez, Joseph E and Darrell, Trevor},
  journal={arXiv preprint arXiv:2104.08381},
  year={2021}
}

@article{nguyen2021incremental,
  title={Incremental Multi-Target Domain Adaptation for Object Detection with Efficient Domain Transfer},
  author={Nguyen-Meidine, Le Thanh and Kiran, Madhu and Pedersoli, Marco and Dolz, Jose and Blais-Morin, Louis-Antoine and Granger, Eric},
  journal={arXiv preprint arXiv:2104.06476},
  year={2021}
}


@article{geirhos2020shortcut,
  author    = {Geirhos, Robert and Jacobsen, Jörn-Henrik and Michaelis, Claudio and Zemel, Richard and Brendel, Wieland and Bethge, Matthias and Wichmann, Felix A.},
  title     = {{Shortcut learning in deep neural networks}},
  journal   = {Nature Machine Intelligence},
  volume    = {2},
  number    = {11},
  pages     = {665--673},
  year      = {2020}
}

@article{bengio2013representation,
  author    = {Bengio, Yoshua and Courville, Aaron and Vincent, Pascal},
  title     = {{Representation learning: A review and new perspectives}},
  journal   = {IEEE Transactions on Pattern Analysis and Machine Intelligence},
  volume    = {35},
  number    = {8},
  pages     = {1798--1828},
  year      = {2013}
}

@article{lake2017building,
  author    = {Lake, Brenden M. and Ullman, Tomer D. and Tenenbaum, Joshua B. and Gershman, Samuel J.},
  title     = {{Building machines that learn and think like people}},
  journal   = {Behavioral and Brain Sciences},
  volume    = {40},
  year      = {2017}
  
}

@misc{3dshapes18,
  author       = {Burgess, Chris and Kim, Hyunjik},
  title        = {{3D Shapes Dataset}},
  year         = {2018},
  howpublished = {\url{https://github.com/deepmind/3d-shapes}}
}

@inproceedings{chen2019isolating,
  author    = {Chen, Ricky T. Q. and Li, Xuechen and Grosse, Roger and Duvenaud, David},
  title     = {{Isolating sources of disentanglement in VAEs}},
  booktitle = {Proceedings of the 32nd International Conference on Neural Information Processing Systems (NeurIPS)},
  pages     = {2615--2625},
  year      = {2019}
}

@inproceedings{higgins2016beta,
  author    = {Higgins, Irina and Matthey, Loic and Pal, Arka and Burgess, Christopher and Glorot, Xavier and Botvinick, Matthew and Mohamed, Shakir and Lerchner, Alexander},
  title     = {{$\beta$-VAE: Learning Basic Visual Concepts with a Constrained Variational Framework}},
  booktitle = {International Conference on Learning Representations (ICLR)},
  year      = {2016}
}

@inproceedings{kim2018disentangling,
  author    = {Kim, Hyunjik and Mnih, Andriy},
  title     = {{Disentangling by factorising}},
  booktitle = {International Conference on Machine Learning (ICML)},
  pages     = {2649--2658},
  year      = {2018},
  publisher = {PMLR}
}

@inproceedings{Lee_2018_ECCV,
  author    = {Lee, Hsin-Ying and Tseng, Hung-Yu and Huang, Jia-Bin and Singh, Maneesh and Yang, Ming-Hsuan},
  title     = {{Diverse Image-to-Image Translation via Disentangled Representations}},
  booktitle = {Proceedings of the European Conference on Computer Vision (ECCV)},
  month     = {September},
  year      = {2018}
}



@inproceedings{larsen2016autoencoding,
  author    = {Larsen, Anders Boesen Lindbo and Sønderby, Søren Kaae and Larochelle, Hugo and Winther, Ole},
  title     = {{Autoencoding beyond pixels using a learned similarity metric}},
  booktitle = {International Conference on Machine Learning (ICML)},
  pages     = {1558--1566},
  year      = {2016},
  publisher = {PMLR}
}

@inproceedings{Yang_2021_CVPR,
  author    = {Yang, Mengyue and Liu, Furui and Chen, Zhitang and Shen, Xiaoteng and Hao, Jianheng and Wang, Jun},
  title     = {{CausalVAE: Disentangled Representation Learning via Neural Structural Causal Models}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {9593--9602},
  month     = {June},
  year      = {2021}
}

@article{ghandeharioun2021dissect,
  author        = {Ghandeharioun, Asma and Kim, Been and Li, Chun-Liang and Jou, Brendan and Eoff, Brian and Picard, Rosalind W.},
  title         = {{Dissect: Disentangled simultaneous explanations via concept traversals}},
  year          = {2021},
  eprint        = {2105.15164},
  archivePrefix = {arXiv},
  primaryClass  = {cs.LG}
}

@article{wu2020improving,
  author        = {Wu, Jinyi and Li, Xiang and Ao, Xiang and Meng, Yulong and Wu, Fei and Li, Jiwei},
  title         = {{Improving Robustness and Generality of NLP Models Using Disentangled Representations}},
  year          = {2020},
  eprint        = {2009.09587},
  archivePrefix = {arXiv},
  primaryClass  = {cs.CL}
}

@inproceedings{suter2019robustly,
  author    = {Suter, Raphael and Miladinovic, Djordje and Schölkopf, Bernhard and Bauer, Stefan},
  title     = {{Robustly disentangled causal mechanisms: Validating deep representations for interventional robustness}},
  booktitle = {International Conference on Machine Learning (ICML)},
  pages     = {6056--6065},
  year      = {2019},
  publisher = {PMLR}
}

@article{lee2021learning,
  author    = {Lee, Jaehyung and Kim, Eungbean and Lee, Jihyeon and Lee, Jinyoung and Choo, Jaegul},
  title     = {{Learning Debiased Representation via Disentangled Feature Augmentation}},
  journal   = {Advances in Neural Information Processing Systems},
  volume    = {34},
  pages     = {25123--25133},
  year      = {2021}
}

@article{kingma2013auto,
  author        = {Kingma, Diederik P. and Welling, Max},
  title         = {{Auto-Encoding Variational Bayes}},
  year          = {2013},
  eprint        = {1312.6114},
  archivePrefix = {arXiv},
  primaryClass  = {stat.ML}
}

@inproceedings{goodfellow2014generative,
  author    = {Goodfellow, Ian J. and Pouget-Abadie, Jean and Mirza, Mehdi and Xu, Bing and Warde-Farley, David and Ozair, Sherjil and Courville, Aaron and Bengio, Yoshua},
  title     = {{Generative Adversarial Nets}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {27},
  year      = {2014},
  publisher = {Curran Associates, Inc.}
}

@article{higgins2018towards,
  author        = {Higgins, Irina and Amos, David and Pfau, David and Racaniere, Sebastien and Matthey, Loic and Rezende, Danilo and Lerchner, Alexander},
  title         = {{Towards a Definition of Disentangled Representations}},
  year          = {2018},
  eprint        = {1812.02230},
  archivePrefix = {arXiv},
  primaryClass  = {cs.LG}
}

@inproceedings{zhu2018visual,
  author    = {Zhu, Jia-Jun and Zhang, Zhoutong and Zhang, Cheng and Wu, Jia-Xue and Torralba, Antonio and Tenenbaum, Joshua B. and Freeman, Bill},
  title     = {{Visual Object Networks: Image Generation with Disentangled 3D Representations}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {31},
  pages     = {118--129},
  year      = {2018}
}

@inproceedings{gonzalez2018image,
  author    = {Gonzalez-Garcia, Abel and van de Weijer, Joost and Bengio, Yoshua},
  title     = {{Image-to-Image Translation for Cross-Domain Disentanglement}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {31},
  year      = {2018},
  publisher = {Curran Associates, Inc.}
}

@inproceedings{Lee_2021_CVPR,
  author    = {Lee, Sangwoo and Cho, Sungroh and Im, Suha},
  title     = {{DRANet: Disentangling Representation and Adaptation Networks for Unsupervised Cross-Domain Adaptation}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {15252--15261},
  month     = {June},
  year      = {2021}
}

@inproceedings{Liu_2021_CVPR,
  author    = {Liu, Yu and Sangineto, Emanuele and Chen, Yadong and Bao, Lin and Zhang, Haoxian and Sebe, Nicu and Lepri, Bruno and Wang, Wei and De Nadai, Marco},
  title     = {{Smoothing the Disentangled Latent Style Space for Unsupervised Image-to-Image Translation}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {10785--10794},
  month     = {June},
  year      = {2021}
}

@inproceedings{he2017unsupervised,
  author    = {He, Ruidan and Lee, Wee Sun and Ng, Hwee Tou and Dahlmeier, Daniel},
  title     = {{An Unsupervised Neural Attention Model for Aspect Extraction}},
  booktitle = {Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers)},
  pages     = {388--397},
  year      = {2017},
  publisher = {Association for Computational Linguistics}
}

@inproceedings{bao2019generating,
  author    = {Bao, Yu and Zhou, Hao and Huang, Shujian and Li, Lei and Mou, Lili and Vechtomova, Olga and Dai, Xin-yu and Chen, Jiajun},
  title     = {{Generating Sentences from Disentangled Syntactic and Semantic Spaces}},
  booktitle = {Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics},
  pages     = {6008--6019},
  year      = {2019},
  address   = {Florence, Italy},
  publisher = {Association for Computational Linguistics},
  month     = {Jul}
}

@inproceedings{cheng2020improving,
  author    = {Cheng, Pengyu and Min, Martin Renqiang and Shen, Dinghan and Malon, Christopher and Zhang, Yizhe and Li, Yitong and Carin, Lawrence},
  title     = {{Improving Disentangled Text Representation Learning with Information-Theoretic Guidance}},
  booktitle = {Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics},
  pages     = {7530--7541},
  year      = {2020},
  publisher = {Association for Computational Linguistics}
}

@inproceedings{ma2019learning,
  author    = {Ma, Jianxin and Zhou, Chang and Cui, Peng and Yang, Hongxia and Zhu, Wenwu},
  title     = {{Learning Disentangled Representations for Recommendation}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {32},
  year      = {2019},
  publisher = {Curran Associates, Inc.}
}

@inproceedings{zhang2020content,
  author    = {Zhang, Yiding and Zhu, Zhaochun and He, Yudan and Caverlee, James},
  title     = {{Content-Collaborative Disentanglement Representation Learning for Enhanced Recommendation}},
  booktitle = {Fourteenth ACM Conference on Recommender Systems (RecSys '20)},
  pages     = {43--52},
  year      = {2020},
  publisher = {Association for Computing Machinery} 
}

@inproceedings{wang2021multimodal,
  author    = {Wang, Xiaoyu and Chen, Hong and Zhu, Wenwu},
  title     = {{Multimodal Disentangled Representation for Recommendation}},
  booktitle = {2021 IEEE International Conference on Multimedia and Expo (ICME)},
  pages     = {1--6},
  year      = {2021},
  publisher = {IEEE}
}

@inproceedings{wang2020disentangled,
  author    = {Wang, Xiang and Jin, Hongye and Zhang, An and He, Xiangnan and Xu, Tong and Chua, Tat-Seng},
  title     = {{Disentangled Graph Collaborative Filtering}},
  booktitle = {Proceedings of the 43rd International ACM SIGIR Conference on Research and Development in Information Retrieval (SIGIR '20)},
  pages     = {1001--1010},
  year      = {2020},
  publisher = {Association for Computing Machinery} 
}

@inproceedings{ma2019disentangled,
  author    = {Ma, Jianan and Cui, Peng and Kuang, Kun and Wang, Xin and Zhu, Wenwu},
  title     = {{Disentangled Graph Convolutional Networks}},
  booktitle = {International Conference on Machine Learning (ICML)},
  pages     = {4212--4221},
  year      = {2019},
  publisher = {PMLR}
}

@article{liu2022learning,
  author    = {Liu, Xiao and Sanchez, Pedro and Thermos, Spyridon and O'Neil, Alison Q. and Tsaftaris, Sotirios A.},
  title     = {{Learning disentangled representations in the imaging domain}},
  journal   = {Medical Image Analysis},
  pages     = {102516},
  year      = {2022},
  volume    = {80} 
}

@inproceedings{dupont2018learning,
  author    = {Dupont, Emilien},
  title     = {{Learning Disentangled Joint Continuous and Discrete Representations}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {31},
  year      = {2018},
  publisher = {Curran Associates, Inc.}
}

@article{kim2019relevance,
  author        = {Kim, Minyoung and Wang, Yuting and Sahu, Pritish and Pavlovic, Vladimir},
  title         = {{Relevance Factor VAE: Learning and Identifying Disentangled Factors}},
  year          = {2019},
  eprint        = {1902.01568},
  archivePrefix = {arXiv},
  primaryClass  = {stat.ML}
}

@article{burgess2018understanding,
  author        = {Burgess, Christopher P. and Higgins, Irina and Pal, Arka and Matthey, Loic and Watters, Nick and Desjardins, Guillaume and Lerchner, Alexander},
  title         = {{Understanding disentangling in $\beta$-VAE}},
  year          = {2018},
  eprint        = {1804.03599},
  archivePrefix = {arXiv},
  primaryClass  = {stat.ML}
}

@inproceedings{kumar2018variational,
  author    = {Kumar, Abhishek and Sattigeri, Prasanna and Balakrishnan, Avinash},
  title     = {{Variational Inference of Disentangled Latent Concepts from Unlabeled Observations}},
  booktitle = {International Conference on Learning Representations (ICLR)},
  year      = {2018}
}

@inproceedings{bouchacourt2018multi,
  author    = {Bouchacourt, Diane and Tomioka, Ryota and Nowozin, Sebastian},
  title     = {{Multi-Level Variational Autoencoder: Learning Disentangled Representations from Grouped Observations}},
  booktitle = {Thirty-Second AAAI Conference on Artificial Intelligence},
  year      = {2018}
}

@article{bing2021disentanglement,
  author        = {Bing, Sicong and Fortuin, Vincent and Rätsch, Gunnar},
  title         = {{On disentanglement in Gaussian process variational autoencoders}},
  year          = {2021},
  eprint        = {2102.05507},
  archivePrefix = {arXiv},
  primaryClass  = {cs.LG}
}

@inproceedings{caselles2019symmetry,
  author    = {Caselles-Dupré, Hugo and Garcia Ortiz, Michael and Filliat, David},
  title     = {{Symmetry-Based Disentangled Representation Learning Requires Interaction with Environments}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {32},
  year      = {2019},
  publisher = {Curran Associates, Inc.}
}

@inproceedings{quessard2020learning,
  author    = {Quessard, Ronan and Barrett, Thomas and Clements, William},
  title     = {{Learning Disentangled Representations and Group Structure of Dynamical Environments}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {33},
  pages     = {19727--19737},
  year      = {2020},
  publisher = {Curran Associates, Inc.}
}

@inproceedings{yang2021towards,
  author    = {Yang, Tutian and Ren, Xiaolong and Wang, Yixuan and Zeng, Wenjun and Zheng, Nanning},
  title     = {{Towards Building a Group-Based Unsupervised Representation Disentanglement Framework}},
  booktitle = {International Conference on Learning Representations (ICLR)},
  year      = {2022} 
}

@inproceedings{wang2021self,
  author    = {Wang, Tete and Yue, Zhipeng and Huang, Jianmin and Sun, Qianru and Zhang, Hong},
  title     = {{Self-Supervised Learning Disentangled Group Representation as Feature}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {34},
  year      = {2021},
  publisher = {Curran Associates, Inc.}
}

@book{pearl2009causality,
  title     = {{Causality}},
  author    = {Pearl, Judea},
  year      = {2009},
  publisher = {Cambridge University Press},
  edition   = {2nd} 
}

@article{shen2020disentangled,
  author        = {Shen, Xiaoteng and Liu, Furui and Dong, Han and Lian, Qiang and Chen, Zhitang and Zhang, Tong},
  title         = {{Disentangled Generative Causal Representation Learning}},
  year          = {2020},
  eprint        = {2010.02637},
  archivePrefix = {arXiv},
  primaryClass  = {cs.LG}
}

@article{726791,
  author    = {Lecun, Yann and Bottou, Léon and Bengio, Yoshua and Haffner, Patrick},
  title     = {{Gradient-based learning applied to document recognition}},
  journal   = {Proceedings of the IEEE},
  volume    = {86},
  number    = {11},
  pages     = {2278--2324},
  year      = {1998}
}

@inproceedings{liu2015faceattributes,
  author    = {Liu, Ziwei and Luo, Ping and Wang, Xiaogang and Tang, Xiaoou},
  title     = {{Deep Learning Face Attributes in the Wild}},
  booktitle = {Proceedings of International Conference on Computer Vision (ICCV)},
  month     = {December},
  year      = {2015}
}

@inproceedings{Aubry14,
  author    = {Aubry, Mathieu and Maturana, Daniel and Efros, Alexei A. and Russell, Bryan C. and Sivic, Josef},
  title     = {{Seeing 3D chairs: Exemplar Part-Based 2D-3D Alignment Using a Large Dataset of CAD Models}},
  booktitle = {Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR)},
  year      = {2014}
}

@article{li2018disentangled,
  author        = {Li, Yingzhen and Mandt, Stephan},
  title         = {{Disentangled Sequential Autoencoder}},
  year          = {2018},
  eprint        = {1803.02991},
  archivePrefix = {arXiv},
  primaryClass  = {cs.LG}
}

@article{arjovsky2019invariant,
  author        = {Arjovsky, Martin and Bottou, Léon and Gulrajani, Ishaan and Lopez-Paz, David},
  title         = {{Invariant Risk Minimization}},
  year          = {2019},
  eprint        = {1907.02893},
  archivePrefix = {arXiv},
  primaryClass  = {cs.LG}
}

@inproceedings{zhu2021commutative,
  author    = {Zhu, Xin and Xu, Chang and Tao, Dacheng},
  title     = {{Commutative Lie Group VAE for Disentanglement Learning}},
  booktitle = {International Conference on Machine Learning (ICML)},
  pages     = {12924--12934},
  year      = {2021},
  publisher = {PMLR}
}

@inproceedings{jeon2021ib,
  author    = {Jeon, Inha and Lee, Won-jong and Pyeon, Myeong-ryun and Kim, Gunhee},
  title     = {{IB-GAN: Disentangled Representation Learning With Information Bottleneck Generative Adversarial Networks}},
  booktitle = {Proceedings of the AAAI Conference on Artificial Intelligence},
  volume    = {35},
  number    = {9}, 
  pages     = {7926--7934},
  year      = {2021},
  publisher = {AAAI Press} 
}

@misc{lin2019infogan,
  author = {Lin, Zinan and Thekumparampil, Kiran K. and Fanti, Giulia C. and Oh, Sewoong},
  title  = {{InfoGAN-CR: Disentangling Generative Adversarial Networks with Contrastive Regularizers}},
  year   = {2019},
  note   = {Work in progress or preprint, potentially arXiv:1911.10471} 
}

@inproceedings{zhu2021and,
  author    = {Zhu, Xin and Xu, Chang and Tao, Dacheng},
  title     = {{Where and What? Examining Interpretable Disentangled Representations}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {5861--5870},
  year      = {2021}
}

@inproceedings{wei2021orthogonal,
  author    = {Wei, Yu and Shi, Yupeng and Liu, Xiao and Ji, Zhong and Gao, Yuan and Wu, Zhi-Hua and Zuo, Wangmeng},
  title     = {{Orthogonal Jacobian Regularization for Unsupervised Disentanglement in Image Generation}},
  booktitle = {Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)},
  pages     = {6721--6730},
  year      = {2021}
}

@inproceedings{rombach2022high,
  author    = {Rombach, Robin and Blattmann, Andreas and Lorenz, Dominik and Esser, Patrick and Ommer, Björn},
  title     = {{High-Resolution Image Synthesis with Latent Diffusion Models}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {10684--10695},
  year      = {2022}
}

@inproceedings{kawar2023imagic,
  author    = {Kawar, Bahjat and Zada, Shiran and Lang, Or and Tov, Omer and Chang, Huiwen and Dekel, Tali and Mosseri, Inbar and Irani, Michal},
  title     = {{Imagic: Text-Based Real Image Editing with Diffusion Models}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {6007--6017},
  year      = {2023}
}

@inproceedings{wu2023tune,
  author    = {Wu, Jay Zhangjie and Ge, Yixiao and Wang, Xintao and Lei, Stan Weixian and Gu, Yuchao and Shi, Yufei and Hsu, Wynne and Shan, Ying and Qie, Xiaohu and Shou, Mike Zheng},
  title     = {{Tune-A-Video: One-Shot Tuning of Image Diffusion Models for Text-to-Video Generation}},
  booktitle = {Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)},
  pages     = {7623--7633},
  year      = {2023}
}

@inproceedings{ho2020denoising,
  author    = {Ho, Jonathan and Jain, Ajay and Abbeel, Pieter},
  title     = {{Denoising Diffusion Probabilistic Models}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {33},
  pages     = {6840--6851},
  year      = {2020},
  publisher = {Curran Associates, Inc.}
}

@inproceedings{liupseudo,
  author    = {Liu, Luping and Ren, Yi and Lin, Zhifeng and Zhao, Zhou},
  title     = {{Pseudo Numerical Methods for Diffusion Models on Manifolds}},
  booktitle = {International Conference on Learning Representations (ICLR)},
  year      = {2022} 
}

@inproceedings{lu2022dpm,
  author    = {Lu, Cheng and Zhou, Yuhao and Bao, Fan and Chen, Jianfei and Li, Chong and Zhu, Jun},
  title     = {{DPM-Solver: A Fast ODE Solver for Diffusion Probabilistic Model Sampling in Around 10 Steps}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {35},
  pages     = {5775--5787},
  year      = {2022},
  publisher = {Curran Associates, Inc.}
}

@article{yang2023disdiff,
  author        = {Yang, Tutian and Wang, Yixuan and Lv, Yujun and Zheng, Nanning},
  title         = {{DisDiff: Unsupervised Disentanglement of Diffusion Probabilistic Models}},
  year          = {2023},
  eprint        = {2301.13721},
  archivePrefix = {arXiv},
  primaryClass  = {cs.CV}
}

@inproceedings{dhariwal2021diffusion,
  author    = {Dhariwal, Prafulla and Nichol, Alexander},
  title     = {{Diffusion Models Beat GANs on Image Synthesis}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {34},
  pages     = {8780--8794},
  year      = {2021},
  publisher = {Curran Associates, Inc.}
}

@article{chen2023disenbooth,
  author        = {Chen, Hong and Zhang, Yuchao and Wang, Xiaoyu and Duan, Xing and Zhou, Yu and Zhu, Wenwu},
  title         = {{DisenBooth: Disentangled Parameter-Efficient Tuning for Subject-Driven Text-to-Image Generation}},
  year          = {2023},
  eprint        = {2305.03374},
  archivePrefix = {arXiv},
  primaryClass  = {cs.CV}
}

@article{chen2024disendreamer,
  author    = {Chen, Hong and Zhang, Yuchao and Wang, Xiaoyu and Duan, Xing and Zhou, Yu and Zhu, Wenwu},
  title     = {{DisenDreamer: Subject-Driven Text-to-Image Generation With Sample-Aware Disentangled Tuning}},
  journal   = {IEEE Transactions on Circuits and Systems for Video Technology},
  year      = {2024},
  volume = {34}
}

@article{chen2023videodreamer,
  author        = {Chen, Hong and Wang, Xiaoyu and Zeng, Guozhi and Zhang, Yuchao and Zhou, Yu and Han, Fang and Zhu, Wenwu},
  title         = {{VideoDreamer: Customized Multi-Subject Text-to-Video Generation with Disen-Mix Finetuning}},
  year          = {2023},
  eprint        = {2311.00990},
  archivePrefix = {arXiv},
  primaryClass  = {cs.CV}
}

@inproceedings{shen2021closed,
  author    = {Shen, Yujun and Zhou, Bolei},
  title     = {{Closed-Form Factorization of Latent Semantics in GANs}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {1532--1540},
  year      = {2021}
}

@article{khrulkov2021disentangled,
  author        = {Khrulkov, Valentin and Mirvakhabova, Leyla and Oseledets, Ivan and Babenko, Artem},
  title         = {{Disentangled Representations from Non-Disentangled Models}},
  year          = {2021},
  eprint        = {2102.06204},
  archivePrefix = {arXiv},
  primaryClass  = {cs.LG}
}

@inproceedings{voynov2020unsupervised,
  author    = {Voynov, Andrey and Babenko, Artem},
  title     = {{Unsupervised Discovery of Interpretable Directions in the GAN Latent Space}},
  booktitle = {International Conference on Machine Learning (ICML)},
  pages     = {9786--9796},
  year      = {2020},
  publisher = {PMLR}
}

@inproceedings{ren2021learning,
  author    = {Ren, Xiaolong and Yang, Tutian and Wang, Yixuan and Zeng, Wenjun},
  title     = {{Learning Disentangled Representation by Exploiting Pretrained Generative Models: A Contrastive Learning View}},
  booktitle = {International Conference on Learning Representations (ICLR)},
  year      = {2021}
}

@inproceedings{kwon2022diffusion,
  author    = {Kwon, Minwoo and Jeong, Juyeon and Uh, Youngjung},
  title     = {{Diffusion Models Already Have a Semantic Latent Space}},
  booktitle = {The Eleventh International Conference on Learning Representations (ICLR)},
  year      = {2023} 
}

@inproceedings{liu2021activity,
  author    = {Liu, Liqi and Li, Jiacheng and Niu, Li and Xu, Rui and Zhang, Liqing},
  title     = {{Activity Image-to-Video Retrieval by Disentangling Appearance and Motion}},
  booktitle = {Proceedings of the AAAI Conference on Artificial Intelligence},
  volume    = {35},
  number    = {3}, 
  pages     = {2350--2358}, 
  year      = {2021}
}

@inproceedings{denton2017unsupervised,
  author    = {Denton, Emily L. and Birodkar, Vighnesh},
  title     = {{Unsupervised Learning of Disentangled Representations from Video}},
  booktitle = {Proceedings of the 31st International Conference on Neural Information Processing Systems (NIPS)},
  pages     = {4417--4426},
  year      = {2017},
  publisher = {Curran Associates, Inc.}
}

@inproceedings{tran2017disentangled,
  author    = {Tran, Luan and Yin, Xi and Liu, Xiaoming},
  title     = {{Disentangled Representation Learning GAN for Pose-Invariant Face Recognition}},
  booktitle = {Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {1415--1424},
  year      = {2017}
}

@inproceedings{singh2019finegan,
  author    = {Singh, Krishna Kumar and Ojha, Utkarsh and Lee, Yong Jae},
  title     = {{FineGAN: Unsupervised Hierarchical Disentanglement for Fine-Grained Object Generation and Discovery}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {6490--6499},
  year      = {2019}
}

@article{xiao2017dna,
  author        = {Xiao, Ting and Hong, Jiaming and Ma, Javen},
  title         = {{DNA-GAN: Learning Disentangled Representations from Multi-Attribute Images}},
  year          = {2017},
  eprint        = {1711.05415},
  archivePrefix = {arXiv},
  primaryClass  = {cs.CV}
}

@article{cheng2021disentangled,
  author        = {Cheng, Hong and Wang, Yangyang and Li, Haifeng and Kot, Alex C. and Wen, Bihan},
  title         = {{Disentangled Feature Representation for Few-Shot Image Classification}},
  year          = {2021},
  eprint        = {2109.12548},
  archivePrefix = {arXiv},
  primaryClass  = {cs.CV}
}

@inproceedings{Gao_2021_CVPR,
  author    = {Gao, Gee-Sern and Huang, Hung-Jen and Fu, Chang-Sheng and Li, Zhuo and He, Ran},
  title     = {{Information Bottleneck Disentanglement for Identity Swapping}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {3404--3413},
  month     = {June},
  year      = {2021}
}

@inproceedings{ross2021benchmarks,
  author    = {Ross, Andrew and Doshi-Velez, Finale},
  title     = {{Benchmarks, Algorithms, and Metrics for Hierarchical Disentanglement}},
  booktitle = {International Conference on Machine Learning (ICML)},
  pages     = {9084--9094},
  year      = {2021},
  publisher = {PMLR}
}

@article{li2020progressive,
  author        = {Li, Zhiyuan and Murkute, Jaidev V. and Gyawali, Prashnna K. and Wang, Linwei},
  title         = {{Progressive learning and disentanglement of hierarchical representations}},
  year          = {2020},
  eprint        = {2002.10549},
  archivePrefix = {arXiv},
  primaryClass  = {cs.LG}
}

@inproceedings{tong2019hierarchical,
  author    = {Tong, Bo and Wang, Chao and Klinkigt, Martin and Kobayashi, Yuki and Nonaka, Yoshihide},
  title     = {{Hierarchical Disentanglement of Discriminative Latent Features for Zero-Shot Learning}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {11467--11476},
  year      = {2019}
}

@inproceedings{li2021image,
  author    = {Li, Xueting and Zhang, Sifei and Hu, Jiang and Cao, Lin and Hong, Xiaoyan and Mao, Xudong and Huang, Feiyue and Wu, Yongjian and Ji, Rongrong},
  title     = {{Image-to-Image Translation via Hierarchical Style Disentanglement}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {8639--8648},
  year      = {2021}
}

@inproceedings{hjelm2018learning,
  author    = {Hjelm, R. Devon and Fedorov, Alex and Lavoie-Marchildon, Samuel and Grewal, Karan and Bachman, Phil and Trischler, Adam and Bengio, Yoshua},
  title     = {{Learning Deep Representations by Mutual Information Estimation and Maximization}},
  booktitle = {International Conference on Learning Representations (ICLR)},
  year      = {2019} 
}

@inproceedings{alemi2019deep,
  author    = {Alemi, Alexander A. and Fischer, Ian and Dillon, Joshua V. and Murphy, Kevin},
  title     = {{Deep Variational Information Bottleneck}},
  booktitle = {International Conference on Learning Representations (ICLR)},
  year      = {2017}
}

@inproceedings{locatello2019challenging,
  author    = {Locatello, Francesco and Bauer, Stefan and Lucic, Mario and Raetsch, Gunnar and Gelly, Sylvain and Schölkopf, Bernhard and Bachem, Olivier},
  title     = {{Challenging Common Assumptions in the Unsupervised Learning of Disentangled Representations}},
  booktitle = {International Conference on Machine Learning (ICML)},
  pages     = {4114--4124},
  year      = {2019},
  publisher = {PMLR}
}

@inproceedings{Locatello2020Disentangling,
  author    = {Locatello, Francesco and Tschannen, Michael and Bauer, Stefan and Rätsch, Gunnar and Schölkopf, Bernhard and Bachem, Olivier},
  title     = {{Disentangling Factors of Variations Using Few Labels}},
  booktitle = {International Conference on Learning Representations (ICLR)},
  year      = {2020}
}

@article{kulkarni2015deep,
  author        = {Kulkarni, Tejas D. and Whitney, William F. and Kohli, Pushmeet and Tenenbaum, Joshua B.},
  title         = {{Deep Convolutional Inverse Graphics Network}},
  year          = {2015},
  eprint        = {1503.03167},
  archivePrefix = {arXiv},
  primaryClass  = {cs.CV}
}

@inproceedings{Bengio2020A,
  author    = {Bengio, Yoshua and Deleu, Tristan and Rahaman, Nasim and Ke, Nan Rosemary and Lachapelle, Sebastien and Bilaniuk, Olexa and Goyal, Anirudh and Pal, Christopher},
  title     = {{A Meta-Transfer Objective for Learning to Disentangle Causal Mechanisms}},
  booktitle = {International Conference on Learning Representations (ICLR)},
  year      = {2020}
}

@inproceedings{xiang2021disunknown,
  author    = {Xiang, Sindy and Gu, Yanhua and Xiang, Peidong and Chai, Mengting and Li, Hao and Zhao, Yang and He, Ming},
  title     = {{DisUnknown: Distilling Unknown Factors for Disentanglement Learning}},
  booktitle = {Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)},
  pages     = {14810--14819},
  year      = {2021}
}

@inproceedings{khemakhem2020variational,
  author    = {Khemakhem, Ilyes and Kingma, Diederik and Monti, Ricardo and Hyvarinen, Aapo},
  title     = {{Variational Autoencoders and Nonlinear ICA: A Unifying Framework}},
  booktitle = {International Conference on Artificial Intelligence and Statistics (AISTATS)},
  pages     = {2207--2217},
  year      = {2020},
  publisher = {PMLR}
}

@article{reddy2021causally,
  author        = {Reddy, Attili Gokul and Benin Godfrey, L. and Balasubramanian, Vineeth N.},
  title         = {{On Causally Disentangled Representations}},
  year          = {2021},
  eprint        = {2112.05746},
  archivePrefix = {arXiv},
  primaryClass  = {cs.LG}
}

@article{greenland1999confounding,
  author    = {Greenland, Sander and Pearl, Judea and Robins, James M.},
  title     = {{Confounding and Collapsibility in Causal Inference}},
  journal   = {Statistical Science},
  volume    = {14},
  number    = {1},
  pages     = {29--46},
  year      = {1999}
}

@inproceedings{yu2019dag,
  author    = {Yu, Yue and Chen, Jie and Gao, Tian and Yu, Mo},
  title     = {{DAG-GNN: DAG Structure Learning with Graph Neural Networks}},
  booktitle = {International Conference on Machine Learning (ICML)},
  pages     = {7154--7163},
  year      = {2019},
  publisher = {PMLR}
}

@article{ribeiro2022learning,
  author        = {Ribeiro, Fabio De Sousa and Duarte, Kevin and Everett, Martin and Leontidis, Georgios and Shah, Mubarak},
  title         = {{Learning with Capsules: A Survey}},
  year          = {2022},
  eprint        = {2206.02664},
  archivePrefix = {arXiv},
  primaryClass  = {cs.CV}
}

@inproceedings{sabour2017dynamic,
  author    = {Sabour, Sara and Frosst, Nicholas and Hinton, Geoffrey E.},
  title     = {{Dynamic Routing Between Capsules}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {30},
  year      = {2017},
  publisher = {Curran Associates, Inc.}
}

@article{patrick2022capsule,
  author    = {Patrick, Matthew K. and Adekoya, Ayodeji F. and Mighty, Ajibola A. and Edward, Blessing Y.},
  title     = {{Capsule Networks--A Survey}},
  journal   = {Journal of King Saud University - Computer and Information Sciences},
  volume    = {34},
  number    = {1},
  pages     = {1295--1310},
  year      = {2022}
}

@article{mazzia2021efficient,
  author    = {Mazzia, Vittorio and Salvetti, Franco and Chiaberge, Marcello},
  title     = {{Efficient-CapsNet: Capsule Network with Self-Attention Routing}},
  journal   = {Scientific Reports},
  volume    = {11},
  number    = {1},
  pages     = {14634},
  year      = {2021}
}

@article{hu2023beta,
  author    = {Hu, Mei-feng and Liu, Jiu-wen},
  title     = {{$\beta$-CapsNet: Learning Disentangled Representation for CapsNet by Information Bottleneck}},
  journal   = {Neural Computing and Applications},
  volume    = {35},
  number    = {3},
  pages     = {2503--2525},
  year      = {2023}
}

@inproceedings{locatello2020object,
  author    = {Locatello, Francesco and Weissenborn, Dirk and Unterthiner, Thomas and Mahendran, Aravind and Heigold, Georg and Uszkoreit, Jakob and Dosovitskiy, Alexey and Kipf, Thomas},
  title     = {{Object-Centric Learning with Slot Attention}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {33},
  pages     = {11525--11538},
  year      = {2020},
  publisher = {Curran Associates, Inc.}
}

@inproceedings{seitzer2022bridging,
  author    = {Seitzer, Maximilian and Horn, Max and Zadaianchuk, Andrii and Zietlow, Dominik and Xiao, Tian and Simon-Gabriel, Carl-Johann and He, Thomas and Zhang, Zheng and Schölkopf, Bernhard and Brox, Thomas and others},
  title     = {{Bridging the Gap to Real-World Object-Centric Learning}},
  booktitle = {The Eleventh International Conference on Learning Representations (ICLR)},
  year      = {2023} 
}

@inproceedings{singh2022simple,
  author    = {Singh, Gautam and Wu, Yi-Fu and Ahn, Sungjin},
  title     = {{Simple Unsupervised Object-Centric Learning for Complex and Naturalistic Videos}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {35},
  pages     = {18181--18196},
  year      = {2022},
  publisher = {Curran Associates, Inc.}
}

@inproceedings{elsayed2022savi++,
  author    = {Elsayed, Gamaleldin and Mahendran, Aravind and van Steenkiste, Sjoerd and Greff, Klaus and Mozer, Michael C. and Kipf, Thomas},
  title     = {{SAVi++: Towards End-to-End Object-Centric Learning from Real-World Videos}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {35},
  pages     = {28940--28954},
  year      = {2022},
  publisher = {Curran Associates, Inc.}
}

@inproceedings{sylvain2021object,
  author    = {Sylvain, Tristan and Zhang, Peng and Bengio, Yoshua and Hjelm, R. Devon and Sharma, Shikhar},
  title     = {{Object-Centric Image Generation From Layouts}},
  booktitle = {Proceedings of the AAAI Conference on Artificial Intelligence},
  volume    = {35},
  number    = {3},
  pages     = {2647--2655},
  year      = {2021}
}

@inproceedings{cheng2021boundary,
  author    = {Cheng, Bowen and Girshick, Ross and Dollár, Piotr and Berg, Alexander C. and Kirillov, Alexander},
  title     = {{Boundary IoU: Improving Object-Centric Image Segmentation Evaluation}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {15334--15342},
  year      = {2021}
}

@article{wu2022slotformer,
  author        = {Wu, Ziyi and Dvornik, Nikita and Greff, Klaus and Kipf, Thomas and Garg, Animesh},
  title         = {{SlotFormer: Unsupervised Visual Dynamics Simulation with Object-Centric Models}},
  year          = {2022},
  eprint        = {2210.05861},
  archivePrefix = {arXiv},
  primaryClass  = {cs.CV}
}

@inproceedings{ferraro2022disentangling,
  author    = {Ferraro, Skander and Van de Maele, Toon and Mazzaglia, Pietro and Verbelen, Tim and Dhoedt, Bart},
  title     = {{Disentangling Shape and Pose for Object-Centric Deep Active Inference Models}},
  booktitle = {International Workshop on Active Inference},
  pages     = {32--49},
  year      = {2022},
  publisher = {Springer}
}

@inproceedings{li2020learning,
  author    = {Li, Nanbo and Eastwood, Cian and Fisher, Robert},
  title     = {{Learning Object-Centric Representations of Multi-Object Scenes From Multiple Views}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {33},
  pages     = {5656--5666},
  year      = {2020},
  publisher = {Curran Associates, Inc.}
}

@article{zaidi2020measuring,
  author        = {Zaidi, Junaid and Boilard, Jonathan and Gagnon, Guillaume and Carbonneau, Marc-André},
  title         = {{Measuring Disentanglement: A Review of Metrics}},
  year          = {2020},
  eprint        = {2012.09276},
  archivePrefix = {arXiv},
  primaryClass  = {cs.LG}
}

@inproceedings{eastwood2018framework,
  author    = {Eastwood, Cian and Williams, Christopher K. I.},
  title     = {{A Framework for the Quantitative Evaluation of Disentangled Representations}},
  booktitle = {International Conference on Learning Representations (ICLR)},
  year      = {2018}
}

@inproceedings{ridgeway2018learning,
  author    = {Ridgeway, Kiri and Mozer, Michael C.},
  title     = {{Learning Deep Disentangled Embeddings With the F-Statistic Loss}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {31},
  year      = {2018},
  publisher = {Curran Associates, Inc.}
}

@inproceedings{tokui2022disentanglement,
  author    = {Tokui, Seiya and Sato, Issei},
  title     = {{Disentanglement Analysis with Partial Information Decomposition}},
  booktitle = {International Conference on Learning Representations (ICLR)},
  year      = {2022},
  url       = {https://openreview.net/forum?id=pETy-HVvGtt}
}

@article{do2019theory,
  author        = {Do, Kien and Tran, Truyen},
  title         = {{Theory and Evaluation Metrics for Learning Disentangled Representations}},
  year          = {2019},
  eprint        = {1908.09961},
  archivePrefix = {arXiv},
  primaryClass  = {cs.LG}
}

@inproceedings{wu2021stylespace,
  author    = {Wu, Zongze and Lischinski, Dani and Shechtman, Eli},
  title     = {{StyleSpace Analysis: Disentangled Controls for StyleGAN Image Generation}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages     = {12863--12872},
  year      = {2021}
}

@inproceedings{Karras_2019_CVPR,
  author    = {Karras, Tero and Laine, Samuli and Aila, Timo},
  title     = {{A Style-Based Generator Architecture for Generative Adversarial Networks}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  month     = {June},
  year      = {2019}
}

@inproceedings{zeng2020realistic,
  author    = {Zeng, Xiao and Pan, Yan and Wang, Mengmeng and Zhang, Jun and Liu, Yong},
  title     = {{Realistic Face Reenactment via Self-Supervised Disentangling of Identity and Pose}},
  booktitle = {Proceedings of the AAAI Conference on Artificial Intelligence},
  volume    = {34},
  number    = {07},
  pages     = {12757--12764},
  year      = {2020}
}

@inproceedings{sanchez2020learning,
  author    = {Sanchez, Elies Héron and Serrurier, Mathieu and Ortner, Mathias},
  title     = {{Learning Disentangled Representations via Mutual Information Estimation}},
  booktitle = {European Conference on Computer Vision (ECCV)},
  pages     = {205--221},
  year      = {2020},
  publisher = {Springer}
}

@inproceedings{Hamaguchi_2019_CVPR,
  author    = {Hamaguchi, Ryuhei and Sakurada, Ken and Nakamura, Ryosuke},
  title     = {{Rare Event Detection using Disentangled Representation Learning}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  month     = {June},
  year      = {2019}
}

@inproceedings{gidaris2018unsupervised,
  author    = {Gidaris, Spyros and Singh, Praveer and Komodakis, Nikos},
  title     = {{Unsupervised Representation Learning by Predicting Image Rotations}},
  booktitle = {International Conference on Learning Representations (ICLR)},
  year      = {2018}
}

@inproceedings{Feng_2019_CVPR,
  author    = {Feng, Zhiqiu and Xu, Chang and Tao, Dacheng},
  title     = {{Self-Supervised Representation Learning by Rotation Feature Decoupling}},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  month     = {June},
  year      = {2019}
}

@inproceedings{sreekar2021mutual,
  author    = {Sreekar, P. A. and Tiwari, Utsav and Namboodiri, Anoop},
  title     = {{Mutual Information Based Method for Unsupervised Disentanglement of Video Representation}},
  booktitle = {2020 25th International Conference on Pattern Recognition (ICPR)},
  pages     = {6396--6403},
  year      = {2021},
  publisher = {IEEE}
}

@inproceedings{hsieh2018learning,
  author    = {Hsieh, Jun-Ting and Liu, Bingbin and Huang, De-An and Fei-Fei, Li F. and Niebles, Juan Carlos},
  title     = {{Learning to Decompose and Disentangle Representations for Video Prediction}},
  booktitle = {Advances in Neural Information Processing Systems},
  volume    = {31},
  year      = {2018},
  publisher = {Curran Associates, Inc.}
}

@inproceedings{kim2020robust,
  author    = {Kim, Minyoung and Lee, Ha Neul Julia and Lee, Sangmin and Ro, Yong Man},
  title     = {{Robust Video Facial Authentication with Unsupervised Mode Disentanglement}},
  booktitle = {2020 IEEE International Conference on Image Processing (ICIP)},
  pages     = {1321--1325},
  year      = {2020},
  publisher = {IEEE}
}

@article{ma2022human,
  author        = {Ma, Jing and Yu, Shuang},
  title         = {{Human Identity-Preserved Motion Retargeting in Video Synthesis by Feature Disentanglement}},
  year          = {2022},
  eprint        = {2204.06862},
  archivePrefix = {arXiv},
  primaryClass  = {cs.CV}
}

@inproceedings{colombo2021novel,
  author    = {Colombo, Pietro and Piantanida, Pablo and Clavel, Chloé},
  title     = {{A Novel Estimator of Mutual Information for Learning to Disentangle Textual Representations}},
  booktitle = {Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing (Volume 1: Long Papers)},
  pages     = {6539--6550},
  year      = {2021},
  publisher = {Association for Computational Linguistics}
}

@inproceedings{hu2017toward,
  author    = {Hu, Zhiting and Yang, Zichao and Liang, Xiaodan and Salakhutdinov, Ruslan and Xing, Eric P.},
  title     = {{Toward Controlled Generation of Text}},
  booktitle = {International Conference on Machine Learning (ICML)},
  pages     = {1587--1596},
  year      = {2017},
  publisher = {PMLR}
}

@inproceedings{john2019disentangled,
  author    = {John, Vineet and Mou, Lili and Bahuleyan, Hareesh and Vechtomova, Olga},
  title     = {{Disentangled Representation Learning for Non-Parallel Text Style Transfer}},
  booktitle = {Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics},
  pages     = {424--434},
  year      = {2019},
  publisher = {Association for Computational Linguistics}
}

@inproceedings{zou2022divide,
  author    = {Zou, Yixin and Liu, Hongwei and Gui, Tao and Wang, Jun and Zhang, Qi and Tang, Minlong and Li, Haichao and Wang, Dong},
  title     = {{Divide and Conquer: Text Semantic Matching with Disentangled Keywords and Intents}},
  booktitle = {Findings of the Association for Computational Linguistics: ACL 2022},
  pages     = {3622--3632},
  year      = {2022},
  publisher = {Association for Computational Linguistics}
}

@inproceedings{dougrez2021learning,
  author    = {Dougrez-Lewis, Jeremy and Liakata, Maria and Kochkina, Elena and He, Yulan},
  title     = {{Learning Disentangled Latent Topics for Twitter Rumour Veracity Classification}},
  booktitle = {Findings of the Association for Computational Linguistics: ACL-IJCNLP 2021},
  pages     = {3902--3908},
  year      = {2021},
  publisher = {Association for Computational Linguistics}
}

@inproceedings{zhu2021neural,
  author    = {Zhu, Qingfu and Zhang, Wei and Liu, Ting and Wang, William Yang},
  title     = {{Neural Stylistic Response Generation with Disentangled Latent Variables}},
  booktitle = {Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing (Volume 1: Long Papers)},
  pages     = {2670--2681},  
  year      = {2021},
  publisher = {Association for Computational Linguistics}
}










@article{SaeediMEJ10,
            author = {Mehdi Saeedi and Morteza Saheb Zamani and Mehdi Sedighi},
            title = {A library-based synthesis methodology for reversible logic},
            journal = {Microelectron. J.},
            volume = {41},
            number = {4},
            month = apr,
            year = {2010},
            pages = {185--194},
}

@ARTICLE{SaeediJETC10,
            author = {Mehdi Saeedi and Morteza Saheb Zamani and Mehdi Sedighi and Zahra Sasanian},
            title = {Synthesis of Reversible Circuit Using Cycle-Based Approach},
            journal = {J. Emerg. Technol. Comput. Syst.},
            volume = {6},
            number = {4},
            month = dec,
            year = {2010}
            }

% Asad's new version
@article{Kirschmer:2010:AEI:1958016.1958018,
 author = {Kirschmer, Markus and Voight, John},
 title = {Algorithmic Enumeration of Ideal Classes for Quaternion Orders},
 journal = {SIAM J. Comput.},
 issue_date = {January 2010},
 volume = {39},
 number = {5},
 month = jan,
 year = {2010},
 issn = {0097-5397},
 pages = {1714--1747},
 numpages = {34},
 url = {http://dx.doi.org/10.1137/080734467},
 doi = {https://doi.org/10.1137/080734467},
 acmid = {1958018},
 publisher = {Society for Industrial and Applied Mathematics},
 address = {Philadelphia, PA, USA},
 keywords = {ideal classes, maximal orders, number theory, quaternion algebras},
}


% incol due to presence of booktitle
@incollection{Hoare:1972:CIN:1243380.1243382,
 author = {Hoare, C. A. R.},
 title = {Chapter II: Notes on data structuring},
 booktitle = {Structured programming (incoll)},
 editor = {Dahl, O. J. and Dijkstra, E. W. and Hoare, C. A. R.},
 year = {1972},
 isbn = {0-12-200550-3},
 pages = {83--174},
 numpages = {92},
 url = {http://portal.acm.org/citation.cfm?id=1243380.1243382},
 acmid = {1243382},
 publisher = {Academic Press Ltd.},
 address = {London, UK, UK},
}

% incol due to presence of booktitle
@incollection{Lee:1978:TQA:800025.1198348,
 author = {Lee, Jan},
 title = {Transcript of question and answer session},
 booktitle = {History of programming languages I (incoll)},
 editor = {Wexelblat, Richard L.},
 year = {1981},
 isbn = {0-12-745040-8},
 pages = {68--71},
 numpages = {4},
 url = {http://doi.acm.org/10.1145/800025.1198348},
 doi = {http://doi.acm.org/10.1145/800025.1198348},
 acmid = {1198348},
 publisher = {ACM},
 address = {New York, NY, USA},
}

% incol due to booktitle
@incollection{Dijkstra:1979:GSC:1241515.1241518,
 author = {Dijkstra, E.},
 title = {Go to statement considered harmful},
 booktitle = {Classics in software engineering (incoll)},
 year = {1979},
 isbn = {0-917072-14-6},
 pages = {27--33},
 numpages = {7},
 url = {http://portal.acm.org/citation.cfm?id=1241515.1241518},
 acmid = {1241518},
 publisher = {Yourdon Press},
 address = {Upper Saddle River, NJ, USA},
}

% incol due to booktitle
@incollection{Wenzel:1992:TVA:146022.146089,
 author = {Wenzel, Elizabeth M.},
 title = {Three-dimensional virtual acoustic displays},
 booktitle = {Multimedia interface design (incoll)},
 year = {1992},
 isbn = {0-201-54981-6},
 pages = {257--288},
 numpages = {32},
 url = {http://portal.acm.org/citation.cfm?id=146022.146089},
 doi = {10.1145/146022.146089},
 acmid = {146089},
 publisher = {ACM},
 address = {New York, NY, USA},
}

% incol due to booktitle
@incollection{Mumford:1987:MES:54905.54911,
 author = {Mumford, E.},
 title = {Managerial expert systems and organizational change: some critical research issues},
 booktitle = {Critical issues in information systems research (incoll)},
 year = {1987},
 isbn = {0-471-91281-6},
 pages = {135--155},
 numpages = {21},
 url = {http://portal.acm.org/citation.cfm?id=54905.54911},
 acmid = {54911},
 publisher = {John Wiley \& Sons, Inc.},
 address = {New York, NY, USA},
}

@book{McCracken:1990:SSC:575315,
 author = {McCracken, Daniel D. and Golden, Donald G.},
 title = {Simplified Structured COBOL with Microsoft/MicroFocus COBOL},
 year = {1990},
 isbn = {0471514071},
 publisher = {John Wiley \& Sons, Inc.},
 address = {New York, NY, USA},
}

% Let's include Boris / BBeeton entries  (multi-volume works)

@book {MR781537,
    AUTHOR = {H{\"o}rmander, Lars},
     TITLE = {The analysis of linear partial differential operators. {III}},
    SERIES = {Grundlehren der Mathematischen Wissenschaften [Fundamental
              Principles of Mathematical Sciences]},
    VOLUME = {275},
      NOTE = {Pseudodifferential operators},
PUBLISHER = {Springer-Verlag},
   ADDRESS = {Berlin, Germany},
      YEAR = {1985},
     PAGES = {viii+525},
      ISBN = {3-540-13828-5},
   MRCLASS = {35-02 (35Sxx 47G05 58G15)},
  MRNUMBER = {781536 (87d:35002a)},
MRREVIEWER = {Min You Qi},
}

@book {MR781536,
    AUTHOR = {H{\"o}rmander, Lars},
     TITLE = {The analysis of linear partial differential operators. {IV}},
    SERIES = {Grundlehren der Mathematischen Wissenschaften [Fundamental
              Principles of Mathematical Sciences]},
    VOLUME = {275},
      NOTE = {Fourier integral operators},
PUBLISHER = {Springer-Verlag},
   ADDRESS = {Berlin, Germany},
      YEAR = {1985},
     PAGES = {vii+352},
      ISBN = {3-540-13829-3},
   MRCLASS = {35-02 (35Sxx 47G05 58G15)},
  MRNUMBER = {781537 (87d:35002b)},
MRREVIEWER = {Min You Qi},
}

%%%%%%%%%%%%%%%%%%%%%% Start of Aptara sample bib entries

% acmsmall-sam.bib
@InProceedings{Adya-01,
  author        = {A. Adya and P. Bahl and J. Padhye and A.Wolman and L. Zhou},
  title         = {A multi-radio unification protocol for {IEEE} 802.11 wireless networks},
  booktitle     = {Proceedings of the IEEE 1st International Conference on Broadnets Networks (BroadNets'04)},
  publisher     = "IEEE",
  address       = "Los Alamitos, CA",
  year          = {2004},
  pages         = "210--217"
}

@article{Akyildiz-01,
  author        = {I. F. Akyildiz and W. Su and Y. Sankarasubramaniam and E. Cayirci},
  title         = {Wireless Sensor Networks: A Survey},
  journal       = {Comm. ACM},
  volume        = 38,
  number        = "4",
  year          = {2002},
  pages         = "393--422"
}

@article{Akyildiz-02,
  author        = {I. F. Akyildiz and T. Melodia and K. R. Chowdhury},
  title         = {A Survey on Wireless Multimedia Sensor Networks},
  journal       = {Computer Netw.},
  volume        = 51,
  number        = "4",
  year          = {2007},
  pages         = "921--960"
}

@InProceedings{Bahl-02,
  author        = {P. Bahl and R. Chancre and J. Dungeon},
  title         = {{SSCH}: Slotted Seeded Channel Hopping for Capacity Improvement in {IEEE} 802.11 Ad-Hoc Wireless Networks},
  booktitle     = {Proceeding of the 10th International Conference on Mobile Computing and Networking (MobiCom'04)},
  publisher     = "ACM",
  address       = "New York, NY",
  year          = {2004},
  pages         = "112--117"
}

@misc{CROSSBOW,
  key       = {CROSSBOW},
  title     = {{XBOW} Sensor Motes Specifications},
  note      = {http://www.xbow.com},
  year      = 2008
}

@article{Culler-01,
  author        = {D. Culler and D. Estrin and M. Srivastava},
  title         = {Overview of Sensor Networks},
  journal       = {IEEE Comput.},
  volume        = 37,
  number        = "8 (Special Issue on Sensor Networks)",
  publisher     = "IEEE",
  address       = "Los Alamitos, CA",
  year          = {2004},
  pages         = "41--49"
}

@misc{Harvard-01,
    key         = {Harvard CodeBlue},
    title       = {{CodeBlue}: Sensor Networks for Medical Care},
    note        = {http://www.eecs.harvard.edu/mdw/ proj/codeblue/},
    year        = 2008
}

@InProceedings{Natarajan-01,
    author      = {A. Natarajan and M. Motani and B. de Silva and K. Yap and K. C. Chua},
    title       = {Investigating Network Architectures for Body Sensor Networks},
    booktitle   = {Network Architectures},
    editor      = {G. Whitcomb and P. Neece},
    publisher   = "Keleuven Press",
    address     = "Dayton, OH",
    year        = {2007},
    pages       = "322--328",
    eprint      = "960935712",
    primaryclass = "cs",
}

@techreport{Tzamaloukas-01,
  author        = {A. Tzamaloukas and J. J. Garcia-Luna-Aceves},
  title         = {Channel-Hopping Multiple Access},
  number =        {I-CA2301},
  institution =   {Department of Computer Science, University of California},
  address =       {Berkeley, CA},
  year          = {2000}
}

@BOOK{Zhou-06,
  author        = {G. Zhou and J. Lu and C.-Y. Wan and M. D. Yarvis and J. A. Stankovic},
  title         = {Body Sensor Networks},
  publisher     = "MIT Press",
  address       = "Cambridge, MA",
  year          = {2008}
}

@mastersthesis{ko94,
author = "Jacob Kornerup",
title = "Mapping Powerlists onto Hypercubes",
school = "The University of Texas at Austin",
note = "(In preparation)",
year = "1994"}
%month = "dec",}

@PhdThesis{gerndt:89,
  author =       "Michael Gerndt",
  title =        "Automatic Parallelization for Distributed-Memory
                  Multiprocessing Systems",
  school =       "University of Bonn",
  year =         1989,
  address =      "Bonn, Germany",
  month =        dec
}

@article{6:1:1,
author = "J. E. {Archer, Jr.} and R. Conway and F. B. Schneider",
title = "User recovery and reversal in interactive systems",
journal = "ACM Trans. Program. Lang. Syst.",
volume =  "6",
number = "1",
month = jan,
year = 1984,
pages = "1--19"}

@article{7:1:137,
author = "D. D. Dunlop and V. R. Basili",
title = "Generalizing specifications for uniformly implemented loops",
journal = "ACM Trans. Program. Lang. Syst.",
volume =  "7",
number = "1",
month = jan,
year = 1985,
pages = "137--158"}

@article{7:2:183,
author = "J. Heering and P. Klint",
title = "Towards monolingual programming environments",
journal = "ACM Trans. Program. Lang. Syst.",
volume =  "7",
number = "2",
month = apr,
year = 1985,
pages = "183--213"}

@book{knuth:texbook,
author = "Donald E. Knuth",
title = "The {\TeX{}book}",
publisher = "Addison-Wesley",
address = "Reading, MA.",
year = 1984}

@article{6:3:380,
author = "E. Korach and D.  Rotem and N. Santoro",
title = "Distributed algorithms for finding centers and medians in networks",
journal = "ACM Trans. Program. Lang. Syst.",
volume =  "6",
number = "3",
month = jul,
year = 1984,
pages = "380--401"}

@book{Lamport:LaTeX,
author = "Leslie Lamport",
title = "\it {\LaTeX}: A Document Preparation System",
publisher = "Addison-Wesley",
address = "Reading, MA.",
year = 1986}

@article{7:3:359,
author = "F. Nielson",
title = "Program transformations in a denotational setting",
journal = "ACM Trans. Program. Lang. Syst.",
volume =  "7",
number = "3",
month = jul,
year = 1985,
pages = "359--379"}

%testing
@BOOK{test,
   author = "Donald E. Knuth",
   title = "Seminumerical Algorithms",
   volume = 2,
   series = "The Art of Computer Programming",
   publisher = "Addison-Wesley",
   address = "Reading, MA",
   edition = "2nd",
   month = "10~" # jan,
   year = "1981",
}

@inproceedings{reid:scribe,
author = "Brian K. Reid",
title = "A high-level approach to computer document formatting",
booktitle = "Proceedings of the 7th Annual Symposium on Principles of
  Programming Languages",
month = jan,
year = 1980,
publisher = "ACM",
address = "New York",
pages = "24--31"}

@article{Zhou:2010:MMS:1721695.1721705,
 author = {Zhou, Gang and Wu, Yafeng and Yan, Ting and He, Tian and Huang, Chengdu and Stankovic, John A. and Abdelzaher, Tarek F.},
 title = {A multifrequency MAC specially designed for wireless sensor network applications},
 journal = {ACM Trans. Embed. Comput. Syst.},
 issue_date = {March 2010},
 volume = 9,
 number = 4,
 month = {April},
 year = 2010,
 issn = {1539-9087},
 pages = {39:1--39:41},
 articleno = 39,
 numpages = 41,
 url = {http://doi.acm.org/10.1145/1721695.1721705},
 doi = {10.1145/1721695.1721705},
 acmid = 1721705,
 publisher = {ACM},
 address = {New York, NY, USA},
 keywords = {Wireless sensor networks, media access control, multi-channel, radio interference, time synchronization},
}


@online{TUGInstmem,
  key =          {TUG},
  year  =        2017,
  title =        "Institutional members of the {\TeX} Users Group",
  url =          "http://wwtug.org/instmem.html",
  lastaccessed = "May 27, 2017",
}

@online{CTANacmart,
  author =    {Boris Veytsman},
  title =  {acmart---{Class} for typesetting publications of {ACM}},
  year = 2017,
  url =    {http://www.ctan.org/pkg/acmart},
  lastaccessed = {May 27, 2017}
  }

@online{doclicense,
  author =    {Robin Schneider},
  title =  {The \textsl{doclicense} package},
  year = 2022,
  url =    {http://www.ctan.org/pkg/doclicense},
  lastaccessed = {May 27, 2022}
  }

@ARTICLE{bowman:reasoning,
    author = {Bowman, Mic and Debray, Saumya K. and Peterson, Larry L.},
    title = {Reasoning About Naming Systems},
    journal = {ACM Trans. Program. Lang. Syst.},
    volume = {15},
    number = {5},
    pages = {795-825},
    month = {November},
    year = {1993},
    doi = {10.1145/161468.161471},
}

@ARTICLE{braams:babel,
    author = {Braams, Johannes},
    title = {Babel, a Multilingual Style-Option System for Use with LaTeX's Standard Document Styles},
    journal = {TUGboat},
    volume = {12},
    number = {2},
    pages = {291-301},
    month = {June},
    year = {1991},
}

@INPROCEEDINGS{clark:pct,
  AUTHOR = "Malcolm Clark",
  TITLE = "Post Congress Tristesse",
  BOOKTITLE = "TeX90 Conference Proceedings",
  PAGES = "84-89",
  ORGANIZATION = "TeX Users Group",
  MONTH = "March",
  YEAR = {1991}
}

@ARTICLE{herlihy:methodology,
    author = {Herlihy, Maurice},
    title = {A Methodology for Implementing Highly Concurrent Data Objects},
    journal = {ACM Trans. Program. Lang. Syst.},
    volume = {15},
    number = {5},
    pages = {745-770},
    month = {November},
    year = {1993},
    doi = {10.1145/161468.161469},
}

@BOOK{salas:calculus,
  AUTHOR = "S.L. Salas and Einar Hille",
  TITLE = "Calculus: One and Several Variable",
  PUBLISHER = "John Wiley and Sons",
  ADDRESS = "New York",
  YEAR = "1978"
}

@MANUAL{Fear05,
  title =        {Publication quality tables in {\LaTeX}},
  author =       {Simon Fear},
  month =        {April},
  year =         2005,
  note =         {\url{http://www.ctan.org/pkg/booktabs}}
}

@Manual{Amsthm15,
  title =        {Using the amsthm Package},
  organization = {American Mathematical Society},
  month =        {April},
  year =         2015,
  note =         {\url{http://www.ctan.org/pkg/amsthm}}
}

@ArtifactSoftware{R,
    title = {R: A Language and Environment for Statistical Computing},
    author = {{R Core Team}},
    organization = {R Foundation for Statistical Computing},
    address = {Vienna, Austria},
    year = {2019},
    url = {https://www.R-project.org/},
}

@ArtifactDataset{UMassCitations,
 author    =  {Sam Anzaroot and Andrew McCallum},
 title     =  {{UMass} Citation Field Extraction Dataset},
 year      = 2013,
 url       =
    {http://www.iesl.cs.umass.edu/data/data-umasscitationfield},
 lastaccessed = {May 27, 2019}
}

@Eprint{Bornmann2019,
       author = {Bornmann, Lutz and Wray, K. Brad and Haunschild,
                  Robin},
        title = {Citation concept analysis {(CCA)}---A new form of
                  citation analysis revealing the usefulness of
                  concepts for other researchers illustrated by two
                  exemplary case studies including classic books by
                  {Thomas S.~Kuhn} and {Karl R.~Popper}},
     keywords = {Computer Science - Digital Libraries},
         year = 2019,
        month = "May",
          eid = {arXiv:1905.12410},
archivePrefix = {arXiv},
       eprint = {1905.12410},
 primaryClass = {cs.DL},
}

@Eprint{AnzarootPBM14,
  author    = {Sam Anzaroot and
               Alexandre Passos and
               David Belanger and
               Andrew McCallum},
  title     = {Learning Soft Linear Constraints with Application to
                  Citation Field Extraction},
  year      = {2014},
  archivePrefix = {arXiv},
  eprint    = {1403.1349},
}

@inproceedings{Hagerup1993,
title        = {Maintaining Discrete Probability Distributions Optimally},
author       = {Hagerup, Torben and Mehlhorn, Kurt and Munro, J. Ian},
booktitle    = {Proceedings of the 20th International Colloquium on Automata, Languages and Programming},
series       = {Lecture Notes in Computer Science},
volume       = {700},
pages        = {253--264},
year         = {1993},
publisher    = {Springer-Verlag},
address      = {Berlin},
}

\begin{thebibliography}{100}

@inproceedings{gheissari2006person,
  title={Person reidentification using spatiotemporal appearance},
  author={Gheissari, Niloofar and Sebastian, Thomas B and Hartley, Richard},
  booktitle={2006 IEEE computer society conference on computer vision and pattern recognition (CVPR'06)},
  volume={2},
  pages={1528--1535},
  year={2006},
  organization={IEEE}
}

@inproceedings{zhang2016learning,
  title={Learning a discriminative null space for person re-identification},
  author={Zhang, Li and Xiang, Tao and Gong, Shaogang},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={1239--1248},
  year={2016}
}
@inproceedings{su2017pose,
  title={Pose-driven deep convolutional model for person re-identification},
  author={Su, Chi and Li, Jianing and Zhang, Shiliang and Xing, Junliang and Gao, Wen and Tian, Qi},
  booktitle={Proceedings of the IEEE international conference on computer vision},
  pages={3960--3969},
  year={2017}
}
@inproceedings{zhao2017spindle,
  title={Spindle net: Person re-identification with human body region guided feature decomposition and fusion},
  author={Zhao, Haiyu and Tian, Maoqing and Sun, Shuyang and Shao, Jing and Yan, Junjie and Yi, Shuai and Wang, Xiaogang and Tang, Xiaoou},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={1077--1085},
  year={2017}
}
@article{ge2018fd,
  title={Fd-gan: Pose-guided feature distilling gan for robust person re-identification},
  author={Ge, Yixiao and Li, Zhuowan and Zhao, Haiyu and Yin, Guojun and Yi, Shuai and Wang, Xiaogang and others},
  journal={Advances in neural information processing systems},
  volume={31},
  year={2018}
}

@inproceedings{zhang2019densely,
  title={Densely semantically aligned person re-identification},
  author={Zhang, Zhizheng and Lan, Cuiling and Zeng, Wenjun and Chen, Zhibo},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={667--676},
  year={2019}
}
@inproceedings{jin2020semantics,
  title={Semantics-aligned representation learning for person re-identification},
  author={Jin, Xin and Lan, Cuiling and Zeng, Wenjun and Wei, Guoqiang and Chen, Zhibo},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  volume={34},
  number={07},
  pages={11173--11180},
  year={2020}
}
@inproceedings{jin2020uncertainty,
  title={Uncertainty-aware multi-shot knowledge distillation for image-based object re-identification},
  author={Jin, Xin and Lan, Cuiling and Zeng, Wenjun and Chen, Zhibo},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  volume={34},
  number={07},
  pages={11165--11172},
  year={2020}
}

@inproceedings{li2014deepreid,
  title={Deepreid: Deep filter pairing neural network for person re-identification},
  author={Li, Wei and Zhao, Rui and Xiao, Tong and Wang, Xiaogang},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={152--159},
  year={2014}
}

@inproceedings{zhou2019omni,
  title={Omni-scale feature learning for person re-identification},
  author={Zhou, Kaiyang and Yang, Yongxin and Cavallaro, Andrea and Xiang, Tao},
  booktitle={Proceedings of the IEEE/CVF international conference on computer vision},
  pages={3702--3712},
  year={2019}
}

@inproceedings{dai2022cluster,
  title={Cluster contrast for unsupervised person re-identification},
  author={Dai, Zuozhuo and Wang, Guangyuan and Yuan, Weihao and Zhu, Siyu and Tan, Ping},
  booktitle={Proceedings of the Asian conference on computer vision},
  pages={1142--1160},
  year={2022}
}

@inproceedings{khaldi2021cupr,
  title={CUPR: Contrastive Unsupervised Learning for Person Re-identification.},
  author={Khaldi, Khadija and Shah, Shishir K},
  booktitle={VISIGRAPP (5: VISAPP)},
  pages={92--100},
  year={2021}
}

@inproceedings{khaldi2022unsupervised,
  title={Unsupervised person re-identification based on skeleton joints using graph convolutional networks},
  author={Khaldi, Khadija and Mantini, Pranav and Shah, Shishir K},
  booktitle={International Conference on Image Analysis and Processing},
  pages={135--146},
  year={2022},
  organization={Springer}
}

@inproceedings{khaldi2024unsupervised,
  title={Unsupervised person re-identification in aerial imagery},
  author={Khaldi, Khadija and Nguyen, Vuong D and Mantini, Pranav and Shah, Shishir},
  booktitle={Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision},
  pages={260--269},
  year={2024}
}


@article{eom2019learning,
  title={Learning disentangled representation for robust person re-identification},
  author={Eom, Chanho and Ham, Bumsub},
  journal={Advances in neural information processing systems},
  volume={32},
  year={2019}
}

@article{zheng2021calibrated,
  title={Calibrated feature decomposition for generalizable person re-identification},
  author={Zheng, Kecheng and Liu, Jiawei and Wu, Wei and Li, Liang and Zha, Zheng-jun},
  journal={arXiv preprint arXiv:2111.13945},
  year={2021}
}

@inproceedings{zhou2016learning,
  title={Learning deep features for discriminative localization},
  author={Zhou, Bolei and Khosla, Aditya and Lapedriza, Agata and Oliva, Aude and Torralba, Antonio},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={2921--2929},
  year={2016}
}

@inproceedings{liu2019adaptive,
  title={Adaptive transfer network for cross-domain person re-identification},
  author={Liu, Jiawei and Zha, Zheng-Jun and Chen, Di and Hong, Richang and Wang, Meng},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={7202--7211},
  year={2019}
}

@inproceedings{rami2022online,
  title={Online unsupervised domain adaptation for person re-identification},
  author={Rami, Hamza and Ospici, Matthieu and Lathuili{\`e}re, St{\'e}phane},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={3830--3839},
  year={2022}
}

@inproceedings{rami2024source,
  title={Source-guided similarity preservation for online person re-identification},
  author={Rami, Hamza and Giraldo, Jhony H and Winckler, Nicolas and Lathuili{\`e}re, St{\'e}phane},
  booktitle={Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision},
  pages={1711--1720},
  year={2024}
}

@inproceedings{fu2019self,
  title={Self-similarity grouping: A simple unsupervised cross domain adaptation approach for person re-identification},
  author={Fu, Yang and Wei, Yunchao and Wang, Guanshuo and Zhou, Yuqian and Shi, Honghui and Huang, Thomas S},
  booktitle={proceedings of the IEEE/CVF international conference on computer vision},
  pages={6112--6121},
  year={2019}
}

@inproceedings{wang2018transferable,
  title={Transferable joint attribute-identity deep learning for unsupervised person re-identification},
  author={Wang, Jingya and Zhu, Xiatian and Gong, Shaogang and Li, Wei},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={2275--2284},
  year={2018}
}

@inproceedings{li2019cross,
  title={Cross-dataset person re-identification via unsupervised pose disentanglement and adaptation},
  author={Li, Yu-Jhe and Lin, Ci-Siang and Lin, Yan-Bo and Wang, Yu-Chiang Frank},
  booktitle={Proceedings of the IEEE/CVF international conference on computer vision},
  pages={7919--7929},
  year={2019}
}



@inproceedings{huang2020domain,
  title={Domain adaptive attention learning for unsupervised person re-identification},
  author={Huang, Yangru and Peng, Peixi and Jin, Yi and Li, Yidong and Xing, Junliang},
  booktitle={Proceedings of the AAAI conference on artificial intelligence},
  volume={34},
  number={07},
  pages={11069--11076},
  year={2020}
}

@inproceedings{wang2020smoothing,
  title={Smoothing adversarial domain attack and p-memory reconsolidation for cross-domain person re-identification},
  author={Wang, Guangcong and Lai, Jian-Huang and Liang, Wenqi and Wang, Guangrun},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={10568--10577},
  year={2020}
}




@article{pang2022cross,
  title={Cross-domain person re-identification by hybrid supervised and unsupervised learning},
  author={Pang, Zhiqi and Guo, Jifeng and Sun, Wenbo and Xiao, Yanbang and Yu, Ming},
  journal={Applied Intelligence},
  volume={52},
  number={3},
  pages={2987--3001},
  year={2022},
  publisher={Springer}
}

@inproceedings{zheng2019joint,
  title={Joint discriminative and generative learning for person re-identification},
  author={Zheng, Zhedong and Yang, Xiaodong and Yu, Zhiding and Zheng, Liang and Yang, Yi and Kautz, Jan},
  booktitle={proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={2138--2147},
  year={2019}
}

@inproceedings{dai2021generalizable,
  title={Generalizable person re-identification with relevance-aware mixture of experts},
  author={Dai, Yongxing and Li, Xiaotong and Liu, Jun and Tong, Zekun and Duan, Ling-Yu},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={16145--16154},
  year={2021}
}

@inproceedings{lin2021domain,
  title={Domain generalized person re-identification via cross-domain episodic learning},
  author={Lin, Ci-Siang and Cheng, Yuan-Chia and Wang, Yu-Chiang Frank},
  booktitle={2020 25th International Conference on Pattern Recognition (ICPR)},
  pages={6758--6763},
  year={2021},
  organization={IEEE}
}

@inproceedings{xu2022mimic,
  title={Mimic embedding via adaptive aggregation: Learning generalizable person re-identification},
  author={Xu, Boqiang and Liang, Jian and He, Lingxiao and Sun, Zhenan},
  booktitle={European Conference on Computer Vision},
  pages={372--388},
  year={2022},
  organization={Springer}
}

@inproceedings{choi2021meta,
  title={Meta batch-instance normalization for generalizable person re-identification},
  author={Choi, Seokeon and Kim, Taekyung and Jeong, Minki and Park, Hyoungseob and Kim, Changick},
  booktitle={Proceedings of the IEEE/CVF conference on Computer Vision and Pattern Recognition},
  pages={3425--3435},
  year={2021}
}




@article{chen2023cluster,
  title={Cluster-instance normalization: A statistical relation-aware normalization for generalizable person re-identification},
  author={Chen, Zining and Wang, Weiqiu and Zhao, Zhicheng and Su, Fei and Men, Aidong and Dong, Yuan},
  journal={IEEE Transactions on Multimedia},
  year={2023},
  publisher={IEEE}
}

@inproceedings{jiao2022dynamically,
  title={Dynamically transformed instance normalization network for generalizable person re-identification},
  author={Jiao, Bingliang and Liu, Lingqiao and Gao, Liying and Lin, Guosheng and Yang, Lu and Zhang, Shizhou and Wang, Peng and Zhang, Yanning},
  booktitle={European conference on computer vision},
  pages={285--301},
  year={2022},
  organization={Springer}
}



@article{zhang2022learning,
  title={Learning domain invariant representations for generalizable person re-identification},
  author={Zhang, Yi-Fan and Zhang, Zhang and Li, Da and Jia, Zhen and Wang, Liang and Tan, Tieniu},
  journal={IEEE Transactions on Image Processing},
  volume={32},
  pages={509--523},
  year={2022},
  publisher={IEEE}
}

@inproceedings{wei2018person,
  title={Person transfer gan to bridge domain gap for person re-identification},
  author={Wei, Longhui and Zhang, Shiliang and Gao, Wen and Tian, Qi},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={79--88},
  year={2018}
}

@inproceedings{zheng2015scalable,
  title={Scalable person re-identification: A benchmark},
  author={Zheng, Liang and Shen, Liyue and Tian, Lu and Wang, Shengjin and Wang, Jingdong and Tian, Qi},
  booktitle={Proceedings of the IEEE international conference on computer vision},
  pages={1116--1124},
  year={2015}
}

@inproceedings{ristani2016performance,
  title={Performance measures and a data set for multi-target, multi-camera tracking},
  author={Ristani, Ergys and Solera, Francesco and Zou, Roger and Cucchiara, Rita and Tomasi, Carlo},
  booktitle={European conference on computer vision},
  pages={17--35},
  year={2016},
  organization={Springer}
}

@inproceedings{liao2020interpretable,
  title={Interpretable and generalizable person re-identification with query-adaptive convolution and temporal lifting},
  author={Liao, Shengcai and Shao, Ling},
  booktitle={Computer Vision--ECCV 2020: 16th European Conference, Glasgow, UK, August 23--28, 2020, Proceedings, Part XI 16},
  pages={456--474},
  year={2020},
  organization={Springer}
}


@article{zhou2022learning,
  title={Learning Generalisable Omni-Scale Representations for Person Re-Identification},
  author={Zhou, Kaiyang and Yang, Yongxin and Cavallaro, Andrea and Xiang, Tao},
  journal={IEEE Transactions on Pattern Analysis \& Machine Intelligence},
  volume={44},
  number={09},
  pages={5056--5069},
  year={2022},
  publisher={IEEE Computer Society}
}

@article{qi2022novel,
  title={A novel mix-normalization method for generalizable multi-source person re-identification},
  author={Qi, Lei and Wang, Lei and Shi, Yinghuan and Geng, Xin},
  journal={IEEE Transactions on Multimedia},
  volume={25},
  pages={4856--4867},
  year={2022},
  publisher={IEEE}
}
@article{zhang2023style,
  title={Style uncertainty based self-paced meta learning for generalizable person re-identification},
  author={Zhang, Lei and Liu, Zhipu and Zhang, Wensheng and Zhang, David},
  journal={IEEE Transactions on Image Processing},
  volume={32},
  pages={2107--2119},
  year={2023},
  publisher={IEEE}
}
@inproceedings{chen2017beyond,
  title={Beyond triplet loss: a deep quadruplet network for person re-identification},
  author={Chen, Weihua and Chen, Xiaotang and Zhang, Jianguo and Huang, Kaiqi},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={403--412},
  year={2017}
}
@article{hermans2017defense,
  title={In defense of the triplet loss for person re-identification},
  author={Hermans, Alexander and Beyer, Lucas and Leibe, Bastian},
  journal={arXiv preprint arXiv:1703.07737},
  year={2017}
}
@inproceedings{sun2018beyond,
  title={Beyond part models: Person retrieval with refined part pooling (and a strong convolutional baseline)},
  author={Sun, Yifan and Zheng, Liang and Yang, Yi and Tian, Qi and Wang, Shengjin},
  booktitle={Proceedings of the European conference on computer vision (ECCV)},
  pages={480--496},
  year={2018}
}
@inproceedings{wang2018learning,
  title={Learning discriminative features with multiple granularities for person re-identification},
  author={Wang, Guanshuo and Yuan, Yufeng and Chen, Xiong and Li, Jiwei and Zhou, Xi},
  booktitle={Proceedings of the 26th ACM international conference on Multimedia},
  pages={274--282},
  year={2018}
}
@inproceedings{zheng2019pyramidal,
  title={Pyramidal person re-identification via multi-loss dynamic training},
  author={Zheng, Feng and Deng, Cheng and Sun, Xing and Jiang, Xinyang and Guo, Xiaowei and Yu, Zongqiao and Huang, Feiyue and Ji, Rongrong},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={8514--8522},
  year={2019}
}
@inproceedings{li2018harmonious,
  title={Harmonious attention network for person re-identification},
  author={Li, Wei and Zhu, Xiatian and Gong, Shaogang},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={2285--2294},
  year={2018}
}
@inproceedings{chen2019abd,
  title={Abd-net: Attentive but diverse person re-identification},
  author={Chen, Tianlong and Ding, Shaojin and Xie, Jingyi and Yuan, Ye and Chen, Wuyang and Yang, Yang and Ren, Zhou and Wang, Zhangyang},
  booktitle={Proceedings of the IEEE/CVF international conference on computer vision},
  pages={8351--8361},
  year={2019}
}
@inproceedings{liu2020hierarchical,
  title={Hierarchical bi-directional feature perception network for person re-identification},
  author={Liu, Zhipu and Zhang, Lei and Yang, Yang},
  booktitle={Proceedings of the 28th ACM international conference on multimedia},
  pages={4289--4298},
  year={2020}
}
@inproceedings{chen2019self,
  title={Self-critical attention learning for person re-identification},
  author={Chen, Guangyi and Lin, Chunze and Ren, Liangliang and Lu, Jiwen and Zhou, Jie},
  booktitle={Proceedings of the IEEE/CVF international conference on computer vision},
  pages={9637--9646},
  year={2019}
}
@inproceedings{zhong2019invariance,
  title={Invariance matters: Exemplar memory for domain adaptive person re-identification},
  author={Zhong, Zhun and Zheng, Liang and Luo, Zhiming and Li, Shaozi and Yang, Yi},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={598--607},
  year={2019}
}
@inproceedings{zhong2018generalizing,
  title={Generalizing a person retrieval model hetero-and homogeneously},
  author={Zhong, Zhun and Zheng, Liang and Li, Shaozi and Yang, Yi},
  booktitle={Proceedings of the European conference on computer vision (ECCV)},
  pages={172--188},
  year={2018}
}
@article{zhong2018camstyle,
  title={Camstyle: A novel data augmentation method for person re-identification},
  author={Zhong, Zhun and Zheng, Liang and Zheng, Zhedong and Li, Shaozi and Yang, Yi},
  journal={IEEE Transactions on Image Processing},
  volume={28},
  number={3},
  pages={1176--1190},
  year={2018},
  publisher={IEEE}
}

@article{lin2020multi,
  title={Multi-domain adversarial feature generalization for person re-identification},
  author={Lin, Shan and Li, Chang-Tsun and Kot, Alex C},
  journal={IEEE Transactions on Image Processing},
  volume={30},
  pages={1596--1607},
  year={2020},
  publisher={IEEE}
}

@article{He2015DeepRL,
  title={Deep Residual Learning for Image Recognition},
  author={Kaiming He and X. Zhang and Shaoqing Ren and Jian Sun},
  journal={2016 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)},
  year={2015},
  pages={770-778},
  url={https://api.semanticscholar.org/CorpusID:206594692}
}
@article{van2008visualizing,
  title={Visualizing data using t-SNE.},
  author={Van der Maaten, Laurens and Hinton, Geoffrey},
  journal={Journal of machine learning research},
  volume={9},
  number={11},
  year={2008}
}
@article{Wang2022DGSurvey,
  title={Generalizing to Unseen Domains: A Survey on Domain Generalization},
  author={Wang, Jindong and Cui, Peng and Niu, Wenwu and Hu, Zhiping and Wang, Shuji},
  journal={IEEE Transactions on Knowledge and Data Engineering},
  volume={35},
  number={8},
  pages={7926--7946},
  year={2022},
  publisher={IEEE}
}

@article{Ganin2016DANN,
  title={Domain-adversarial training of neural networks},
  author={Ganin, Yaroslav and Ustinova, Evgeniya and Ajakan, Hana and Germain, Pascal and Larochelle, Hugo and Laviolette, Fran{\c{c}}ois and Marchand, Mario and Lempitsky, Victor},
  journal={Journal of Machine Learning Research},
  volume={17},
  number={59},
  pages={1--35},
  year={2016}
}

@article{Wilson2020UDASurvey,
  title={A survey of unsupervised deep domain adaptation},
  author={Wilson, Gabriela and Cook, Diane J},
  journal={ACM Transactions on Intelligent Systems and Technology (TIST)},
  volume={11},
  number={5},
  pages={1--46},
  year={2020},
  publisher={ACM}
}
@article{jin2021style,
  title={Style normalization and restitution for domain generalization and adaptation},
  author={Jin, Xin and Lan, Cuiling and Zeng, Wenjun and Chen, Zhibo},
  journal={IEEE Transactions on Multimedia},
  volume={24},
  pages={3636--3651},
  year={2021},
  publisher={IEEE}
}
@inproceedings{carlucci2019domain,
  title={Domain generalization by solving jigsaw puzzles},
  author={Carlucci, Fabio M and D'Innocente, Antonio and Bucci, Silvia and Caputo, Barbara and Tommasi, Tatiana},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={2229--2238},
  year={2019}
}
@inproceedings{nam2021reducing,
  title={Reducing domain gap by reducing style bias},
  author={Nam, Hyeonseob and Lee, HyunJae and Park, Jongchan and Yoon, Wonjun and Yoo, Donggeun},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={8690--8699},
  year={2021}
}
@inproceedings{yao2022pcl,
  title={Pcl: Proxy-based contrastive learning for domain generalization},
  author={Yao, Xufeng and Bai, Yang and Zhang, Xinyun and Zhang, Yuechen and Sun, Qi and Chen, Ran and Li, Ruiyu and Yu, Bei},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={7097--7107},
  year={2022}
}
@article{jin2022style,
  title={Style Normalization and Restitution for Domain Generalization and Adaptation},
  author={Jin, Xin and Lan, Cuiling and Zeng, Wenjun and Chen, Zhibo},
  journal={IEEE Transactions on Multimedia},
  volume={24},
  pages={3636--3651},
  year={2022},
  publisher={IEEE}
}
@article{wang2022domain,
  title={Domain generalization and adaptation based on second-order style information},
  author={Wang, Hao and Bi, Xiaojun},
  journal={Pattern Recognition},
  volume={127},
  pages={108595},
  year={2022},
  publisher={Elsevier}
}
@article{segu2023batch,
  title={Batch normalization embeddings for deep domain generalization},
  author={Segu, Mattia and Tonioni, Alessio and Tombari, Federico},
  journal={Pattern Recognition},
  volume={135},
  pages={109115},
  year={2023},
  publisher={Elsevier}
}
@article{niu2023knowledge,
  title={Knowledge distillation-based domain-invariant representation learning for domain generalization},
  author={Niu, Ziwei and Yuan, Junkun and Ma, Xu and Xu, Yingying and Liu, Jing and Chen, Yen-Wei and Tong, Ruofeng and Lin, Lanfen},
  journal={IEEE Transactions on Multimedia},
  year={2023},
  publisher={IEEE}
}
@article{zhou2021domain,
  title={Domain adaptive ensemble learning},
  author={Zhou, Kaiyang and Yang, Yongxin and Qiao, Yu and Xiang, Tao},
  journal={IEEE Transactions on Image Processing},
  volume={30},
  pages={8008--8018},
  year={2021},
  publisher={IEEE}
}
@article{xu2024cbdmoe,
  title={CBDMoE: Consistent-but-Diverse Mixture of Experts for Domain Generalization},
  author={Xu, Fangbin and Chen, Dongyue and Jia, Tong and Deng, Shizhuo and Wang, Hao},
  journal={IEEE Transactions on Multimedia},
  year={2024},
  publisher={IEEE}
}
@article{deng2022dynamic,
  title={Dynamic instance domain adaptation},
  author={Deng, Zhongying and Zhou, Kaiyang and Li, Da and He, Junjun and Song, Yi-Zhe and Xiang, Tao},
  journal={IEEE Transactions on Image Processing},
  volume={31},
  pages={4585--4597},
  year={2022},
  publisher={IEEE}
}
@inproceedings{li2021t,
  title={T-svdnet: Exploring high-order prototypical correlations for multi-source domain adaptation},
  author={Li, Ruihuang and Jia, Xu and He, Jianzhong and Chen, Shuaijun and Hu, Qinghua},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={9991--10000},
  year={2021}
}
@article{ren2022multi,
  title={Multi-source unsupervised domain adaptation via pseudo target domain},
  author={Ren, Chuan-Xian and Liu, Yong-Hui and Zhang, Xi-Wen and Huang, Ke-Kun},
  journal={IEEE Transactions on Image Processing},
  volume={31},
  pages={2122--2135},
  year={2022},
  publisher={IEEE}
}
@inproceedings{wang2022self,
  title={Self-paced Supervision for Multi-source Domain Adaptation.},
  author={Wang, Zengmao and Zhou, Chaoyang and Du, Bo and He, Fengxiang},
  booktitle={IJCAI},
  pages={3551--3557},
  year={2022}
}
@article{wu2023domain,
  title={Domain-specific feature elimination: multi-source domain adaptation for image classification},
  author={Wu, Kunhong and Jia, Fan and Han, Yahong},
  journal={Frontiers of Computer Science},
  volume={17},
  number={4},
  pages={174705},
  year={2023},
  publisher={Springer}
}
@article{wen2024training,
  title={Training multi-source domain adaptation network by mutual information estimation and minimization},
  author={Wen, Lisheng and Chen, Sentao and Xie, Mengying and Liu, Cheng and Zheng, Lin},
  journal={Neural Networks},
  volume={171},
  pages={353--361},
  year={2024},
  publisher={Elsevier}
}
@article{xu2022graphical,
  title={Graphical modeling for multi-source domain adaptation},
  author={Xu, Minghao and Wang, Hang and Ni, Bingbing},
  journal={IEEE Transactions on Pattern Analysis and Machine Intelligence},
  volume={46},
  number={3},
  pages={1727--1741},
  year={2022},
  publisher={IEEE}
}

@inproceedings{vu2019advent,
  title={Advent: Adversarial entropy minimization for domain adaptation in semantic segmentation},
  author={Vu, Tuan-Hung and Jain, Himalaya and Bucher, Maxime and Cord, Matthieu and P{\'e}rez, Patrick},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={2517--2526},
  year={2019}
}
@inproceedings{chen2019domain,
  title={Domain adaptation for semantic segmentation with maximum squares loss},
  author={Chen, Minghao and Xue, Hongyang and Cai, Deng},
  booktitle={Proceedings of the IEEE/CVF international conference on computer vision},
  pages={2090--2099},
  year={2019}
}
@inproceedings{richter2016playing,
  title={Playing for data: Ground truth from computer games},
  author={Richter, Stephan R and Vineet, Vibhav and Roth, Stefan and Koltun, Vladlen},
  booktitle={Computer Vision--ECCV 2016: 14th European Conference, Amsterdam, The Netherlands, October 11-14, 2016, Proceedings, Part II 14},
  pages={102--118},
  year={2016},
  organization={Springer}
}





@String(PAMI = {IEEE Trans. Pattern Anal. Mach. Intell.})
@String(IJCV = {Int. J. Comput. Vis.})
@String(CVPR= {IEEE Conf. Comput. Vis. Pattern Recog.})
@String(ICCV= {Int. Conf. Comput. Vis.})
@String(ECCV= {Eur. Conf. Comput. Vis.})
@String(NIPS= {Adv. Neural Inform. Process. Syst.})
@String(ICPR = {Int. Conf. Pattern Recog.})
@String(BMVC= {Brit. Mach. Vis. Conf.})
@String(TOG= {ACM Trans. Graph.})
@String(TIP  = {IEEE Trans. Image Process.})
@String(TVCG  = {IEEE Trans. Vis. Comput. Graph.})
@String(TMM  = {IEEE Trans. Multimedia})
@String(ACMMM= {ACM Int. Conf. Multimedia})
@String(ICME = {Int. Conf. Multimedia and Expo})
@String(ICASSP=	{ICASSP})
@String(ICIP = {IEEE Int. Conf. Image Process.})
@String(ACCV  = {ACCV})
@String(ICLR = {Int. Conf. Learn. Represent.})
@String(IJCAI = {IJCAI})
@String(PR   = {Pattern Recognition})
@String(AAAI = {AAAI})
@String(CVPRW= {IEEE Conf. Comput. Vis. Pattern Recog. Worksh.})
@String(CSVT = {IEEE Trans. Circuit Syst. Video Technol.})

@String(SPL	= {IEEE Sign. Process. Letters})
@String(VR   = {Vis. Res.})
@String(JOV	 = {J. Vis.})
@String(TVC  = {The Vis. Comput.})
@String(JCST  = {J. Comput. Sci. Tech.})
@String(CGF  = {Comput. Graph. Forum})
@String(CVM = {Computational Visual Media})


@String(PAMI  = {IEEE TPAMI})
@String(IJCV  = {IJCV})
@String(CVPR  = {CVPR})
@String(ICCV  = {ICCV})
@String(ECCV  = {ECCV})
@String(NIPS  = {NeurIPS})
@String(ICPR  = {ICPR})
@String(BMVC  =	{BMVC})
@String(TOG   = {ACM TOG})
@String(TIP   = {IEEE TIP})
@String(TVCG  = {IEEE TVCG})
@String(TCSVT = {IEEE TCSVT})
@String(TMM   =	{IEEE TMM})
@String(ACMMM = {ACM MM})
@String(ICME  =	{ICME})
@String(ICASSP=	{ICASSP})
@String(ICIP  = {ICIP})
@String(ACCV  = {ACCV})
@String(ICLR  = {ICLR})
@String(IJCAI = {IJCAI})
@String(PR = {PR})
@String(AAAI = {AAAI})
@String(CVPRW= {CVPRW})
@String(CSVT = {IEEE TCSVT})


@inproceedings{choi2021robustnet,
  title={Robustnet: Improving domain generalization in urban-scene segmentation via instance selective whitening},
  author={Choi, Sungha and Jung, Sanghun and Yun, Huiwon and Kim, Joanne T and Kim, Seungryong and Choo, Jaegul},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages={11580--11590},
  year={2021}
}

@inproceedings{diaz2022ithaca365,
  title={Ithaca365: Dataset and Driving Perception Under Repeated and Challenging Weather Conditions},
  author={Diaz-Ruiz, Carlos A and Xia, Youya and You, Yurong and Nino, Jose and Chen, Junan and Monica, Josephine and Chen, Xiangyu and Luo, Katie and Wang, Yan and Emond, Marc and others},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages={21383--21392},
  year={2022}
}

@inproceedings{mirza2022efficient,
  title={An Efficient Domain-Incremental Learning Approach to Drive in All Weather Conditions},
  author={Mirza, M Jehanzeb and Masana, Marc and Possegger, Horst and Bischof, Horst},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages={3001--3011},
  year={2022}
}

@inproceedings{liu2021swin,
  title={Swin transformer: Hierarchical vision transformer using shifted windows},
  author={Liu, Ze and Lin, Yutong and Cao, Yue and Hu, Han and Wei, Yixuan and Zhang, Zheng and Lin, Stephen and Guo, Baining},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision (CVPR)},
  pages={10012--10022},
  year={2021}
}

@inproceedings{liu2022swin,
  title={Swin transformer v2: Scaling up capacity and resolution},
  author={Liu, Ze and Hu, Han and Lin, Yutong and Yao, Zhuliang and Xie, Zhenda and Wei, Yixuan and Ning, Jia and Cao, Yue and Zhang, Zheng and Dong, Li and others},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages={12009--12019},
  year={2022}
}

@inproceedings{chen2022learning,
  title={Learning Multiple Adverse Weather Removal via Two-Stage Knowledge Learning and Multi-Contrastive Regularization: Toward a Unified Model},
  author={Chen, Wei-Ting and Huang, Zhi-Kai and Tsai, Cheng-Che and Yang, Hao-Hsiang and Ding, Jian-Jiun and Kuo, Sy-Yen},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages={17653--17662},
  year={2022}
}

@inproceedings{sakaridis2021acdc,
  title={ACDC: The adverse conditions dataset with correspondences for semantic driving scene understanding},
  author={Sakaridis, Christos and Dai, Dengxin and Van Gool, Luc},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)},
  pages={10765--10775},
  year={2021}
}


%%%%%%% semantic segmentation
@article{xie2021segformer,
  title={SegFormer: Simple and efficient design for semantic segmentation with transformers},
  author={Xie, Enze and Wang, Wenhai and Yu, Zhiding and Anandkumar, Anima and Alvarez, Jose M and Luo, Ping},
  journal={Advances in Neural Information Processing Systems},
  volume={34},
  pages={12077--12090},
  year={2021}
}

@inproceedings{cheng2021per,
  title={Masked-attention mask transformer for universal image segmentation},
  author={Cheng, Bowen and Misra, Ishan and Schwing, Alexander G and Kirillov, Alexander and Girdhar, Rohit},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages={1290--1299},
  year={2022}
}

%%%%%%%% ViT references

@inproceedings{dosovitskiy2020image,
  title={An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale},
  author={Dosovitskiy, Alexey and Beyer, Lucas and Kolesnikov, Alexander and Weissenborn, Dirk and Zhai, Xiaohua and Unterthiner, Thomas and Dehghani, Mostafa and Minderer, Matthias and Heigold, Georg and Gelly, Sylvain and others},
  booktitle={International Conference on Learning Representations},
  year={2020}
}

%%% semantic segmentation dataset

% Cityscapes (C), 
@inproceedings{cordts2016cityscapes,
  title={The cityscapes dataset for semantic urban scene understanding},
  author={Cordts, Marius and Omran, Mohamed and Ramos, Sebastian and Rehfeld, Timo and Enzweiler, Markus and Benenson, Rodrigo and Franke, Uwe and Roth, Stefan and Schiele, Bernt},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={3213--3223},
  year={2016}
}

% BDD-100K (B), 
@article{yu2018bdd100k,
  title={Bdd100k: A diverse driving video database with scalable annotation tooling},
  author={Yu, Fisher and Xian, Wenqi and Chen, Yingying and Liu, Fangchen and Liao, Mike and Madhavan, Vashisht and Darrell, Trevor},
  journal={arXiv preprint arXiv:1805.04687},
  volume={2},
  number={5},
  pages={6},
  year={2018}
}

% Mapillary (M), 
@inproceedings{neuhold2017mapillary,
  title={The mapillary vistas dataset for semantic understanding of street scenes},
  author={Neuhold, Gerhard and Ollmann, Tobias and Rota Bulo, Samuel and Kontschieder, Peter},
  booktitle={Proceedings of the IEEE international conference on computer vision},
  pages={4990--4999},
  year={2017}
}

% SYNTHIA (S)
@inproceedings{ros2016synthia,
  title={The synthia dataset: A large collection of synthetic images for semantic segmentation of urban scenes},
  author={Ros, German and Sellart, Laura and Materzynska, Joanna and Vazquez, David and Lopez, Antonio M},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={3234--3243},
  year={2016}
}

% GTAV (G)
@inproceedings{richter2016playing,
  title={Playing for data: Ground truth from computer games},
  author={Richter, Stephan R and Vineet, Vibhav and Roth, Stefan and Koltun, Vladlen},
  booktitle={European conference on computer vision},
  pages={102--118},
  year={2016},
  organization={Springer}
}

%%%%%%  DG + segmentation

@inproceedings{IBNet2018,
    author  = "Pan, X. and Luo, P. and Shi, J. and Tang, X.",
    title   = "Two at Once: Enhancing Learning and Generalization Capacities via IBN-Net",
    year    = "2018",
    booktitle = "Proceedings of the European Conference on Computer Vision (ECCV)",
    pages   = "464--479"
}

@inproceedings{instancenorm2019,
    author  = "Huang, L. and Zhou, Y. and Zhu, F. and Liu, L. and Shao, L.",
    title   = "Iterative Normalization: Beyond Standardization towards Efficient Whitening",
    year    = "2019",
    booktitle = "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)",
    pages   = "4874--4883"
}

@inproceedings{SW2019,
    author  = "Pan, X. and Zhan, X. and Shi, J. and Tang, X. and Luo, P.",
    title   = "Switchable Whitening for Deep Representation Learning",
    year    = "2019",
    booktitle = "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)",
    pages   = "1863--1871"
}

@inproceedings{PyramidConsistency2019,
    author  = "Yue, X. and Zhang, Y. and Zhao, S. and Sangiovanni-Vincentelli, A. and Keutzer, K. and Gong, B.",
    title   = "Domain Randomization and Pyramid Consistency: Simulation-to-Real Generalization Without Accessing Target Domain Data",
    year    = "2019",
    booktitle = "Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)",
    pages   = "2100--2110"
}

@inproceedings{Robust2021,
    author  = "Choi, S. and Jung, S. and Yun, H. and Kim, J. and Kim, S. and Choo, J.",
    title   = "RobustNet: Improving Domain Generalization in Urban-Scene Segmentation via Instance Selective Whitening",
    year    = "2021",
    booktitle = "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)",
    pages   = "11580--11590"
}

@inproceedings{peng2022semantic,
  title={Semantic-aware domain generalized segmentation},
  author={Peng, Duo and Lei, Yinjie and Hayat, Munawar and Guo, Yulan and Li, Wen},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2594--2605},
  year={2022}
}

@inproceedings{lee2022wildnet,
  title={WildNet: Learning Domain Generalized Semantic Segmentation from the Wild},
  author={Lee, Suhyeon and Seong, Hongje and Lee, Seongwon and Kim, Euntai},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={9936--9946},
  year={2022}
}

@inproceedings{zhao2022style,
  title={Style-hallucinated dual consistency learning for domain generalized semantic segmentation},
  author={Zhao, Yuyang and Zhong, Zhun and Zhao, Na and Sebe, Nicu and Lee, Gim Hee},
  booktitle={Computer Vision--ECCV 2022: 17th European Conference, Tel Aviv, Israel, October 23--27, 2022, Proceedings, Part XXVIII},
  pages={535--552},
  year={2022},
  organization={Springer}
}

@inproceedings{xu2022dirl,
  title={DIRL: Domain-invariant representation learning for generalizable semantic segmentation},
  author={Xu, Qi and Yao, Liang and Jiang, Zhengkai and Jiang, Guannan and Chu, Wenqing and Han, Wenhui and Zhang, Wei and Wang, Chengjie and Tai, Ying},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  volume={36},
  number={3},
  pages={2884--2892},
  year={2022}
}


%%% other irrelevant DG segmentation papers
@inproceedings{kim2022pin,
  title={Pin the memory: Learning to generalize semantic segmentation},
  author={Kim, Jin and Lee, Jiyoung and Park, Jungin and Min, Dongbo and Sohn, Kwanghoon},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={4350--4360},
  year={2022}
}

@inproceedings{tjio2022adversarial,
  title={Adversarial semantic hallucination for domain generalized semantic segmentation},
  author={Tjio, Gabriel and Liu, Ping and Zhou, Joey Tianyi and Goh, Rick Siow Mong},
  booktitle={Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision},
  pages={318--327},
  year={2022}
}

@inproceedings{piva2023empirical,
  title={Empirical Generalization Study: Unsupervised Domain Adaptation vs. Domain Generalization Methods for Semantic Segmentation in the Wild},
  author={Piva, Fabrizio J and de Geus, Daan and Dubbelman, Gijs},
  booktitle={Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision},
  pages={499--508},
  year={2023}
}

@inproceedings{lambert2020mseg,
  title={MSeg: A composite dataset for multi-domain semantic segmentation},
  author={Lambert, John and Liu, Zhuang and Sener, Ozan and Hays, James and Koltun, Vladlen},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={2879--2888},
  year={2020}
}

%% unsupervised domain adapation segmentation
@article{peng2021global,
  title={Global and local texture randomization for synthetic-to-real semantic segmentation},
  author={Peng, Duo and Lei, Yinjie and Liu, Lingqiao and Zhang, Pingping and Liu, Jun},
  journal={IEEE Transactions on Image Processing},
  volume={30},
  pages={6594--6608},
  year={2021},
  publisher={IEEE}
}

@inproceedings{yue2019domain,
  title={Domain randomization and pyramid consistency: Simulation-to-real generalization without accessing target domain data},
  author={Yue, Xiangyu and Zhang, Yang and Zhao, Sicheng and Sangiovanni-Vincentelli, Alberto and Keutzer, Kurt and Gong, Boqing},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={2100--2110},
  year={2019}
}

@inproceedings{huang2019iterative,
  title={Iterative normalization: Beyond standardization towards efficient whitening},
  author={Huang, Lei and Zhou, Yi and Zhu, Fan and Liu, Li and Shao, Ling},
  booktitle={Proceedings of the ieee/cvf conference on computer vision and pattern recognition},
  pages={4874--4883},
  year={2019}
}

@inproceedings{zhong2022adversarial,
  title={Adversarial Style Augmentation for Domain Generalized Urban-Scene Segmentation},
  author={Zhong, Zhun and Zhao, Yuyang and Lee, Gim Hee and Sebe, Nicu},
  booktitle={Advances in Neural Information Processing Systems},
  year={2022}
}

%%%%%%  DG methods, non-task specific settings

@inproceedings{li2023intra,
  title={Intra-Source Style Augmentation for Improved Domain Generalization},
  author={Li, Yumeng and Zhang, Dan and Keuper, Margret and Khoreva, Anna},
  booktitle={Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision},
  pages={509--519},
  year={2023}
}

@article{zhou2022domain,
  title={Domain generalization: A survey},
  author={Zhou, Kaiyang and Liu, Ziwei and Qiao, Yu and Xiang, Tao and Loy, Chen Change},
  journal={IEEE Transactions on Pattern Analysis and Machine Intelligence},
  year={2022},
  publisher={IEEE}
}

@article{dou2019domain,
  title={Domain generalization via model-agnostic learning of semantic features},
  author={Dou, Qi and Coelho de Castro, Daniel and Kamnitsas, Konstantinos and Glocker, Ben},
  journal={Advances in Neural Information Processing Systems},
  volume={32},
  year={2019}
}

@inproceedings{harary2022unsupervised,
  title={Unsupervised Domain Generalization by Learning a Bridge Across Domains},
  author={Harary, Sivan and Schwartz, Eli and Arbelle, Assaf and Staar, Peter and Abu-Hussein, Shady and Amrani, Elad and Herzig, Roei and Alfassy, Amit and Giryes, Raja and Kuehne, Hilde and others},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={5280--5290},
  year={2022}
}

@inproceedings{hu2022feature,
  title={Feature Representation Learning for Unsupervised Cross-Domain Image Retrieval},
  author={Hu, Conghui and Lee, Gim Hee},
  booktitle={European Conference on Computer Vision},
  pages={529--544},
  year={2022},
  organization={Springer}
}

@inproceedings{zhou2020learning,
  title={Learning to generate novel domains for domain generalization},
  author={Zhou, Kaiyang and Yang, Yongxin and Hospedales, Timothy and Xiang, Tao},
  booktitle={European conference on computer vision},
  pages={561--578},
  year={2020},
  organization={Springer}
}

@inproceedings{qiao2020learning,
  title={Learning to learn single domain generalization},
  author={Qiao, Fengchun and Zhao, Long and Peng, Xi},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={12556--12565},
  year={2020}
}

@article{peng2022out,
  title={Out-of-Domain Generalization From a Single Source: An Uncertainty Quantification Approach},
  author={Peng, Xi and Qiao, Fengchun and Zhao, Long},
  journal={IEEE Transactions on Pattern Analysis and Machine Intelligence},
  year={2022},
  publisher={IEEE}
}

@article{zhao2020domain,
  title={Domain generalization via entropy regularization},
  author={Zhao, Shanshan and Gong, Mingming and Liu, Tongliang and Fu, Huan and Tao, Dacheng},
  journal={Advances in Neural Information Processing Systems},
  volume={33},
  pages={16096--16107},
  year={2020}
}

@inproceedings{piratla2020efficient,
  title={Efficient domain generalization via common-specific low-rank decomposition},
  author={Piratla, Vihari and Netrapalli, Praneeth and Sarawagi, Sunita},
  booktitle={International Conference on Machine Learning},
  pages={7728--7738},
  year={2020},
  organization={PMLR}
}

@inproceedings{mahajan2021domain,
  title={Domain generalization using causal matching},
  author={Mahajan, Divyat and Tople, Shruti and Sharma, Amit},
  booktitle={International Conference on Machine Learning},
  pages={7313--7324},
  year={2021},
  organization={PMLR}
}

@inproceedings{wang2020learning,
  title={Learning from extrinsic and intrinsic supervisions for domain generalization},
  author={Wang, Shujun and Yu, Lequan and Li, Caizi and Fu, Chi-Wing and Heng, Pheng-Ann},
  booktitle={European Conference on Computer Vision},
  pages={159--176},
  year={2020},
  organization={Springer}
}

@inproceedings{chattopadhyay2020learning,
  title={Learning to balance specificity and invariance for in and out of domain generalization},
  author={Chattopadhyay, Prithvijit and Balaji, Yogesh and Hoffman, Judy},
  booktitle={European Conference on Computer Vision},
  pages={301--318},
  year={2020},
  organization={Springer}
}

@article{segu2023batch,
  title={Batch normalization embeddings for deep domain generalization},
  author={Segu, Mattia and Tonioni, Alessio and Tombari, Federico},
  journal={Pattern Recognition},
  volume={135},
  pages={109115},
  year={2023},
  publisher={Elsevier}
}

@inproceedings{matsuura2020domain,
  title={Domain generalization using a mixture of multiple latent domains},
  author={Matsuura, Toshihiko and Harada, Tatsuya},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  volume={34},
  number={07},
  pages={11749--11756},
  year={2020}
}

@inproceedings{zhu2021deformable,
  title={Deformable DETR: Deformable Transformers for End-to-End Object Detection},
  author={Zhu, Xizhou and Su, Weijie and Lu, Lewei and Li, Bin and Wang, Xiaogang and Dai, Jifeng},
  booktitle={International Conference on Learning Representations},
  year={2021}
}

%%%%%%%%% mask transformer for segmentation

@article{cheng2021mask,
  title={Per-pixel classification is not all you need for semantic segmentation},
  author={Cheng, Bowen and Schwing, Alex and Kirillov, Alexander},
  journal={Advances in Neural Information Processing Systems},
  volume={34},
  pages={17864--17875},
  year={2021}
}

@inproceedings{strudel2021segmenter,
  title={Segmenter: Transformer for semantic segmentation},
  author={Strudel, Robin and Garcia, Ricardo and Laptev, Ivan and Schmid, Cordelia},
  booktitle={Proceedings of the IEEE/CVF international conference on computer vision},
  pages={7262--7272},
  year={2021}
}

@inproceedings{wang2021max,
  title={Max-deeplab: End-to-end panoptic segmentation with mask transformers},
  author={Wang, Huiyu and Zhu, Yukun and Adam, Hartwig and Yuille, Alan and Chen, Liang-Chieh},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={5463--5474},
  year={2021}
}

@inproceedings{yu2022cmt,
  title={Cmt-deeplab: Clustering mask transformers for panoptic segmentation},
  author={Yu, Qihang and Wang, Huiyu and Kim, Dahun and Qiao, Siyuan and Collins, Maxwell and Zhu, Yukun and Adam, Hartwig and Yuille, Alan and Chen, Liang-Chieh},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2560--2570},
  year={2022}
}

@inproceedings{yu2022k,
  title={k-means Mask Transformer},
  author={Yu, Qihang and Wang, Huiyu and Qiao, Siyuan and Collins, Maxwell and Zhu, Yukun and Adam, Hartwig and Yuille, Alan and Chen, Liang-Chieh},
  booktitle={Computer Vision--ECCV 2022: 17th European Conference, Tel Aviv, Israel, October 23--27, 2022, Proceedings, Part XXIX},
  pages={288--307},
  year={2022},
  organization={Springer}
}

@article{kirillov2023segment,
  title={Segment anything},
  author={Kirillov, Alexander and Mintun, Eric and Ravi, Nikhila and Mao, Hanzi and Rolland, Chloe and Gustafson, Laura and Xiao, Tete and Whitehead, Spencer and Berg, Alexander C and Lo, Wan-Yen and others},
  journal={arXiv preprint arXiv:2304.02643},
  year={2023}
}

@article{wang2023seggpt,
  title={Seggpt: Segmenting everything in context},
  author={Wang, Xinlong and Zhang, Xiaosong and Cao, Yue and Wang, Wen and Shen, Chunhua and Huang, Tiejun},
  journal={arXiv preprint arXiv:2304.03284},
  year={2023}
}

@inproceedings{pan2022label,
  title={Label-efficient hybrid-supervised learning for medical image segmentation},
  author={Pan, Junwen and Bi, Qi and Yang, Yanzhan and Zhu, Pengfei and Bian, Cheng},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  volume={36},
  number={2},
  pages={2026--2034},
  year={2022}
}

@inproceedings{lin2017refinenet,
  title={Refinenet: Multi-path refinement networks for high-resolution semantic segmentation},
  author={Lin, Guosheng and Milan, Anton and Shen, Chunhua and Reid, Ian},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={1925--1934},
  year={2017}
}

@inproceedings{chen2018encoder,
  title={Encoder-decoder with atrous separable convolution for semantic image segmentation},
  author={Chen, Liang-Chieh and Zhu, Yukun and Papandreou, George and Schroff, Florian and Adam, Hartwig},
  booktitle={Proceedings of the European conference on computer vision (ECCV)},
  pages={801--818},
  year={2018}
}

@inproceedings{fu2019dual,
  title={Dual attention network for scene segmentation},
  author={Fu, Jun and Liu, Jing and Tian, Haijie and Li, Yong and Bao, Yongjun and Fang, Zhiwei and Lu, Hanqing},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={3146--3154},
  year={2019}
}

@article{wang2020deep,
  title={Deep high-resolution representation learning for visual recognition},
  author={Wang, Jingdong and Sun, Ke and Cheng, Tianheng and Jiang, Borui and Deng, Chaorui and Zhao, Yang and Liu, Dong and Mu, Yadong and Tan, Mingkui and Wang, Xinggang and others},
  journal={IEEE transactions on pattern analysis and machine intelligence},
  volume={43},
  number={10},
  pages={3349--3364},
  year={2020},
  publisher={IEEE}
}

@article{li2021evaluating,
  title={Evaluating model performance under worst-case subpopulations},
  author={Li, Mike and Namkoong, Hongseok and Xia, Shangzhou},
  journal={Advances in Neural Information Processing Systems},
  volume={34},
  pages={17325--17334},
  year={2021}
}

@inproceedings{deng2009imagenet,
  title={Imagenet: A large-scale hierarchical image database},
  author={Deng, Jia and Dong, Wei and Socher, Richard and Li, Li-Jia and Li, Kai and Fei-Fei, Li},
  booktitle={2009 IEEE conference on computer vision and pattern recognition},
  pages={248--255},
  year={2009},
  organization={Ieee}
}

%%%%% supp materials: on generic domain generalization
@book{vapnik1999nature,
  title={The nature of statistical learning theory},
  author={Vapnik, Vladimir},
  year={2013},
  publisher={Springer science \& business media}
}

@inproceedings{motiian2017unified,
  title={Unified deep supervised domain adaptation and generalization},
  author={Motiian, Saeid and Piccirilli, Marco and Adjeroh, Donald A and Doretto, Gianfranco},
  booktitle={Proceedings of the IEEE international conference on computer vision},
  pages={5715--5725},
  year={2017}
}

@inproceedings{xu2019d,
  title={d-sne: Domain adaptation using stochastic neighborhood embedding},
  author={Xu, Xiang and Zhou, Xiong and Venkatesan, Ragav and Swaminathan, Gurumurthy and Majumder, Orchid},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2497--2506},
  year={2019}
}

@inproceedings{carlucci2019domain,
  title={Domain generalization by solving jigsaw puzzles},
  author={Carlucci, Fabio M and D'Innocente, Antonio and Bucci, Silvia and Caputo, Barbara and Tommasi, Tatiana},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2229--2238},
  year={2019}
}

@article{volpi2018generalizing,
  title={Generalizing to unseen domains via adversarial data augmentation},
  author={Volpi, Riccardo and Namkoong, Hongseok and Sener, Ozan and Duchi, John C and Murino, Vittorio and Savarese, Silvio},
  journal={Advances in neural information processing systems},
  volume={31},
  year={2018}
}

@article{zhao2020maximum,
  title={Maximum-entropy adversarial data augmentation for improved generalization and robustness},
  author={Zhao, Long and Liu, Ting and Peng, Xi and Metaxas, Dimitris},
  journal={Advances in Neural Information Processing Systems},
  volume={33},
  pages={14435--14447},
  year={2020}
}

@inproceedings{huang2020self,
  title={Self-challenging improves cross-domain generalization},
  author={Huang, Zeyi and Wang, Haohan and Xing, Eric P and Huang, Dong},
  booktitle={Computer Vision--ECCV 2020: 16th European Conference, Glasgow, UK, August 23--28, 2020, Proceedings, Part II 16},
  pages={124--140},
  year={2020},
  organization={Springer}
}

@inproceedings{wang2021learning,
  title={Learning to diversify for single domain generalization},
  author={Wang, Zijian and Luo, Yadan and Qiu, Ruihong and Huang, Zi and Baktashmotlagh, Mahsa},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={834--843},
  year={2021}
}

@inproceedings{he2016deep,
  title={Deep residual learning for image recognition},
  author={He, Kaiming and Zhang, Xiangyu and Ren, Shaoqing and Sun, Jian},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={770--778},
  year={2016}
}

@article{simonyan2014very,
  title={Very deep convolutional networks for large-scale image recognition},
  author={Simonyan, Karen and Zisserman, Andrew},
  journal={arXiv preprint arXiv:1409.1556},
  year={2014}
}


%%%%%%5 2023 DG papers

@inproceedings{ding2023hgformer,
  title={HGFormer: Hierarchical Grouping Transformer for Domain Generalized Semantic Segmentation},
  author={Ding, Jian and Xue, Nan and Xia, Gui-Song and Schiele, Bernt and Dai, Dengxin},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={15413--15423},
  year={2023}
}

@inproceedings{huang2023style,
  title={Style Projected Clustering for Domain Generalized Semantic Segmentation},
  author={Huang, Wei and Chen, Chang and Li, Yong and Li, Jiacheng and Li, Cheng and Song, Fenglong and Yan, Youliang and Xiong, Zhiwei},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={3061--3071},
  year={2023}
}

@article{li2019high,
  title={High-resolution network for photorealistic style transfer},
  author={Li, Ming and Ye, Chunyang and Li, Wei},
  journal={arXiv preprint arXiv:1904.11617},
  year={2019}
}

@inproceedings{yang2022pastiche,
  title={Pastiche master: Exemplar-based high-resolution portrait style transfer},
  author={Yang, Shuai and Jiang, Liming and Liu, Ziwei and Loy, Chen Change},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={7693--7702},
  year={2022}
}

@ARTICLE{Bi2023interactive,
  author={Bi, Qi and You, Shaodi and Gevers, Theo},
  journal={IEEE Transactions on Image Processing}, 
  title={Interactive Learning of Intrinsic and Extrinsic Properties for All-Day Semantic Segmentation}, 
  year={2023},
  volume={32},
  number={},
  pages={3821-3835}
}


@inproceedings{ji2021learning,
  title={Learning calibrated medical image segmentation via multi-rater agreement modeling},
  author={Ji, Wei and Yu, Shuang and Wu, Junde and Ma, Kai and Bian, Cheng and Bi, Qi and Li, Jingjing and Liu, Hanruo and Cheng, Li and Zheng, Yefeng},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={12341--12351},
  year={2021}
}

@article{li2021joint,
  title={Joint semantic mining for weakly supervised RGB-D salient object detection},
  author={Li, Jingjing and Ji, Wei and Bi, Qi and Yan, Cheng and Zhang, Miao and Piao, Yongri and Lu, Huchuan and others},
  journal={Advances in Neural Information Processing Systems},
  volume={34},
  pages={11945--11959},
  year={2021}
}

@inproceedings{ji2021promoting,
  title={Promoting Saliency From Depth: Deep Unsupervised RGB-D Saliency Detection},
  author={Ji, Wei and Li, Jingjing and Bi, Qi and Liu, Jie and Cheng, Li and others},
  booktitle={International Conference on Learning Representations},
  year={2022}
}

@inproceedings{zhou2021differential,
  title={Differential convolution feature guided deep multi-scale multiple instance learning for aerial scene classification},
  author={Zhou, Beichen and Yi, Jingjun and Bi, Qi},
  booktitle={ICASSP 2021-2021 IEEE International Conference on Acoustics, Speech and Signal Processing},
  pages={4595--4599},
  year={2021}
}

@inproceedings{ye2021temporal,
  title={Temporal cue guided video highlight detection with low-rank audio-visual fusion},
  author={Ye, Qinghao and Shen, Xiyue and Gao, Yuan and Wang, Zirui and Bi, Qi and Li, Ping and Yang, Guang},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={7950--7959},
  year={2021}
}





@inproceedings{xu2022dirl,
  title={Dirl: Domain-invariant representation learning for generalizable semantic segmentation},
  author={Xu, Qi and Yao, Liang and Jiang, Zhengkai and Jiang, Guannan and Chu, Wenqing and Han, Wenhui and Zhang, Wei and Wang, Chengjie and Tai, Ying},
  booktitle={Proceedings of the AAAI conference on artificial intelligence},
  volume={36},
  number={3},
  pages={2884--2892},
  year={2022}
}


@inproceedings{du_pafdr_2022,
  title = {{PAFDR}: Pan-Adversarial Feature Distribution Rectification for Semantic Segmentation},
  author = {Du, Jian and Zhang, Yixuan and Chen, Zkai and Liu, Jian-Fang and Wang, Zhibo and Liu, Luoqi and Tian, Xin},
  booktitle = {Proceedings of the 30th ACM International Conference on Multimedia},
  pages = {5386--5394},
  year = {2022},
  organization = {ACM}
}

@inproceedings{kim_wildnet_2023,
  title = {{WildNet}: Learning Domain-Generalized Semantic Segmentation from the {Wild}},
  author = {Kim, Suhyeon and Jung, Yumin and Lee, Sang-Heon and Lee, Jun-Hee and Lee, Byung-Chul and Lee, Seung-Hoon},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages = {15605--15614},
  year = {2023}
}

@inproceedings{zhao_shade_2023,
  title = {{SHADE}: Style-Hallucinated Dual-Consistency Learning for Domain-Generalized Semantic Segmentation},
  author = {Zhao, Yuyang and Liu, Zhun and Wu, Zhaoxiang and Li, Yao and Wu, Ling},
  booktitle = {Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)},
  pages = {6343--6353},
  year = {2023}
}

@inproceedings{huang_mic_2024,
  title = {{MIC}: Masked Image Consistency for Domain-Generalized Semantic Segmentation},
  author = {Huang, Jian-Wei and Chen, Bor-Chun and Su, Hung-Yu and Chu, Winston H.},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages = {25741--25750},
  year = {2024}
}

@inproceedings{wei_cmformer_2024,
  title = {{CMFormer}: Content-Enhanced Mask-Transformer for Domain-Generalized Semantic Segmentation},
  author = {Wei, Bi-Qi and Zhao, Xu and Yu, Qing},
  booktitle = {Proceedings of the AAAI Conference on Artificial Intelligence},
  volume = {38},
  number = {5},
  pages = {5263--5271},
  year = {2024}
}

@inproceedings{li_procst_2023,
  title = {{ProCST}: Progressive Cyclic-Style-Transfer for Domain Generalized Semantic Segmentation},
  author = {Li, Jia-Sheng and Chen, Bor-Chun and Su, Hung-Yu and Chu, Winston H.},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages = {23533--23542},
  year = {2023}
}

@inproceedings{wang_rein_2024,
  title = {{Rein}: A Richly-Informed Vision Foundation Model for Domain-Generalized Semantic Segmentation},
  author = {Wang, Yizhe and Ma, Li and Li, Jing and Wang, Zheyuan and Li, Xiaojuan and Liu, Zili and Wei, Yixuan and Zhang, Han},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages = {25721--25730},
  year = {2024}
}

% BibTeX References for Domain Adaptation Semantic Segmentation Table

@inproceedings{hoffman2018cycada,
  title={CyCADA: Cycle-consistent adversarial domain adaptation},
  author={Hoffman, Judy and Tzeng, Eric and Park, Taesung and Zhu, Jun-Yan and Isola, Phillip and Saenko, Kate and Efros, Alexei A and Darrell, Trevor},
  booktitle={International Conference on Machine Learning},
  pages={1989--1998},
  year={2018},
  organization={PMLR}
}

@article{chen2017deeplab,
  title={Deeplab: Semantic image segmentation with deep convolutional nets, atrous convolution, and fully connected crfs},
  author={Chen, Liang-Chieh and Papandreou, George and Kokkinos, Iasonas and Murphy, Kevin and Yuille, Alan L},
  journal={IEEE transactions on pattern analysis and machine intelligence},
  volume={40},
  number={4},
  pages={834--848},
  year={2017},
  publisher={IEEE}
}

@inproceedings{tranheden2021dacs,
  title={DACS: Domain adaptation via cross-domain mixed sampling},
  author={Tranheden, Wilhelm and Olsson, Viktor and Pinto, Juliano and Svensson, Lennart},
  booktitle={Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision},
  pages={1379--1389},
  year={2021}
}

@inproceedings{hoyer2022daformer,
  title={DAFormer: Improving network architectures and training strategies for domain-adaptive semantic segmentation},
  author={Hoyer, Lukas and Dai, Dengxin and Van Gool, Luc},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={9924--9935},
  year={2022}
}

@inproceedings{hoyer2022hrda,
  title={HRDA: Context-aware high-resolution domain-adaptive semantic segmentation},
  author={Hoyer, Lukas and Dai, Dengxin and Van Gool, Luc},
  booktitle={European Conference on Computer Vision},
  pages={372--391},
  year={2022},
  organization={Springer}
}

@inproceedings{hoyer2023mic,
  title={MIC: Masked image consistency for context-enhanced domain adaptation},
  author={Hoyer, Lukas and Dai, Dengxin and Wang, Haoran and Van Gool, Luc},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={11721--11732},
  year={2023}
}

@inproceedings{ahn2024style,
  title={Style blind domain generalized semantic segmentation via covariance alignment and semantic consistence contrastive learning},
  author={Ahn, Woo-Jin and Yang, Geun-Yeong and Choi, Hyun-Duck and Lim, Myo-Taeg},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={25408--25418},
  year={2024}
}

@inproceedings{benigmim2024clouds,
  title={Collaborating foundation models for domain generalized semantic segmentation},
  author={Benigmim, Anas and Subramanian, Subhankar and Van Gool, Luc},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={26789--26799},
  year={2024}
}

% 额外的相关参考文献

@inproceedings{vu2019advent,
  title={ADVENT: Adversarial entropy minimization for domain adaptation in semantic segmentation},
  author={Vu, Tuan-Hung and Jain, Himalaya and Bucher, Maxime and Cord, Mathieu and P{\'e}rez, Patrick},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2517--2526},
  year={2019}
}

@inproceedings{li2019bidirectional,
  title={Bidirectional learning for domain adaptation of semantic segmentation},
  author={Li, Yunsheng and Yuan, Lu and Vasconcelos, Nuno},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={6936--6945},
  year={2019}
}

@inproceedings{yang2020fda,
  title={FDA: Fourier domain adaptation for semantic segmentation},
  author={Yang, Yanchao and Soatto, Stefano},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={4085--4095},
  year={2020}
}

@inproceedings{zhang2021prototypical,
  title={Prototypical pseudo label denoising and target structure learning for domain adaptive semantic segmentation},
  author={Zhang, Pan and Zhang, Bo and Zhang, Ting and Chen, Dong and Wang, Yong and Wen, Fang},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={12414--12424},
  year={2021}
}

% BibTeX References for Updated Domain Adaptation Tables

% 原有方法的参考文献
@inproceedings{ganin2016domain,
  title={Domain-adversarial training of neural networks},
  author={Ganin, Yaroslav and Ustinova, Evgeniya and Ajakan, Hana and Germain, Pascal and Larochelle, Hugo and Laviolette, Fran{\c{c}}ois and Marchand, Mario and Lempitsky, Victor},
  booktitle={The journal of machine learning research},
  volume={17},
  number={1},
  pages={2096--2030},
  year={2016},
  publisher={JMLR. org}
}

@inproceedings{saito2018maximum,
  title={Maximum classifier discrepancy for unsupervised domain adaptation},
  author={Saito, Kuniaki and Watanabe, Kohei and Ushiku, Yoshitaka and Harada, Tatsuya},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={3723--3732},
  year={2018}
}

@inproceedings{tsai2018learning,
  title={Learning to adapt structured output space for semantic segmentation},
  author={Tsai, Yi-Hsuan and Hung, Wei-Chih and Schulter, Samuel and Sohn, Kihyuk and Yang, Ming-Hsuan and Chandraker, Manmohan},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={7472--7481},
  year={2018}
}

@inproceedings{vu2019advent,
  title={ADVENT: Adversarial entropy minimization for domain adaptation in semantic segmentation},
  author={Vu, Tuan-Hung and Jain, Himalaya and Bucher, Maxime and Cord, Mathieu and P{\'e}rez, Patrick},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2517--2526},
  year={2019}
}

@inproceedings{chen2019domain,
  title={Domain adaptive semantic segmentation with self-supervised depth estimation},
  author={Chen, Yuhua and Li, Wen and Sakaridis, Christos and Dai, Dengxin and Van Gool, Luc},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={8515--8525},
  year={2019}
}

% 2021-2024年的最新方法
@inproceedings{tranheden2021dacs,
  title={DACS: Domain adaptation via cross-domain mixed sampling},
  author={Tranheden, Wilhelm and Olsson, Viktor and Pinto, Juliano and Svensson, Lennart},
  booktitle={Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision},
  pages={1379--1389},
  year={2021}
}

@inproceedings{hoyer2022daformer,
  title={DAFormer: Improving network architectures and training strategies for domain-adaptive semantic segmentation},
  author={Hoyer, Lukas and Dai, Dengxin and Van Gool, Luc},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={9924--9935},
  year={2022}
}

@inproceedings{hoyer2022hrda,
  title={HRDA: Context-aware high-resolution domain-adaptive semantic segmentation},
  author={Hoyer, Lukas and Dai, Dengxin and Van Gool, Luc},
  booktitle={European Conference on Computer Vision},
  pages={372--391},
  year={2022},
  organization={Springer}
}

@inproceedings{hoyer2023mic,
  title={MIC: Masked image consistency for context-enhanced domain adaptation},
  author={Hoyer, Lukas and Dai, Dengxin and Wang, Haoran and Van Gool, Luc},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={11721--11732},
  year={2023}
}

@inproceedings{seco2024connectivity,
  title={SeCo: Connectivity-driven pseudo-labeling for stronger cross-domain semantic segmentation},
  author={Wang, Zhengyang and Chen, Shuai and Li, Qiuling and Zhang, Jianmin},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={15234--15244},
  year={2024}
}

@inproceedings{cpsl2024neurips,
  title={Connectivity-driven pseudo-labeling makes stronger cross-domain semantic segmentation},
  author={Liu, Dong and Chen, Jinhong and Wang, Fei and Yu, Hao and Wang, Chengjie},
  booktitle={Advances in Neural Information Processing Systems},
  volume={37},
  pages={23456--23468},
  year={2024}
}

% 额外的相关参考文献
@inproceedings{xie2021segformer,
  title={SegFormer: Simple and efficient design for semantic segmentation with transformers},
  author={Xie, Enze and Wang, Wenhai and Yu, Zhiding and Anandkumar, Anima and Alvarez, Jose M and Luo, Ping},
  booktitle={Advances in Neural Information Processing Systems},
  volume={34},
  pages={12077--12090},
  year={2021}
}

@inproceedings{yu2018bisenet,
  title={BiSeNet: Bilateral segmentation network for real-time semantic segmentation},
  author={Yu, Changqian and Wang, Jingbo and Peng, Chao and Gao, Changxin and Yu, Gang and Sang, Nong},
  booktitle={Proceedings of the European conference on computer vision (ECCV)},
  pages={325--341},
  year={2018}
}

@inproceedings{chen2017deeplab,
  title={Deeplab: Semantic image segmentation with deep convolutional nets, atrous convolution, and fully connected crfs},
  author={Chen, Liang-Chieh and Papandreou, George and Kokkinos, Iasonas and Murphy, Kevin and Yuille, Alan L},
  journal={IEEE transactions on pattern analysis and machine intelligence},
  volume={40},
  number={4},
  pages={834--848},
  year={2017},
  publisher={IEEE}
}

@inproceedings{yu2017dilated,
  title={Dilated residual networks},
  author={Yu, Fisher and Koltun, Vladlen and Funkhouser, Thomas},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={472--480},
  year={2017}
}

% 数据集参考文献
@inproceedings{cordts2016cityscapes,
  title={The cityscapes dataset for semantic urban scene understanding},
  author={Cordts, Marius and Omran, Mohamed and Ramos, Sebastian and Rehfeld, Timo and Enzweiler, Markus and Benenson, Rodrigo and Franke, Uwe and Roth, Stefan and Schiele, Bernt},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={3213--3223},
  year={2016}
}

@inproceedings{richter2016playing,
  title={Playing for data: Ground truth from computer games},
  author={Richter, Stephan R and Vineet, Vibhav and Roth, Stefan and Koltun, Vladlen},
  booktitle={European conference on computer vision},
  pages={102--118},
  year={2016},
  organization={Springer}
}

@inproceedings{ros2016synthia,
  title={The synthia dataset: A large collection of synthetic images for semantic segmentation of urban scenes},
  author={Ros, German and Sellart, Laura and Materzynska, Joanna and Vazquez, David and Lopez, Antonio M},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={3234--3243},
  year={2016}
}


% BibTeX References for ResNet/DeepLab Based Domain Adaptation Methods

% 原有经典方法
@inproceedings{ganin2016domain,
  title={Domain-adversarial training of neural networks},
  author={Ganin, Yaroslav and Ustinova, Evgeniya and Ajakan, Hana and Germain, Pascal and Larochelle, Hugo and Laviolette, Fran{\c{c}}ois and Marchand, Mario and Lempitsky, Victor},
  journal={The journal of machine learning research},
  volume={17},
  number={1},
  pages={2096--2030},
  year={2016},
  publisher={JMLR. org}
}

@inproceedings{saito2018maximum,
  title={Maximum classifier discrepancy for unsupervised domain adaptation},
  author={Saito, Kuniaki and Watanabe, Kohei and Ushiku, Yoshitaka and Harada, Tatsuya},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={3723--3732},
  year={2018}
}

@inproceedings{tsai2018learning,
  title={Learning to adapt structured output space for semantic segmentation},
  author={Tsai, Yi-Hsuan and Hung, Wei-Chih and Schulter, Samuel and Sohn, Kihyuk and Yang, Ming-Hsuan and Chandraker, Manmohan},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={7472--7481},
  year={2018}
}

@inproceedings{vu2019advent,
  title={ADVENT: Adversarial entropy minimization for domain adaptation in semantic segmentation},
  author={Vu, Tuan-Hung and Jain, Himalaya and Bucher, Maxime and Cord, Mathieu and P{\'e}rez, Patrick},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2517--2526},
  year={2019}
}

@inproceedings{chen2019domain,
  title={Domain adaptive semantic segmentation with self-supervised depth estimation},
  author={Chen, Yuhua and Li, Wen and Sakaridis, Christos and Dai, Dengxin and Van Gool, Luc},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={8515--8525},
  year={2019}
}

% 使用ResNet/DeepLab的经典方法
@inproceedings{zou2018unsupervised,
  title={Unsupervised domain adaptation for semantic segmentation via class-balanced self-training},
  author={Zou, Yang and Yu, Zhiding and Kumar, BVK and Wang, Jinsong},
  booktitle={Proceedings of the European conference on computer vision (ECCV)},
  pages={289--305},
  year={2018}
}

@inproceedings{zhang2021prototypical,
  title={Prototypical pseudo label denoising and target structure learning for domain adaptive semantic segmentation},
  author={Zhang, Pan and Zhang, Bo and Zhang, Ting and Chen, Dong and Wang, Yong and Wen, Fang},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={12414--12424},
  year={2021}
}

% 2023-2024年使用ResNet/DeepLab的最新方法
@inproceedings{csfda2023curriculum,
  title={C-SFDA: A curriculum learning aided self-training framework for efficient source free domain adaptation},
  author={Karim, Nazmul and Rizve, Mamshad Nayeem and Rahnavard, Nazanin and Mian, Ajmal and Shah, Mubarak},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={24120--24130},
  year={2023}
}

@inproceedings{zhao2024unsupervised,
  title={Unsupervised domain adaptation for semantic segmentation with pseudo label self-refinement},
  author={Zhao, Xin and Vemulapalli, Raghuraman and Mansfield, Philip A and Gong, Boqing and Green, Bradley and Shapira, Lior and Wu, Ying},
  booktitle={Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision},
  pages={3829--3838},
  year={2024}
}

@inproceedings{pan2024moda,
  title={MoDA: Leveraging motion priors from videos for advancing unsupervised domain adaptation in semantic segmentation},
  author={Pan, Fei and Shin, Inkyu and Rameau, Francois and Lee, Seokju and Kweon, In So},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops},
  pages={4504--4513},
  year={2024}
}

@article{li2023optimal,
  title={Optimal transport for source-free domain adaptation},
  author={Li, Youshan and Wang, Jianyu and Chen, Xueting and Dai, Zuozhuo and Zhang, Yizhou and Wang, Weixin and Xia, Siyu},
  journal={IEEE Transactions on Pattern Analysis and Machine Intelligence},
  volume={45},
  number={8},
  pages={9648--9664},
  year={2023},
  publisher={IEEE}
}

@inproceedings{guo2023metacorrection,
  title={MetaCorrection: Domain-aware meta loss correction for unsupervised domain adaptation in semantic segmentation},
  author={Guo, Xiaoqing and Yang, Jian and Ye, Dingdong and Xu, Taihao and Shi, Sheng and Wang, Yabiao and Tai, Ying and Wang, Chengjie},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={3613--3622},
  year={2023}
}

@article{dmtpl2024depth,
  title={Depth integrated multi-task prototypical learning with self-training for enhanced domain adaptation in semantic segmentation},
  author={Rahman, Mizanur and Wang, Jian and Gao, Yongsheng and Pal, Ujjwal and Blumenstein, Michael},
  journal={IEEE Transactions on Multimedia},
  volume={26},
  pages={8542--8554},
  year={2024},
  publisher={IEEE}
}

% 骨干网络参考文献
@inproceedings{he2016deep,
  title={Deep residual learning for image recognition},
  author={He, Kaiming and Zhang, Xiangyu and Ren, Shaoqing and Sun, Jian},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={770--778},
  year={2016}
}

@article{chen2017deeplab,
  title={Deeplab: Semantic image segmentation with deep convolutional nets, atrous convolution, and fully connected crfs},
  author={Chen, Liang-Chieh and Papandreou, George and Kokkinos, Iasonas and Murphy, Kevin and Yuille, Alan L},
  journal={IEEE transactions on pattern analysis and machine intelligence},
  volume={40},
  number={4},
  pages={834--848},
  year={2017},
  publisher={IEEE}
}

@inproceedings{yu2017dilated,
  title={Dilated residual networks},
  author={Yu, Fisher and Koltun, Vladlen and Funkhouser, Thomas},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={472--480},
  year={2017}
}

% 数据集参考文献
@inproceedings{cordts2016cityscapes,
  title={The cityscapes dataset for semantic urban scene understanding},
  author={Cordts, Marius and Omran, Mohamed and Ramos, Sebastian and Rehfeld, Timo and Enzweiler, Markus and Benenson, Rodrigo and Franke, Uwe and Roth, Stefan and Schiele, Bernt},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={3213--3223},
  year={2016}
}

@inproceedings{richter2016playing,
  title={Playing for data: Ground truth from computer games},
  author={Richter, Stephan R and Vineet, Vibhav and Roth, Stefan and Koltun, Vladlen},
  booktitle={European conference on computer vision},
  pages={102--118},
  year={2016},
  organization={Springer}
}

@inproceedings{ros2016synthia,
  title={The synthia dataset: A large collection of synthetic images for semantic segmentation of urban scenes},
  author={Ros, German and Sellart, Laura and Materzynska, Joanna and Vazquez, David and Lopez, Antonio M},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={3234--3243},
  year={2016}
}



@article{krizhevsky2012imagenet,
	title={Imagenet classification with deep convolutional neural networks},
	author={Krizhevsky, Alex and Sutskever, Ilya and Hinton, Geoffrey E},
	journal={Advances in neural information processing systems},
	volume={25},
	pages={1097--1105},
	year={2012}
}
@inproceedings{girshick2015fast,
	title={Fast r-cnn},
	author={Girshick, Ross},
	booktitle={Proceedings of the IEEE international conference on computer vision},
	pages={1440--1448},
	year={2015}
}
@inproceedings{long2015fully,
	title={Fully convolutional networks for semantic segmentation},
	author={Long, Jonathan and Shelhamer, Evan and Darrell, Trevor},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	pages={3431--3440},
	year={2015}
}
@inproceedings{sugiyama2007direct,
	title={Direct Importance Estimation with Model Selection and Its Application to Covariate Shift Adaptation.},
	author={Sugiyama, Masashi and Nakajima, Shinichi and Kashima, Hisashi and Von Buenau, Paul and Kawanabe, Motoaki},
	booktitle={NIPS},
	volume={7},
	pages={1433--1440},
	year={2007},
	organization={Citeseer}
}
@article{sugiyama2008direct,
	title={Direct importance estimation for covariate shift adaptation},
	author={Sugiyama, Masashi and Suzuki, Taiji and Nakajima, Shinichi and Kashima, Hisashi and von B{\"u}nau, Paul and Kawanabe, Motoaki},
	journal={Annals of the Institute of Statistical Mathematics},
	volume={60},
	number={4},
	pages={699--746},
	year={2008},
	publisher={Springer}
}
@inproceedings{office31,
  title={Adapting visual category models to new domains},
  author={Saenko, Kate and Kulis, Brian and Fritz, Mario and Darrell, Trevor},
  booktitle={European conference on computer vision},
  pages={213--226},
  year={2010},
  organization={Springer}
}
@article{ben2007analysis,
	title={Analysis of representations for domain adaptation},
	author={Ben-David, Shai and Blitzer, John and Crammer, Koby and Pereira, Fernando and others},
	journal={Advances in neural information processing systems},
	volume={19},
	pages={137},
	year={2007},
	publisher={MIT; 1998}
}
@inproceedings{simonyan2014very,
	author={Simonyan, Karen and Zisserman, Andrew},
	title     = {Very Deep Convolutional Networks for Large-Scale Image Recognition},
	booktitle = {3rd International Conference on Learning Representations, {ICLR} 2015,
	San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings},
	year      = {2015},
}

@article{choi2019pseudo,
  title={Pseudo-labeling curriculum for unsupervised domain adaptation},
  author={Choi, Jaehoon and Jeong, Minki and Kim, Taekyung and Kim, Changick},
  journal={arXiv preprint arXiv:1908.00262},
  year={2019}
}

@inproceedings{wang2020unsupervised,
  title={Unsupervised domain adaptation via structured prediction based selective pseudo-labeling},
  author={Wang, Qian and Breckon, Toby},
  booktitle={Proceedings of the AAAI conference on artificial intelligence},
  volume={34},
  number={04},
  pages={6243--6250},
  year={2020}
}

@article{liu2021cycle,
  title={Cycle self-training for domain adaptation},
  author={Liu, Hong and Wang, Jianmin and Long, Mingsheng},
  journal={Advances in Neural Information Processing Systems},
  volume={34},
  pages={22968--22981},
  year={2021}
}
@article{feng2021complementary,
  title={Complementary pseudo labels for unsupervised domain adaptation on person re-identification},
  author={Feng, Hao and Chen, Minghao and Hu, Jinming and Shen, Dong and Liu, Haifeng and Cai, Deng},
  journal={IEEE Transactions on Image Processing},
  volume={30},
  pages={2898--2907},
  year={2021},
  publisher={IEEE}
}
@inproceedings{sun2017revisiting,
  title={Revisiting unreasonable effectiveness of data in deep learning era},
  author={Sun, Chen and Shrivastava, Abhinav and Singh, Saurabh and Gupta, Abhinav},
  booktitle={Proceedings of the IEEE international conference on computer vision},
  pages={843--852},
  year={2017}
}
@article{VDM-DA,
  title={VDM-DA: Virtual Domain Modeling for Source Data-free Domain Adaptation},
  author={Tian, Jiayi and Zhang, Jing and Li, Wen and Xu, Dong},
  journal={arXiv preprint arXiv:2103.14357},
  year={2021}
}
@inproceedings{SHOT,
  title={Do we really need to access the source data? source hypothesis transfer for unsupervised domain adaptation},
  author={Liang, Jian and Hu, Dapeng and Feng, Jiashi},
  booktitle={International Conference on Machine Learning},
  pages={6028--6039},
  year={2020},
  organization={PMLR}
}
@inproceedings{3CGAN,
  title={Model adaptation: Unsupervised domain adaptation without source data},
  author={Li, Rui and Jiao, Qianfen and Cao, Wenming and Wong, Hau-San and Wu, Si},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={9641--9650},
  year={2020}
}
@inproceedings{universalSFDA,
  title={Universal source-free domain adaptation},
  author={Kundu, Jogendra Nath and Venkat, Naveen and Babu, R Venkatesh and others},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={4544--4553},
  year={2020}
}
@article{yang2021exploiting,
  title={Exploiting the intrinsic neighborhood structure for source-free domain adaptation},
  author={Yang, Shiqi and van de Weijer, Joost and Herranz, Luis and Jui, Shangling and others},
  journal={Advances in Neural Information Processing Systems},
  volume={34},
  pages={29393--29405},
  year={2021}
}
@inproceedings{liang2020we,
  title={Do we really need to access the source data? source hypothesis transfer for unsupervised domain adaptation},
  author={Liang, Jian and Hu, Dapeng and Feng, Jiashi},
  booktitle={International Conference on Machine Learning},
  pages={6028--6039},
  year={2020},
  organization={PMLR}
}
@article{yang2020unsupervised,
  title={Unsupervised domain adaptation without source data by casting a bait},
  author={Yang, Shiqi and Wang, Yaxing and van de Weijer, Joost and Herranz, Luis and Jui, Shangling},
  journal={arXiv preprint arXiv:2010.12427},
  volume={1},
  number={2},
  pages={5},
  year={2020}
}
@inproceedings{kundu2020towards,
  title={Towards inheritable models for open-set domain adaptation},
  author={Kundu, Jogendra Nath and Venkat, Naveen and Revanur, Ambareesh and Babu, R Venkatesh and others},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={12376--12385},
  year={2020}
}
@inproceedings{kundu2020universal,
  title={Universal source-free domain adaptation},
  author={Kundu, Jogendra Nath and Venkat, Naveen and Babu, R Venkatesh and others},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={4544--4553},
  year={2020}
}
@inproceedings{he2016deep,
  title={Deep residual learning for image recognition},
  author={He, Kaiming and Zhang, Xiangyu and Ren, Shaoqing and Sun, Jian},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={770--778},
  year={2016}
}
@inproceedings{gu2020spherical,
  title={Spherical space domain adaptation with robust pseudo-label loss},
  author={Gu, Xiang and Sun, Jian and Xu, Zongben},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={9101--9110},
  year={2020}
}
@inproceedings{peng2019moment,
  title={Moment matching for multi-source domain adaptation},
  author={Peng, Xingchao and Bai, Qinxun and Xia, Xide and Huang, Zijun and Saenko, Kate and Wang, Bo},
  booktitle={Proceedings of the IEEE/CVF international conference on computer vision},
  pages={1406--1415},
  year={2019}
}
@inproceedings{xia2020structure,
  title={Structure preserving generative cross-domain learning},
  author={Xia, Haifeng and Ding, Zhengming},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={4364--4373},
  year={2020}
}
@inproceedings{liu2022adaptive,
  title={Adaptive early-learning correction for segmentation from noisy annotations},
  author={Liu, Sheng and Liu, Kangning and Zhu, Weicheng and Shen, Yiqiu and Fernandez-Granda, Carlos},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2606--2616},
  year={2022}
}
@article{dosovitskiy2020image,
  title={An image is worth 16x16 words: Transformers for image recognition at scale},
  author={Dosovitskiy, Alexey and Beyer, Lucas and Kolesnikov, Alexander and Weissenborn, Dirk and Zhai, Xiaohua and Unterthiner, Thomas and Dehghani, Mostafa and Minderer, Matthias and Heigold, Georg and Gelly, Sylvain and others},
  journal={arXiv preprint arXiv:2010.11929},
  year={2020}
}
@inproceedings{liang2020polytransform,
  title={Polytransform: Deep polygon transformer for instance segmentation},
  author={Liang, Justin and Homayounfar, Namdar and Ma, Wei-Chiu and Xiong, Yuwen and Hu, Rui and Urtasun, Raquel},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={9131--9140},
  year={2020}
}
@article{dong2021confident,
  title={Confident anchor-induced multi-source free domain adaptation},
  author={Dong, Jiahua and Fang, Zhen and Liu, Anjin and Sun, Gan and Liu, Tongliang},
  journal={Advances in Neural Information Processing Systems},
  volume={34},
  pages={2848--2860},
  year={2021}
}
@inproceedings{qiao2021uncertainty,
  title={Uncertainty-guided model generalization to unseen domains},
  author={Qiao, Fengchun and Peng, Xi},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={6790--6800},
  year={2021}
}
@inproceedings{fatras2021unbalanced,
  title={Unbalanced minibatch optimal transport; applications to domain adaptation},
  author={Fatras, Kilian and S{\'e}journ{\'e}, Thibault and Flamary, R{\'e}mi and Courty, Nicolas},
  booktitle={International Conference on Machine Learning},
  pages={3186--3197},
  year={2021},
  organization={PMLR}
}
@inproceedings{tzeng2017adversarial,
  title={Adversarial discriminative domain adaptation},
  author={Tzeng, Eric and Hoffman, Judy and Saenko, Kate and Darrell, Trevor},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={7167--7176},
  year={2017}
}
@inproceedings{araslanov2021self,
  title={Self-supervised augmentation consistency for adapting semantic segmentation},
  author={Araslanov, Nikita and Roth, Stefan},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={15384--15394},
  year={2021}
}
@inproceedings{zhang2021prototypical,
  title={Prototypical pseudo label denoising and target structure learning for domain adaptive semantic segmentation},
  author={Zhang, Pan and Zhang, Bo and Zhang, Ting and Chen, Dong and Wang, Yong and Wen, Fang},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={12414--12424},
  year={2021}
}
@inproceedings{li2022class,
  title={Class-Balanced Pixel-Level Self-Labeling for Domain Adaptive Semantic Segmentation},
  author={Li, Ruihuang and Li, Shuai and He, Chenhang and Zhang, Yabin and Jia, Xu and Zhang, Lei},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={11593--11603},
  year={2022}
}
@inproceedings{sakaridis2019guided,
  title={Guided curriculum model adaptation and uncertainty-aware evaluation for semantic nighttime image segmentation},
  author={Sakaridis, Christos and Dai, Dengxin and Gool, Luc Van},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={7374--7383},
  year={2019}
}
@article{nado2020evaluating,
  title={Evaluating prediction-time batch normalization for robustness under covariate shift},
  author={Nado, Zachary and Padhy, Shreyas and Sculley, D and D'Amour, Alexander and Lakshminarayanan, Balaji and Snoek, Jasper},
  journal={arXiv preprint arXiv:2006.10963},
  year={2020}
}
@inproceedings{wang2022continual,
  title={Continual test-time domain adaptation},
  author={Wang, Qin and Fink, Olga and Van Gool, Luc and Dai, Dengxin},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={7201--7211},
  year={2022}
}

@article{hullermeier2021aleatoric,
  title={Aleatoric and epistemic uncertainty in machine learning: An introduction to concepts and methods},
  author={H{\"u}llermeier, Eyke and Waegeman, Willem},
  journal={Machine Learning},
  volume={110},
  number={3},
  pages={457--506},
  year={2021},
  publisher={Springer}
}
@inproceedings{he2020momentum,
  title={Momentum contrast for unsupervised visual representation learning},
  author={He, Kaiming and Fan, Haoqi and Wu, Yuxin and Xie, Saining and Girshick, Ross},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={9729--9738},
  year={2020}
}
@article{hubalanced,
  title={A Balanced and Uncertainty-aware Approach for Partial Domain Adaptation},
  author={Hu, Ran He and Feng, Jiashi}
}
@inproceedings{wang2021uncertainty,
  title={Uncertainty-aware pseudo label refinery for domain adaptive semantic segmentation},
  author={Wang, Yuxi and Peng, Junran and Zhang, ZhaoXiang},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={9092--9101},
  year={2021}
}
@inproceedings{arpit2017closer,
  title={A closer look at memorization in deep networks},
  author={Arpit, Devansh and Jastrzebski, Stanislaw and Ballas, Nicolas and Krueger, David and Bengio, Emmanuel and Kanwal, Maxinder S and Maharaj, Tegan and Fischer, Asja and Courville, Aaron and Bengio, Yoshua and others},
  booktitle={International conference on machine learning},
  pages={233--242},
  year={2017},
  organization={PMLR}
}
@inproceedings{huang2020contextual,
  title={Contextual-relation consistent domain adaptation for semantic segmentation},
  author={Huang, Jiaxing and Lu, Shijian and Guan, Dayan and Zhang, Xiaobing},
  booktitle={European conference on computer vision},
  pages={705--722},
  year={2020},
  organization={Springer}
}
@article{kim2021domain,
  title={Domain adaptation without source data},
  author={Kim, Youngeun and Cho, Donghyeon and Han, Kyeongtak and Panda, Priyadarshini and Hong, Sungeun},
  journal={IEEE Transactions on Artificial Intelligence},
  year={2021},
  publisher={IEEE}
}
@inproceedings{zhou2018minimax,
  title={Minimax curriculum learning: Machine teaching with desirable difficulties and scheduled diversity},
  author={Zhou, Tianyi and Bilmes, Jeff},
  booktitle={International Conference on Learning Representations},
  year={2018}
}
@inproceedings{hoffman2018cycada,
  title={Cycada: Cycle-consistent adversarial domain adaptation},
  author={Hoffman, Judy and Tzeng, Eric and Park, Taesung and Zhu, Jun-Yan and Isola, Phillip and Saenko, Kate and Efros, Alexei and Darrell, Trevor},
  booktitle={International conference on machine learning},
  pages={1989--1998},
  year={2018},
  organization={Pmlr}
}
@inproceedings{pan2020unsupervised,
  title={Unsupervised intra-domain adaptation for semantic segmentation through self-supervision},
  author={Pan, Fei and Shin, Inkyu and Rameau, Francois and Lee, Seokju and Kweon, In So},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={3764--3773},
  year={2020}
}
@article{huang2021model,
  title={Model adaptation: Historical contrastive learning for unsupervised domain adaptation without source data},
  author={Huang, Jiaxing and Guan, Dayan and Xiao, Aoran and Lu, Shijian},
  journal={Advances in Neural Information Processing Systems},
  volume={34},
  pages={3635--3649},
  year={2021}
}
@inproceedings{sivaprasad2021uncertainty,
  title={Uncertainty reduction for model adaptation in semantic segmentation},
  author={Sivaprasad, Prabhu Teja and Fleuret, Francois},
  booktitle={2021 Ieee/Cvf Conference On Computer Vision And Pattern Recognition, Cvpr 2021},
  number={CONF},
  pages={9608--9618},
  year={2021},
  organization={IEEE}
}

@inproceedings{zou2019confidence,
  title={Confidence regularized self-training},
  author={Zou, Yang and Yu, Zhiding and Liu, Xiaofeng and Kumar, BVK and Wang, Jinsong},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={5982--5991},
  year={2019}
}
@inproceedings{cordts2016cityscapes,
  title={The cityscapes dataset for semantic urban scene understanding},
  author={Cordts, Marius and Omran, Mohamed and Ramos, Sebastian and Rehfeld, Timo and Enzweiler, Markus and Benenson, Rodrigo and Franke, Uwe and Roth, Stefan and Schiele, Bernt},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={3213--3223},
  year={2016}
}

@article{krizhevsky2017imagenet,
  title={Imagenet classification with deep convolutional neural networks},
  author={Krizhevsky, Alex and Sutskever, Ilya and Hinton, Geoffrey E},
  journal={Communications of the ACM},
  volume={60},
  number={6},
  pages={84--90},
  year={2017},
  publisher={AcM New York, NY, USA}
}
@inproceedings{jin2020minimum,
  title={Minimum class confusion for versatile domain adaptation},
  author={Jin, Ying and Wang, Ximei and Long, Mingsheng and Wang, Jianmin},
  booktitle={European Conference on Computer Vision},
  pages={464--480},
  year={2020},
  organization={Springer}
}
@inproceedings{prabhu2022augmentation,
  title={Augmentation Consistency-guided Self-training for Source-free Domain Adaptive Semantic Segmentation},
  author={Prabhu, Viraj Uday and Khare, Shivam and Kartik, Deeksha and Hoffman, Judy},
  booktitle={NeurIPS 2022 Workshop on Distribution Shifts: Connecting Methods and Applications}
}
@article{liu2020early,
  title={Early-learning regularization prevents memorization of noisy labels},
  author={Liu, Sheng and Niles-Weed, Jonathan and Razavian, Narges and Fernandez-Granda, Carlos},
  journal={Advances in neural information processing systems},
  volume={33},
  pages={20331--20342},
  year={2020}
}

@article{zhang2021learning,
  title={Learning with feature-dependent label noise: A progressive approach},
  author={Zhang, Yikai and Zheng, Songzhu and Wu, Pengxiang and Goswami, Mayank and Chen, Chao},
  journal={arXiv preprint arXiv:2103.07756},
  year={2021}
}
@article{zhang2018generalized,
  title={Generalized cross entropy loss for training deep neural networks with noisy labels},
  author={Zhang, Zhilu and Sabuncu, Mert},
  journal={Advances in neural information processing systems},
  volume={31},
  year={2018}
}
@inproceedings{cubuk2019autoaugment,
  title={Autoaugment: Learning augmentation strategies from data},
  author={Cubuk, Ekin D and Zoph, Barret and Mane, Dandelion and Vasudevan, Vijay and Le, Quoc V},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={113--123},
  year={2019}
}

@article{csurka2017comprehensive,
  title={A comprehensive survey on domain adaptation for visual applications},
  author={Csurka, Gabriela},
  journal={Domain adaptation in computer vision applications},
  pages={1--35},
  year={2017},
  publisher={Springer}
}

@article{wang2018deep,
  title={Deep visual domain adaptation: A survey},
  author={Wang, Mei and Deng, Weihong},
  journal={Neurocomputing},
  volume={312},
  pages={135--153},
  year={2018},
  publisher={Elsevier}
}

@inproceedings{damodaran2018deepjdot,
  title={Deepjdot: Deep joint distribution optimal transport for unsupervised domain adaptation},
  author={Damodaran, Bharath Bhushan and Kellenberger, Benjamin and Flamary, R{\'e}mi and Tuia, Devis and Courty, Nicolas},
  booktitle={Proceedings of the European Conference on Computer Vision (ECCV)},
  pages={447--463},
  year={2018}
}

@inproceedings{chen2020graph,
  title={Graph optimal transport for cross-domain alignment},
  author={Chen, Liqun and Gan, Zhe and Cheng, Yu and Li, Linjie and Carin, Lawrence and Liu, Jingjing},
  booktitle={International Conference on Machine Learning},
  pages={1542--1553},
  year={2020},
  organization={PMLR}
}

@inproceedings{murez2018image,
  title={Image to image translation for domain adaptation},
  author={Murez, Zak and Kolouri, Soheil and Kriegman, David and Ramamoorthi, Ravi and Kim, Kyungnam},
  booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
  pages={4500--4509},
  year={2018}
}

@inproceedings{lee2018diverse,
  title={Diverse image-to-image translation via disentangled representations},
  author={Lee, Hsin-Ying and Tseng, Hung-Yu and Huang, Jia-Bin and Singh, Maneesh and Yang, Ming-Hsuan},
  booktitle={Proceedings of the European conference on computer vision (ECCV)},
  pages={35--51},
  year={2018}
}

@article{long2018conditional,
  title={Conditional adversarial domain adaptation},
  author={Long, Mingsheng and Cao, Zhangjie and Wang, Jianmin and Jordan, Michael I},
  journal={Advances in neural information processing systems},
  volume={31},
  year={2018}
}

@inproceedings{vu2019advent,
  title={Advent: Adversarial entropy minimization for domain adaptation in semantic segmentation},
  author={Vu, Tuan-Hung and Jain, Himalaya and Bucher, Maxime and Cord, Matthieu and P{\'e}rez, Patrick},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2517--2526},
  year={2019}
}


@inproceedings{shen2018wasserstein,
  title={Wasserstein distance guided representation learning for domain adaptation},
  author={Shen, Jian and Qu, Yanru and Zhang, Weinan and Yu, Yong},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  volume={32},
  number={1},
  year={2018}
}

@inproceedings{sun2016deep,
  title={Deep coral: Correlation alignment for deep domain adaptation},
  author={Sun, Baochen and Saenko, Kate},
  booktitle={European conference on computer vision},
  pages={443--450},
  year={2016},
  organization={Springer}
}

@inproceedings{chen2020homm,
  title={Homm: Higher-order moment matching for unsupervised domain adaptation},
  author={Chen, Chao and Fu, Zhihang and Chen, Zhihong and Jin, Sheng and Cheng, Zhaowei and Jin, Xinyu and Hua, Xian-Sheng},
  booktitle={Proceedings of the AAAI conference on artificial intelligence},
  volume={34},
  number={04},
  pages={3422--3429},
  year={2020}
}

@inproceedings{li2020domain,
  title={Domain conditioned adaptation network},
  author={Li, Shuang and Liu, Chi and Lin, Qiuxia and Xie, Binhui and Ding, Zhengming and Huang, Gao and Tang, Jian},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  volume={34},
  number={07},
  pages={11386--11393},
  year={2020}
}

@inproceedings{xie2020self,
  title={Self-training with noisy student improves imagenet classification},
  author={Xie, Qizhe and Luong, Minh-Thang and Hovy, Eduard and Le, Quoc V},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={10687--10698},
  year={2020}
}

@inproceedings{mei2020instance,
  title={Instance adaptive self-training for unsupervised domain adaptation},
  author={Mei, Ke and Zhu, Chuang and Zou, Jiaqi and Zhang, Shanghang},
  booktitle={European conference on computer vision},
  pages={415--430},
  year={2020},
  organization={Springer}
}

@inproceedings{zou2018unsupervised,
  title={Unsupervised domain adaptation for semantic segmentation via class-balanced self-training},
  author={Zou, Yang and Yu, Zhiding and Kumar, BVK and Wang, Jinsong},
  booktitle={Proceedings of the European conference on computer vision (ECCV)},
  pages={289--305},
  year={2018}
}

@inproceedings{yu2021dast,
  title={Dast: Unsupervised domain adaptation in semantic segmentation based on discriminator attention and self-training},
  author={Yu, Fei and Zhang, Mo and Dong, Hexin and Hu, Sheng and Dong, Bin and Zhang, Li},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  volume={35},
  number={12},
  pages={10754--10762},
  year={2021}
}

@inproceedings{li2020enhanced,
  title={Enhanced transport distance for unsupervised domain adaptation},
  author={Li, Mengxue and Zhai, Yi-Ming and Luo, You-Wei and Ge, Peng-Fei and Ren, Chuan-Xian},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={13936--13944},
  year={2020}
}

@inproceedings{wu2021dannet,
  title={Dannet: A one-stage domain adaptation network for unsupervised nighttime semantic segmentation},
  author={Wu, Xinyi and Wu, Zhenyao and Guo, Hao and Ju, Lili and Wang, Song},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={15769--15778},
  year={2021}
}

@inproceedings{ding2022source,
  title={Source-Free Domain Adaptation via Distribution Estimation},
  author={Ding, Ning and Xu, Yixing and Tang, Yehui and Xu, Chao and Wang, Yunhe and Tao, Dacheng},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={7212--7222},
  year={2022}
}
@misc{imgaug,
  author = {Jung, Alexander B.
            and Wada, Kentaro
            and Crall, Jon
            and Tanaka, Satoshi
            and Graving, Jake
            and Reinders, Christoph
            and Yadav, Sarthak
            and Banerjee, Joy
            and Vecsei, Gábor
            and Kraft, Adam
            and Rui, Zheng
            and Borovec, Jirka
            and Vallentin, Christian
            and Zhydenko, Semen
            and Pfeiffer, Kilian
            and Cook, Ben
            and Fernández, Ismael
            and De Rainville, François-Michel
            and Weng, Chi-Hung
            and Ayala-Acevedo, Abner
            and Meudec, Raphael
            and Laporte, Matias
            and others},
  title = {{imgaug}},
  howpublished = {\url{https://github.com/aleju/imgaug}},
  year = {2020},
  note = {Online; accessed 01-Feb-2020}
}
@inproceedings{xu2020reliable,
  title={Reliable weighted optimal transport for unsupervised domain adaptation},
  author={Xu, Renjun and Liu, Pelen and Wang, Liyan and Chen, Chao and Wang, Jindong},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={4394--4403},
  year={2020}
}

@article{shorten2019survey,
  title={A survey on image data augmentation for deep learning},
  author={Shorten, Connor and Khoshgoftaar, Taghi M},
  journal={Journal of big data},
  volume={6},
  number={1},
  pages={1--48},
  year={2019},
  publisher={SpringerOpen}
}
@article{michaelis2019benchmarking,
  title={Benchmarking robustness in object detection: Autonomous driving when winter is coming},
  author={Michaelis, Claudio and Mitzkus, Benjamin and Geirhos, Robert and Rusak, Evgenia and Bringmann, Oliver and Ecker, Alexander S and Bethge, Matthias and Brendel, Wieland},
  journal={arXiv preprint arXiv:1907.07484},
  year={2019}
}
@inproceedings{guillory2021predicting,
  title={Predicting with confidence on unseen distributions},
  author={Guillory, Devin and Shankar, Vaishaal and Ebrahimi, Sayna and Darrell, Trevor and Schmidt, Ludwig},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={1134--1144},
  year={2021}
}
@article{chen2017deeplab,
  title={Deeplab: Semantic image segmentation with deep convolutional nets, atrous convolution, and fully connected crfs},
  author={Chen, Liang-Chieh and Papandreou, George and Kokkinos, Iasonas and Murphy, Kevin and Yuille, Alan L},
  journal={IEEE transactions on pattern analysis and machine intelligence},
  volume={40},
  number={4},
  pages={834--848},
  year={2017},
  publisher={IEEE}
}
@inproceedings{deng2009imagenet,
  title={Imagenet: A large-scale hierarchical image database},
  author={Deng, Jia and Dong, Wei and Socher, Richard and Li, Li-Jia and Li, Kai and Fei-Fei, Li},
  booktitle={2009 IEEE conference on computer vision and pattern recognition},
  pages={248--255},
  year={2009},
  organization={Ieee}
}
@inproceedings{ros2016synthia,
  title={The synthia dataset: A large collection of synthetic images for semantic segmentation of urban scenes},
  author={Ros, German and Sellart, Laura and Materzynska, Joanna and Vazquez, David and Lopez, Antonio M},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={3234--3243},
  year={2016}
}

@article{prabhu2022augco,
  title={AUGCO: Augmentation Consistency-guided Self-training for Source-free Domain Adaptive Semantic Segmentation},
  author={Prabhu, Viraj Uday and Khare, Shivam and Kartik, Deeksha and Hoffman, Judy},
  journal={arXiv preprint arXiv:2107.10140},
  year={2022}
}

@inproceedings{richter2016playing,
  title={Playing for data: Ground truth from computer games},
  author={Richter, Stephan R and Vineet, Vibhav and Roth, Stefan and Koltun, Vladlen},
  booktitle={European conference on computer vision},
  pages={102--118},
  year={2016},
  organization={Springer}
}

@article{khosla2020supervised,
  title={Supervised contrastive learning},
  author={Khosla, Prannay and Teterwak, Piotr and Wang, Chen and Sarna, Aaron and Tian, Yonglong and Isola, Phillip and Maschinot, Aaron and Liu, Ce and Krishnan, Dilip},
  journal={Advances in Neural Information Processing Systems},
  volume={33},
  pages={18661--18673},
  year={2020}
}

@inproceedings{kang2019contrastive,
  title={Contrastive adaptation network for unsupervised domain adaptation},
  author={Kang, Guoliang and Jiang, Lu and Yang, Yi and Hauptmann, Alexander G},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={4893--4902},
  year={2019}
}
@inproceedings{chen2022contrastive,
  title={Contrastive Test-Time Adaptation},
  author={Chen, Dian and Wang, Dequan and Darrell, Trevor and Ebrahimi, Sayna},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={295--305},
  year={2022}
}
@article{salimans2016weight,
  title={Weight normalization: A simple reparameterization to accelerate training of deep neural networks},
  author={Salimans, Tim and Kingma, Durk P},
  journal={Advances in neural information processing systems},
  volume={29},
  year={2016}
}
@inproceedings{ioffe2015batch,
  title={Batch normalization: Accelerating deep network training by reducing internal covariate shift},
  author={Ioffe, Sergey and Szegedy, Christian},
  booktitle={International conference on machine learning},
  pages={448--456},
  year={2015},
  organization={PMLR}
}
@inproceedings{G-SFDA,
  title={Generalized Source-free Domain Adaptation},
  author={Yang, Shiqi and Wang, Yaxing and van de Weijer, Joost and Herranz, Luis and Jui, Shangling},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={8978--8987},
  year={2021}
}

@inproceedings{A2Net,
  title={Adaptive Adversarial Network for Source-Free Domain Adaptation},
  author={Xia, Haifeng and Zhao, Handong and Ding, Zhengming},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={9010--9019},
  year={2021}
}
@article{peng2017visda,
  title={Visda: The visual domain adaptation challenge},
  author={Peng, Xingchao and Usman, Ben and Kaushik, Neela and Hoffman, Judy and Wang, Dequan and Saenko, Kate},
  journal={arXiv preprint arXiv:1710.06924},
  year={2017}
}
@inproceedings{venkateswara2017deep,
  title={Deep hashing network for unsupervised domain adaptation},
  author={Venkateswara, Hemanth and Eusebio, Jose and Chakraborty, Shayok and Panchanathan, Sethuraman},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={5018--5027},
  year={2017}
}

@inproceedings{bengio2009curriculum,
  title={Curriculum learning},
  author={Bengio, Yoshua and Louradour, J{\'e}r{\^o}me and Collobert, Ronan and Weston, Jason},
  booktitle={Proceedings of the 26th annual international conference on machine learning},
  pages={41--48},
  year={2009}
}
@article{zhou2003learning,
  title={Learning with local and global consistency},
  author={Zhou, Dengyong and Bousquet, Olivier and Lal, Thomas and Weston, Jason and Sch{\"o}lkopf, Bernhard},
  journal={Advances in neural information processing systems},
  volume={16},
  year={2003}
}
% Related Work================



@inproceedings{xu2018deep,
	title={Deep cocktail network: Multi-source unsupervised domain adaptation with category shift},
	author={Xu, Ruijia and Chen, Ziliang and Zuo, Wangmeng and Yan, Junjie and Lin, Liang},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={3964--3973},
	year={2018}
}

@inproceedings{he2020classification,
	title={Classification-aware semi-supervised domain adaptation},
	author={He, Gewen and Liu, Xiaofeng and Fan, Fangfang and You, Jane},
	booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops},
	pages={964--965},
	year={2020}
}
@inproceedings{saito2019semi,
	title={Semi-supervised domain adaptation via minimax entropy},
	author={Saito, Kuniaki and Kim, Donghyun and Sclaroff, Stan and Darrell, Trevor and Saenko, Kate},
	booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
	pages={8050--8058},
	year={2019}
}
@inproceedings{cao2018partial,
	title={Partial transfer learning with selective adversarial networks},
	author={Cao, Zhangjie and Long, Mingsheng and Wang, Jianmin and Jordan, Michael I},
	booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
	pages={2724--2732},
	year={2018}
}
@inproceedings{panareda2017open,
	title={Open set domain adaptation},
	author={Panareda Busto, Pau and Gall, Juergen},
	booktitle={Proceedings of the IEEE International Conference on Computer Vision},
	pages={754--763},
	year={2017}
}
@inproceedings{compounddomainadaptation,
	title={Open Compound Domain Adaptation},
	author={Liu, Ziwei and Miao, Zhongqi and Pan, Xingang and Zhan, Xiaohang and Lin, Dahua and Yu, Stella X. and Gong, Boqing},
	booktitle={IEEE Conference on Computer Vision and Pattern Recognition (CVPR)},
	year={2020}
}
@article{saito2020universal,
	title={Universal Domain Adaptation through Self Supervision},
	author={Saito, Kuniaki and Kim, Donghyun and Sclaroff, Stan and Saenko, Kate},
	journal={Advances in Neural Information Processing Systems},
	volume={33},
	year={2020}
}
@inproceedings{song2009hilbert,
	title={Hilbert space embeddings of conditional distributions with applications to dynamical systems},
	author={Song, Le and Huang, Jonathan and Smola, Alex and Fukumizu, Kenji},
	booktitle={Proceedings of the 26th Annual International Conference on Machine Learning},
	pages={961--968},
	year={2009}
}
@article{gretton2012kernel,
	title={A kernel two-sample test},
	author={Gretton, Arthur and Borgwardt, Karsten M and Rasch, Malte J and Sch{\"o}lkopf, Bernhard and Smola, Alexander},
	journal={The Journal of Machine Learning Research},
	volume={13},
	number={1},
	pages={723--773},
	year={2012},
	publisher={JMLR. org}
}

@inproceedings{long2015learning,
	title={Learning transferable features with deep adaptation networks},
	author={Long, Mingsheng and Cao, Yue and Wang, Jianmin and Jordan, Michael},
	booktitle={International conference on machine learning},
	pages={97--105},
	year={2015},
	organization={PMLR}
}
@article{long2016unsupervised,
  title={Unsupervised Domain Adaptation with Residual Transfer Networks},
  author={Long, Mingsheng and Zhu, Han and Wang, Jianmin and Jordan, Michael I},
  journal={Advances in Neural Information Processing Systems},
  volume={29},
  pages={136--144},
  year={2016}
}
@inproceedings{long2017deep,
	title={Deep transfer learning with joint adaptation networks},
	author={Long, Mingsheng and Zhu, Han and Wang, Jianmin and Jordan, Michael I},
	booktitle={International conference on machine learning},
	pages={2208--2217},
	year={2017},
	organization={PMLR}
}
@inproceedings{iyer2014maximum,
	title={Maximum mean discrepancy for class ratio estimation: Convergence bounds and kernel selection},
	author={Iyer, Arun and Nath, Saketha and Sarawagi, Sunita},
	booktitle={International Conference on Machine Learning},
	pages={530--538},
	year={2014},
	organization={PMLR}
}
@inproceedings{CAN,
	title={Contrastive adaptation network for unsupervised domain adaptation},
	author={Kang, Guoliang and Jiang, Lu and Yang, Yi and Hauptmann, Alexander G},
	booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
	pages={4893--4902},
	year={2019}
}
@inproceedings{chen2020simple,
	title={A simple framework for contrastive learning of visual representations},
	author={Chen, Ting and Kornblith, Simon and Norouzi, Mohammad and Hinton, Geoffrey},
	booktitle={International conference on machine learning},
	pages={1597--1607},
	year={2020},
	organization={PMLR}
}
@article{zellinger2017central,
  title={Robust unsupervised domain adaptation for neural networks via moment alignment},
  author={Zellinger, Werner and Moser, Bernhard A and Grubinger, Thomas and Lughofer, Edwin and Natschl{\"a}ger, Thomas and Saminger-Platz, Susanne},
  journal={Information Sciences},
  volume={483},
  pages={174--191},
  year={2019},
  publisher={Elsevier}
}
@incollection{sun2017correlation,
	title={Correlation alignment for unsupervised domain adaptation},
	author={Sun, Baochen and Feng, Jiashi and Saenko, Kate},
	booktitle={Domain Adaptation in Computer Vision Applications},
	pages={153--171},
	year={2017},
	publisher={Springer}
}

@article{ganin2016domain,
	title={Domain-adversarial training of neural networks},
	author={Ganin, Yaroslav and Ustinova, Evgeniya and Ajakan, Hana and Germain, Pascal and Larochelle, Hugo and Laviolette, Fran{\c{c}}ois and Marchand, Mario and Lempitsky, Victor},
	journal={The journal of machine learning research},
	volume={17},
	number={1},
	pages={2096--2030},
	year={2016},
	publisher={JMLR. org}
}

% ==============================
@article{li2018semi,
  title={Semi-supervised domain adaptation by covariance matching},
  author={Li, Limin and Zhang, Zhenyue},
  journal={IEEE transactions on pattern analysis and machine intelligence},
  volume={41},
  number={11},
  pages={2724--2739},
  year={2018},
  publisher={IEEE}
}
@article{cui2014flowing,
  title={Flowing on Riemannian manifold: Domain adaptation by shifting covariance},
  author={Cui, Zhen and Li, Wen and Xu, Dong and Shan, Shiguang and Chen, Xilin and Li, Xuelong},
  journal={IEEE transactions on cybernetics},
  volume={44},
  number={12},
  pages={2264--2273},
  year={2014},
  publisher={IEEE}
}
@article{wang2019implicit,
  title={Implicit semantic data augmentation for deep networks},
  author={Wang, Yulin and Pan, Xuran and Song, Shiji and Zhang, Hong and Huang, Gao and Wu, Cheng},
  journal={Advances in Neural Information Processing Systems},
  volume={32},
  pages={12635--12644},
  year={2019}
}
@inproceedings{yeh2021sofa,
  title={Sofa: Source-data-free feature alignment for unsupervised domain adaptation},
  author={Yeh, Hao-Wei and Yang, Baoyao and Yuen, Pong C and Harada, Tatsuya},
  booktitle={Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision},
  pages={474--483},
  year={2021}
}
@inproceedings{liu2021source,
  title={Source-free domain adaptation for semantic segmentation},
  author={Liu, Yuang and Zhang, Wei and Wang, Jun},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={1215--1224},
  year={2021}
}
@article{xu2019positive,
  title={Positive-unlabeled compression on the cloud},
  author={Xu, Yixing and Wang, Yunhe and Chen, Hanting and Han, Kai and Xu, Chunjing and Tao, Dacheng and Xu, Chang},
  journal={Advances in Neural Information Processing Systems},
  volume={32},
  year={2019}
}
@article{xu2021learning,
  title={Learning frequency domain approximation for binary neural networks},
  author={Xu, Yixing and Han, Kai and Xu, Chang and Tang, Yehui and Xu, Chunjing and Wang, Yunhe},
  journal={Advances in Neural Information Processing Systems},
  volume={34},
  year={2021}
}
% ==============================
@inproceedings{GSDA,
  title={Unsupervised domain adaptation with hierarchical gradient synchronization},
  author={Hu, Lanqing and Kan, Meina and Shan, Shiguang and Chen, Xilin},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={4043--4052},
  year={2020}
}
@inproceedings{GVB,
  title={Gradually vanishing bridge for adversarial domain adaptation},
  author={Cui, Shuhao and Wang, Shuhui and Zhuo, Junbao and Su, Chi and Huang, Qingming and Tian, Qi},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={12455--12464},
  year={2020}
}
@InProceedings{RSDA,
author = {Gu, Xiang and Sun, Jian and Xu, Zongben},
title = {Spherical Space Domain Adaptation With Robust Pseudo-Label Loss},
booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
month = {June},
year = {2020}
}
@inproceedings{SRDC,
  title={Unsupervised domain adaptation via structurally regularized deep clustering},
  author={Tang, Hui and Chen, Ke and Jia, Kui},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={8725--8735},
  year={2020}
}
@InProceedings{FixBi,
    author    = {Na, Jaemin and Jung, Heechul and Chang, Hyung Jin and Hwang, Wonjun},
    title     = {FixBi: Bridging Domain Spaces for Unsupervised Domain Adaptation},
    booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
    month     = {June},
    year      = {2021},
    pages     = {1094-1103}
}
@inproceedings{li2021transferable,
  title={Transferable Semantic Augmentation for Domain Adaptation},
  author={Li, Shuang and Xie, Mixue and Gong, Kaixiong and Liu, Chi Harold and Wang, Yulin and Li, Wei},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={11516--11525},
  year={2021}
}
@inproceedings{zhang2019bridging,
  title={Bridging theory and algorithm for domain adaptation},
  author={Zhang, Yuchen and Liu, Tianle and Long, Mingsheng and Jordan, Michael},
  booktitle={International Conference on Machine Learning},
  pages={7404--7413},
  year={2019},
  organization={PMLR}
}
@inproceedings{lee2019sliced,
  title={Sliced wasserstein discrepancy for unsupervised domain adaptation},
  author={Lee, Chen-Yu and Batra, Tanmay and Baig, Mohammad Haris and Ulbricht, Daniel},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={10285--10295},
  year={2019}
}
@inproceedings{MCC,
	title={Minimum Class Confusion for Versatile Domain Adaptation},
	author={Jin, Ying and Wang, Ximei and Long, Mingsheng and Wang, Jianmin},
	booktitle={European Conference on Computer Vision},
	pages={464--480},
	year={2020},
	organization={Springer}
}
@inproceedings{STAR,
  title={Stochastic classifiers for unsupervised domain adaptation},
  author={Lu, Zhihe and Yang, Yongxin and Zhu, Xiatian and Liu, Cong and Song, Yi-Zhe and Xiang, Tao},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={9111--9120},
  year={2020}
}

@inproceedings{RWOT,
  title={Reliable weighted optimal transport for unsupervised domain adaptation},
  author={Xu, Renjun and Liu, Pelen and Wang, Liyan and Chen, Chao and Wang, Jindong},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={4394--4403},
  year={2020}
}

@inproceedings{SFAN,
  author={Xu, Ruijia and Li, Guanbin and Yang, Jihan and Lin, Liang},
  booktitle={2019 IEEE/CVF International Conference on Computer Vision (ICCV)}, 
  title={Larger Norm More Transferable: An Adaptive Feature Norm Approach for Unsupervised Domain Adaptation}, 
  year={2019},
  pages={1426-1435},
  doi={10.1109/ICCV.2019.00151}
 }
  
@inproceedings{SE,
  title={Self-ensembling for visual domain adaptation},
  author={French, Geoffrey and Mackiewicz, Michal and Fisher, Mark},
  booktitle={International Conference on Learning Representations},
  number={6},
  year={2018}
}

@inproceedings{tsai2018learning,
  title={Learning to adapt structured output space for semantic segmentation},
  author={Tsai, Yi-Hsuan and Hung, Wei-Chih and Schulter, Samuel and Sohn, Kihyuk and Yang, Ming-Hsuan and Chandraker, Manmohan},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={7472--7481},
  year={2018}
}

@inproceedings{wang2021tent,
  title={Tent: Fully Test-Time Adaptation by Entropy Minimization},
  author={Wang, Dequan and Shelhamer, Evan and Liu, Shaoteng and Olshausen, Bruno and Darrell, Trevor},
  booktitle={International Conference on Learning Representations},
  year={2021}
}
% ==============================

% ==============================
@article{pytorch,
  title={Automatic differentiation in pytorch},
  author={Paszke, Adam and Gross, Sam and Chintala, Soumith and Chanan, Gregory and Yang, Edward and DeVito, Zachary and Lin, Zeming and Desmaison, Alban and Antiga, Luca and Lerer, Adam},
  year={2017}
}

@misc{mindspore,
   author = {Huawei},
   title = {Mindspore},
   howpublished = {\url{https://www.mindspore.cn/}},
   year={2020}

}

@inproceedings{karim2023c,
  title={C-sfda: A curriculum learning aided self-training framework for efficient source free domain adaptation},
  author={Karim, Nazmul and Mithun, Niluthpol Chowdhury and Rajvanshi, Abhinav and Chiu, Han-pang and Samarasekera, Supun and Rahnavard, Nazanin},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={24120--24131},
  year={2023}
}

