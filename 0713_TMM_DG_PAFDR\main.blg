This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: main.aux
Reallocating 'name_of_file' (item size: 1) to 9 items.
The style file: IEEEtran.bst
Reallocating 'name_of_file' (item size: 1) to 6 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Reallocating 'wiz_functions' (item size: 4) to 6000 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Database file #1: arxiv.bib
Repeated entry---line 7869 of file arxiv.bib
 : @inproceedings{zhou2020learning
 :                                ,
I'm skipping whatever remains of this entry
Repeated entry---line 7921 of file arxiv.bib
 : @inproceedings{wang2020learning
 :                                ,
I'm skipping whatever remains of this entry
Repeated entry---line 7939 of file arxiv.bib
 : @article{segu2023batch
 :                       ,
I'm skipping whatever remains of this entry
Repeated entry---line 8111 of file arxiv.bib
 : @inproceedings{carlucci2019domain
 :                                  ,
I'm skipping whatever remains of this entry
Repeated entry---line 8153 of file arxiv.bib
 : @inproceedings{he2016deep
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 8711 of file arxiv.bib
 : @inproceedings{he2016deep
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 8940 of file arxiv.bib
 : @inproceedings{he2016deep
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 8954 of file arxiv.bib
 : @inproceedings{peng2019moment
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 9315 of file arxiv.bib
 : @inproceedings{li2020domain
 :                            ,
I'm skipping whatever remains of this entry
Repeated entry---line 9515 of file arxiv.bib
 : @inproceedings{ioffe2015batch
 :                              ,
I'm skipping whatever remains of this entry
-- IEEEtran.bst version 1.14 (2015/08/26) by Michael Shell.
-- http://www.michaelshell.org/tex/ieeetran/bibtex/
-- See the "IEEEtran_bst_HOWTO.pdf" manual for usage information.

Done.
You've used 41 entries,
            4087 wiz_defined-function locations,
            1107 strings with 16309 characters,
and the built_in function-call counts, 33539 in all, are:
= -- 2501
> -- 1037
< -- 244
+ -- 564
- -- 207
* -- 1599
:= -- 4633
add.period$ -- 82
call.type$ -- 41
change.case$ -- 41
chr.to.int$ -- 612
cite$ -- 41
duplicate$ -- 2397
empty$ -- 2843
format.name$ -- 239
if$ -- 7935
int.to.chr$ -- 0
int.to.str$ -- 41
missing$ -- 452
newline$ -- 146
num.names$ -- 41
pop$ -- 1182
preamble$ -- 1
purify$ -- 0
quote$ -- 2
skip$ -- 2596
stack$ -- 0
substring$ -- 1527
swap$ -- 1888
text.length$ -- 40
text.prefix$ -- 0
top$ -- 5
type$ -- 41
warning$ -- 0
while$ -- 128
width$ -- 43
write$ -- 390
(There were 10 error messages)
