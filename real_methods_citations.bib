% BibTeX引用 - 基于真实数据的语义分割方法

@article{hoyer2021daformer,
  title={DAFormer: Improving Network Architectures and Training Strategies for Domain-Adaptive Semantic Segmentation},
  author={<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON>},
  journal={arXiv preprint arXiv:2111.14887},
  year={2021}
}

@inproceedings{xie2022sepico,
  title={SePiCo: Semantic-Guided Pixel Contrast for Domain Adaptive Semantic Segmentation},
  author={<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={9004--9014},
  year={2022}
}

@inproceedings{hoyer2022hrda,
  title={HRDA: Context-Aware High-Resolution Domain-Adaptive Semantic Segmentation},
  author={<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON>},
  booktitle={European Conference on Computer Vision},
  pages={372--391},
  year={2022},
  organization={Springer}
}

@inproceedings{hoyer2022mic,
  title={MIC: Masked Image Consistency for Context-Enhanced Domain Adaptation},
  author={Hoyer, Lukas and Dai, Dengxin and Van Gool, Luc},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={11721--11732},
  year={2022}
}

@inproceedings{chen2022pipa,
  title={PiPa: Pixel- and Patch-wise Self-supervised Learning for Domain Adaptative Semantic Segmentation},
  author={Chen, Mu and Xie, Binhui and Li, Shuang and Liu, Chi Harold and Huang, Gao and Wang, Guoren},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={3918--3928},
  year={2022}
}

@inproceedings{franco2023halo,
  title={Hyperbolic Active Learning for Semantic Segmentation under Domain Shift},
  author={Franco, Luca and Mandica, Paolo and Kallidromitis, Konstantinos and Guillory, Devin and Li, Yu-Teng and Darrell, Trevor and Galasso, Fabio},
  booktitle={International Conference on Machine Learning},
  pages={10134--10155},
  year={2023},
  organization={PMLR}
}

@inproceedings{chen2023ilm,
  title={Iterative Loop Method Combining Active and Semi-Supervised Learning for Domain Adaptive Semantic Segmentation},
  author={Chen, Qiming and Huang, Lingdong and Wang, Jie and Chen, Xueyang and Zhang, Yiming},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={7568--7578},
  year={2023}
}

@article{zhang2024guidance,
  title={Improve Cross-domain Mixed Sampling with Guidance Training for Adaptive Segmentation},
  author={Zhang, Wenlve and He, Zhiyuan and Zhou, Tianfei and Lu, Yutong and Wang, Jianwu},
  journal={arXiv preprint arXiv:2403.14995},
  year={2024}
}

@inproceedings{zheng2021proda,
  title={Prototypical Pseudo Label Denoising and Target Structure Learning for Domain Adaptive Semantic Segmentation},
  author={Zheng, Pan and Wang, Wen and Kao, Chia-Chih and others},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={12414--12424},
  year={2021}
}

@inproceedings{liu2021gta,
  title={Generalize then Adapt: Source-Free Domain Adaptive Semantic Segmentation},
  author={Liu, Qin and Dou, Haoran and Liu, Yurong and Yu, Qilong},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
  pages={7046--7056},
  year={2021}
}
