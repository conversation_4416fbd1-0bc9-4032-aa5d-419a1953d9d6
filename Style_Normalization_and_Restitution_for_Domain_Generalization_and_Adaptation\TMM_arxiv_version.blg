This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: TMM_arxiv_version.aux
Reallocating 'name_of_file' (item size: 1) to 9 items.
The style file: IEEEtran.bst
Reallocating 'name_of_file' (item size: 1) to 9 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Reallocating 'wiz_functions' (item size: 4) to 6000 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Database file #1: IEEEabrv.bib
Repeated entry---line 74 of file IEEEabrv.bib
 : @article{ganin2016domain
 :                         ,
I'm skipping whatever remains of this entry
Repeated entry---line 91 of file IEEEabrv.bib
 : @inproceedings{li2018domain
 :                            ,
I'm skipping whatever remains of this entry
Repeated entry---line 309 of file IEEEabrv.bib
 : @inproceedings{saito2018maximum
 :                                ,
I'm skipping whatever remains of this entry
Repeated entry---line 336 of file IEEEabrv.bib
 : @inproceedings{muandet2013domain
 :                                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 350 of file IEEEabrv.bib
 : @article{shankar2018generalizing
 :                                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 356 of file IEEEabrv.bib
 : @inproceedings{carlucci2019domain
 :                                  ,
I'm skipping whatever remains of this entry
Repeated entry---line 363 of file IEEEabrv.bib
 : @inproceedings{li2019episodic
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 370 of file IEEEabrv.bib
 : @inproceedings{li2018learning
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 376 of file IEEEabrv.bib
 : @inproceedings{peng2019moment
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 396 of file IEEEabrv.bib
 : @inproceedings{li2018domain
 :                            ,
I'm skipping whatever remains of this entry
Repeated entry---line 403 of file IEEEabrv.bib
 : @inproceedings{motiian2017unified
 :                                  ,
I'm skipping whatever remains of this entry
Repeated entry---line 432 of file IEEEabrv.bib
 : @article{long2015learning
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 465 of file IEEEabrv.bib
 : @article{ganin2014unsupervised
 :                               ,
I'm skipping whatever remains of this entry
Repeated entry---line 478 of file IEEEabrv.bib
 : @article{hoffman2018cycada
 :                           ,
I'm skipping whatever remains of this entry
Repeated entry---line 498 of file IEEEabrv.bib
 : @inproceedings{xu2018deep
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 508 of file IEEEabrv.bib
 : @article{ulyanov2016instance
 :                             ,
I'm skipping whatever remains of this entry
Repeated entry---line 514 of file IEEEabrv.bib
 : @inproceedings{ulyanov2017improved
 :                                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 521 of file IEEEabrv.bib
 : @article{dumoulin2016learned
 :                             ,
I'm skipping whatever remains of this entry
Repeated entry---line 527 of file IEEEabrv.bib
 : @inproceedings{huang2017arbitrary
 :                                  ,
I'm skipping whatever remains of this entry
Repeated entry---line 534 of file IEEEabrv.bib
 : @inproceedings{nam2018batch
 :                            ,
I'm skipping whatever remains of this entry
Repeated entry---line 541 of file IEEEabrv.bib
 : @inproceedings{pan2018two
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 548 of file IEEEabrv.bib
 : @article{ioffe2015batch
 :                        ,
I'm skipping whatever remains of this entry
Repeated entry---line 567 of file IEEEabrv.bib
 : @inproceedings{liu2018unified
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 582 of file IEEEabrv.bib
 : @article{peng2019federated
 :                           ,
I'm skipping whatever remains of this entry
Repeated entry---line 616 of file IEEEabrv.bib
 : @inproceedings{lee2018diverse
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 657 of file IEEEabrv.bib
 : @inproceedings{zhao2018adversarial
 :                                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 664 of file IEEEabrv.bib
 : @inproceedings{saito2019semi
 :                             ,
I'm skipping whatever remains of this entry
Repeated entry---line 671 of file IEEEabrv.bib
 : @inproceedings{li2018learning
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 683 of file IEEEabrv.bib
 : @inproceedings{muandet2013domain
 :                                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 690 of file IEEEabrv.bib
 : @article{shankar2018generalizing
 :                                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 841 of file IEEEabrv.bib
 : @article{ganin2016domain
 :                         ,
I'm skipping whatever remains of this entry
Repeated entry---line 859 of file IEEEabrv.bib
 : @inproceedings{long2017deep
 :                            ,
I'm skipping whatever remains of this entry
Warning--string name "jmlr" is undefined
--line 877 of file IEEEabrv.bib
Repeated entry---line 880 of file IEEEabrv.bib
 : @inproceedings{pan2018two
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 892 of file IEEEabrv.bib
 : @article{peng2019federated
 :                           ,
I'm skipping whatever remains of this entry
Warning--string name "bmvc" is undefined
--line 907 of file IEEEabrv.bib
Warning--string name "iccv" is undefined
--line 913 of file IEEEabrv.bib
Repeated entry---line 916 of file IEEEabrv.bib
 : @article{ulyanov2016instance
 :                             ,
I'm skipping whatever remains of this entry
Repeated entry---line 922 of file IEEEabrv.bib
 : @article{ioffe2015batch
 :                        ,
I'm skipping whatever remains of this entry
Repeated entry---line 928 of file IEEEabrv.bib
 : @inproceedings{ulyanov2017improved
 :                                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 935 of file IEEEabrv.bib
 : @inproceedings{huang2017arbitrary
 :                                  ,
I'm skipping whatever remains of this entry
Repeated entry---line 963 of file IEEEabrv.bib
 : @inproceedings{chen2019domain
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 972 of file IEEEabrv.bib
 : @article{he2016deep
 :                    ,
I'm skipping whatever remains of this entry
Repeated entry---line 984 of file IEEEabrv.bib
 : @inproceedings{hu2018squeeze
 :                             ,
I'm skipping whatever remains of this entry
Repeated entry---line 997 of file IEEEabrv.bib
 : @article{ulyanov2017improved
 :                             ,
I'm skipping whatever remains of this entry
Repeated entry---line 1003 of file IEEEabrv.bib
 : @article{dumoulin2016learned
 :                             ,
I'm skipping whatever remains of this entry
Repeated entry---line 1009 of file IEEEabrv.bib
 : @article{huang2017arbitrary
 :                            ,
I'm skipping whatever remains of this entry
Repeated entry---line 1015 of file IEEEabrv.bib
 : @article{ulyanov2017improved
 :                             ,
I'm skipping whatever remains of this entry
Repeated entry---line 1028 of file IEEEabrv.bib
 : @inproceedings{chen2019domain
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 1078 of file IEEEabrv.bib
 : @inproceedings{tsai2018learning
 :                                ,
I'm skipping whatever remains of this entry
Repeated entry---line 1167 of file IEEEabrv.bib
 : @inproceedings{ren2015faster
 :                             ,
I'm skipping whatever remains of this entry
Repeated entry---line 1174 of file IEEEabrv.bib
 : @article{sakaridis2018semantic
 :                               ,
I'm skipping whatever remains of this entry
Warning--string name "cvpr" is undefined
--line 1185 of file IEEEabrv.bib
Repeated entry---line 1198 of file IEEEabrv.bib
 : @inproceedings{jin2020style
 :                            ,
I'm skipping whatever remains of this entry
Warning--string name "cvpr" is undefined
--line 1246 of file IEEEabrv.bib
-- IEEEtran.bst version 1.14 (2015/08/26) by Michael Shell.
-- http://www.michaelshell.org/tex/ieeetran/bibtex/
-- See the "IEEEtran_bst_HOWTO.pdf" manual for usage information.
Warning--empty journal in jia2019frustratingly
Warning--empty booktitle in song2019generalizable
Warning--empty journal in zhou2019omni
Warning--empty booktitle in zheng2011person
Warning--empty journal in maaten2008visualizing

Done.
You've used 88 entries,
            4087 wiz_defined-function locations,
            1196 strings with 21375 characters,
and the built_in function-call counts, 70543 in all, are:
= -- 5233
> -- 2034
< -- 550
+ -- 1110
- -- 366
* -- 3300
:= -- 9694
add.period$ -- 180
call.type$ -- 88
change.case$ -- 90
chr.to.int$ -- 1449
cite$ -- 93
duplicate$ -- 5010
empty$ -- 6170
format.name$ -- 454
if$ -- 16761
int.to.chr$ -- 0
int.to.str$ -- 88
missing$ -- 907
newline$ -- 287
num.names$ -- 90
pop$ -- 2421
preamble$ -- 1
purify$ -- 0
quote$ -- 2
skip$ -- 5462
stack$ -- 0
substring$ -- 3468
swap$ -- 3893
text.length$ -- 67
text.prefix$ -- 0
top$ -- 5
type$ -- 88
warning$ -- 5
while$ -- 297
width$ -- 90
write$ -- 790
(There were 50 error messages)
