% BibTeX references for the new methods added to the tables

@inproceedings{hoyer2022hrda,
  title={HRDA: Context-Aware High-Resolution Domain-Adaptive Semantic Segmentation},
  author={<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON>},
  booktitle={European Conference on Computer Vision (ECCV)},
  pages={372--391},
  year={2022},
  organization={Springer}
}

@inproceedings{hoyer2023mic,
  title={MIC: Masked Image Consistency for Context-Enhanced Domain Adaptation},
  author={<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON>},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages={11721--11732},
  year={2023}
}

@inproceedings{zhao2024seco,
  title={Connectivity-Driven Pseudo-Labeling Makes Stronger Cross-Domain Semantic Segmentation},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, Wang<PERSON>g},
  booktitle={Advances in Neural Information Processing Systems (NeurIPS)},
  volume={37},
  year={2024}
}

@inproceedings{benigmim2024clouds,
  title={Collaborating Foundation Models for Domain Generalized Semantic Segmentation},
  author={Benigmim, Yasser and Roy, Subhankar and Essid, Slim and Kalogeiton, Vicky and Lathuiliere, Stephane},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages={28257--28267},
  year={2024}
}

% Additional related references (optional)

@inproceedings{hoyer2022daformer,
  title={DAFormer: Improving Network Architectures and Training Strategies for Domain-Adaptive Semantic Segmentation},
  author={Hoyer, Lukas and Dai, Dengxin and Van Gool, Luc},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  pages={9924--9935},
  year={2022}
}

@inproceedings{kirillov2023sam,
  title={Segment Anything},
  author={Kirillov, Alexander and Mintun, Eric and Ravi, Nikhila and Mao, Hanzi and Rolland, Chloe and Gustafson, Laura and Xiao, Tete and Whitehead, Spencer and Berg, Alexander C and Lo, Wan-Yen and others},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)},
  pages={4015--4026},
  year={2023}
}

@inproceedings{radford2021clip,
  title={Learning Transferable Visual Representations with Natural Language Supervision},
  author={Radford, Alec and Kim, Jong Wook and Hallacy, Chris and Ramesh, Aditya and Goh, Gabriel and Agarwal, Sandhini and Sastry, Girish and Askell, Amanda and Mishkin, Pamela and Clark, Jack and others},
  booktitle={International Conference on Machine Learning (ICML)},
  pages={8748--8763},
  year={2021},
  organization={PMLR}
}
