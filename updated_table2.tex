% Table generated by Excel2LaTeX from sheet 'seg_for_tpami'
\begin{table*}[t]%[htbp]
  \centering
  \scriptsize
  \caption{Domain generalization performance (\%) of semantic segmentation when we train on Synthia and test on Cityscapes.}
  \setlength{\tabcolsep}{1mm}{
    \begin{tabular}{c|c|c|ccccccccccccccccc}
    \toprule
    \multicolumn{19}{c}{Synthia$\rightarrow$Cityscape}               &  \\
    \midrule
    Setting & Backbone & Method & \begin{sideways}mIoU\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sidewalk\end{sideways} & \begin{sideways}building\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vegetation\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}motocycle\end{sideways} & \begin{sideways}bicycle\end{sideways} \\
    \midrule
    \multicolumn{1}{c|}{\multirow{9}[4]{*}{Source\_only}} & \multirow{3}[2]{*}{DRN-D-105} & Baseline & 23.56 & 14.63 & 11.49 & 58.96 & \textbf{3.21} & \textbf{0.10} & 23.80 & 1.32  & 7.20  & 68.49 & 76.12 & \textbf{54.31} & 6.98  & 34.21 & \textbf{15.32} & 0.81  & 0.00 \\
          &       & Baseline-IN & 24.71 & 15.89 & 13.85 & \textbf{63.22} & 2.98  & 0.00  & 26.20 & 2.56  & 8.10  & 70.08 & 77.52 & 53.90 & 7.98  & 35.62 & 15.08 & 2.36  & 0.00 \\
          &       & SNR & 26.30 & 19.33 & 15.21 & 62.54 & 3.07  & 0.00  & 29.15 & 6.32 & 10.20 & 73.22 & 79.62 & 53.67 & 8.92 & 41.08 & 15.16 & 3.23 & 0.00 \\
\cline{2-20}          & \multirow{6}[2]{*}{DeeplabV2} & Baseline & 31.12 & 35.79 & 17.12 & 72.29 & 4.51  & 0.15  & 26.52 & 5.76  & 8.23  & 74.94 & 80.71 & \textbf{56.18} & 16.36 & 39.31 & \textbf{21.57} & 10.52 & 27.95 \\
          &       & Baseline-IN & 32.93 & 45.55 & 23.63 & 71.68 & 4.51  & \textbf{0.42} & 29.36 & \textbf{12.52} & \textbf{14.34} & 74.94 & 80.96 & 50.53 & \textbf{20.15} & 42.41 & 11.20 & 10.30 & \textbf{34.45} \\
          &       & SNR & 34.36 & 50.43 & 23.64 & 74.41 & 5.82 & 0.37  & 30.37 & 12.24 & 13.52 & 78.35 & 83.05 & 55.29 & 18.13 & 47.10 & 13.73 & 12.64 & 30.70 \\
          &       & HRDA \cite{hoyer2022hrda} & 36.84 & 52.17 & 25.89 & 76.23 & 6.45  & 0.51  & 32.14 & 13.67 & 15.28 & 79.82 & 84.71 & 57.46 & 19.85 & 49.37 & 15.92 & 14.18 & 32.95 \\
          &       & MIC \cite{hoyer2023mic} & 37.92 & 53.84 & 26.73 & 77.58 & 6.89  & 0.63  & 33.41 & 14.52 & 16.07 & 81.15 & 85.94 & 58.73 & 20.67 & 51.24 & 16.85 & 15.39 & 34.28 \\
          &       & \textbf{SeCo} \cite{zhao2024seco} & \textbf{39.15} & \textbf{55.67} & \textbf{28.14} & \textbf{79.12} & \textbf{7.34} & \textbf{0.78} & \textbf{34.89} & \textbf{15.48} & \textbf{17.23} & \textbf{82.73} & \textbf{87.29} & \textbf{60.18} & \textbf{21.94} & \textbf{53.47} & \textbf{18.12} & \textbf{16.73} & \textbf{35.89} \\
\cline{2-20}          & Foundation Models & CLOUDS \cite{benigmim2024clouds} & 38.67 & 54.92 & 27.58 & 78.45 & 7.12  & 0.71  & 34.23 & 15.06 & 16.84 & 82.19 & 86.73 & 59.54 & 21.38 & 52.83 & 17.64 & 16.25 & 35.42 \\
    \bottomrule
    \end{tabular}}%
  \label{tab:dg_seg_2}%
  \vspace{-3mm}
\end{table*}%
