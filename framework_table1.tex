\begin{table*}[t]%tbp]
  \centering
  \scriptsize
  \caption{Domain generalization performance (\%) for semantic segmentation when we train on GTA5 and test on Cityscapes. (*表示需要从原始论文获取真实数据)}
  \setlength{\tabcolsep}{0.7mm}{
    \begin{tabular}{c|c|c|cccccccccccccccccccc}
    \toprule
    \multicolumn{23}{c}{GTA5$\rightarrow$Cityscape} \\
    \midrule
    Setting & Backbone & Method & \begin{sideways}mIoU\end{sideways} & \begin{sideways}road\end{sideways} & \begin{sideways}sidewalk\end{sideways} & \begin{sideways}building\end{sideways} & \begin{sideways}wall\end{sideways} & \begin{sideways}fence\end{sideways} & \begin{sideways}pole\end{sideways} & \begin{sideways}light\end{sideways} & \begin{sideways}sign\end{sideways} & \begin{sideways}vegetation\end{sideways} & \begin{sideways}terrain\end{sideways} & \begin{sideways}sky\end{sideways} & \begin{sideways}person\end{sideways} & \begin{sideways}rider\end{sideways} & \begin{sideways}car\end{sideways} & \begin{sideways}truck\end{sideways} & \begin{sideways}bus\end{sideways} & \begin{sideways}train\end{sideways} & \begin{sideways}motocycle\end{sideways} & \begin{sideways}bicycle\end{sideways} \\
    \midrule
    \multicolumn{1}{c|}{\multirow{9}[4]{*}{Source\_only}} & \multirow{3}[2]{*}{DRN-D-105} & Baseline & 29.84 & 45.82 & 20.80 & 58.86 & 5.14  & \textbf{16.74} & \textbf{31.74} & \textbf{33.70} & \textbf{19.34} & 83.25 & 15.11 & 66.99 & 52.99 & 9.20  & 53.59 & 12.99 & 14.24 & 3.46  & 17.54 & 5.50 \\
          &       & Baseline-IN & 32.64 & 59.27 & 16.25 & 71.58 & 12.66 & 16.04 & 23.61 & 24.72 & 14.01 & \textbf{84.43} & 31.96 & 62.76 & 52.33 & \textbf{11.34} & 61.00 & \textbf{15.27} & \textbf{21.98} & \textbf{7.43} & 20.48 & 13.07 \\
          &       & SNR & 36.16 & 83.34 & 17.32 & 78.74 & 16.85 & 10.71 & 29.17 & 30.46 & 13.76 & 83.42 & 34.43 & 73.30 & 53.95 & 8.95  & 78.84 & 13.86 & 15.18 & 3.96  & 21.48 & 19.39 \\
\cline{2-23}          & \multirow{6}[2]{*}{DeeplabV2} & Baseline & 36.94 & 71.41 & 15.33 & 74.04 & 21.13 & 14.49 & 22.86 & 33.93 & 18.62 & 80.75 & 20.98 & 68.58 & 56.62 & \textbf{27.17} & 67.47 & 32.81 & 5.60  & \textbf{7.74} & 28.43 & 33.82 \\
          &       & Baseline-IN & 39.46 & 73.43 & 22.19 & 78.71 & 24.04 & 15.29 & 27.63 & 29.66 & 19.96 & 80.19 & 27.42 & 70.26 & 56.27 & 15.86 & 72.97 & \textbf{33.66} & 37.79 & 5.63  & 29.20 & 29.59 \\
          &       & SNR & 42.68 & 78.95 & 29.51 & 79.92 & 25.01 & 20.32 & 28.33 & 34.83 & 20.40 & 82.76 & 36.13 & 71.47 & 59.19 & 21.62 & 75.84 & 32.78 & 45.48 & 2.97  & 30.26 & 35.13 \\
          &       & SCSD \cite{niu2025scsd}* & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX \\
          &       & WeatherDG \cite{qian2024weatherdg}* & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX \\
          &       & CLOUDS \cite{benigmim2024clouds}* & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX & XX.XX \\
    \bottomrule
    \end{tabular}}%
  \label{tab:dg_seg}%
\end{table*}%

% 注意：标记为*的方法需要从原始论文中获取真实的实验数据
% XX.XX 表示需要填入真实数值
