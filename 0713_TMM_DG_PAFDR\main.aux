\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\citation{qian2018pose}
\citation{jin2021style}
\citation{jin2021style}
\citation{Wang2022DGSurvey}
\citation{Ganin2016DANN}
\citation{zhou2019omni}
\citation{ioffe2015batch}
\citation{choi2021meta,huang2017arbitrary,jin2021style,pan2018two,zhou2019osnet}
\citation{jin2021style,zheng2021calibrated}
\citation{zhou2016learning}
\citation{jin2021style}
\providecommand \oddpage@label [2]{}
\@writefile{toc}{\contentsline {section}{\numberline {I}Introduction}{1}{section.1}\protected@file@percent }
\newlabel{intro}{{I}{1}{Introduction}{section.1}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces  \textbf  {Observation and Motivation}. From left to right, input represents the input image, F represents the activation map of the output features from the backbone network (taking ResNet50 as an example), $\cc@accent {"707E}{\mathrm  {\textbf  {F}}}$ represents the activation map of the features after IN and BN processing, baseline represents the output feature activation map of existing decomposition-based DG method (taking SNR \cite  {jin2021style} as an example), and PAFDR represents the output feature activation map of the PAFDR proposed in this paper. }}{1}{figure.1}\protected@file@percent }
\newlabel{fig:feat_intro}{{1}{1}{\textbf {Observation and Motivation}. From left to right, input represents the input image, F represents the activation map of the output features from the backbone network (taking ResNet50 as an example), $\tilde {\mathrm {\textbf {F}}}$ represents the activation map of the features after IN and BN processing, baseline represents the output feature activation map of existing decomposition-based DG method (taking SNR \cite {jin2021style} as an example), and PAFDR represents the output feature activation map of the PAFDR proposed in this paper}{figure.1}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces  \textbf  {Overall flowchart.} ($\textrm  {I}$) ResNet-50 network with the integrated PAFDR module. ($\textrm  {II}$) PAFDR module: Initially, BN and IN are utilized to reduce domain gaps, followed by the recovery of Task-Relevant features (indicated by the red solid line) through parallel spatial and channel attention (shown as red squares in the figure). The green dashed line branch is solely for calculating the Suppression loss and is discarded during inference. Letters $a$ to $i$ in the feature map denote spatial attention weights for different regions, and gradient yellow indicates channel attention weights. ($\textrm  {III}$) ATFD Loss: (a) ATFD Loss ensures that features $\cc@accent {"707E}{F}^{+}$ after recovering Task-Relevant features $R^{+}$ are more discriminative than original features $F$, while features $\cc@accent {"707E}{F}^{-}$ after adding task-irrelevant features $R^{-}$ are less discriminative than normalized features $\cc@accent {"707E}{F}$; (b) ATFD Loss consists of asymmetric Enhancement loss and Suppression loss. }}{2}{figure.2}\protected@file@percent }
\newlabel{fig:intro}{{2}{2}{\textbf {Overall flowchart.} ($\textrm {I}$) ResNet-50 network with the integrated PAFDR module. ($\textrm {II}$) PAFDR module: Initially, BN and IN are utilized to reduce domain gaps, followed by the recovery of Task-Relevant features (indicated by the red solid line) through parallel spatial and channel attention (shown as red squares in the figure). The green dashed line branch is solely for calculating the Suppression loss and is discarded during inference. Letters $a$ to $i$ in the feature map denote spatial attention weights for different regions, and gradient yellow indicates channel attention weights. ($\textrm {III}$) ATFD Loss: (a) ATFD Loss ensures that features $\tilde {F}^{+}$ after recovering Task-Relevant features $R^{+}$ are more discriminative than original features $F$, while features $\tilde {F}^{-}$ after adding task-irrelevant features $R^{-}$ are less discriminative than normalized features $\tilde {F}$; (b) ATFD Loss consists of asymmetric Enhancement loss and Suppression loss}{figure.2}{}}
\citation{li2018mmdaae}
\citation{liu2020shape}
\citation{cha2021domain}
\citation{pan2018two}
\citation{huang2017arbitrary,pan2018two}
\citation{khodabandeh2019robust}
\citation{chen2018domain}
\citation{peng2019moment}
\citation{higgins2016beta}
\citation{wu2021stylespace}
\citation{jin2021style}
\citation{he2016deep}
\@writefile{toc}{\contentsline {section}{\numberline {II}Related Work}{3}{section.2}\protected@file@percent }
\newlabel{related}{{II}{3}{Related Work}{section.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-A}}Domain Generalization (DG)}{3}{subsection.2.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-B}}Unsupervised Domain Adaptation (UDA)}{3}{subsection.2.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-C}}Feature Decomposition}{3}{subsection.2.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {III}Method}{3}{section.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-A}}Parallel Attention-based Feature Decomposition and Recovery (PAFDR)}{4}{subsection.3.1}\protected@file@percent }
\newlabel{Hypergraph Construction}{{\mbox  {III-A}}{4}{Parallel Attention-based Feature Decomposition and Recovery (PAFDR)}{subsection.3.1}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-A}1}\textbf  {Domain Gaps Reduction via BN and IN.}}{4}{subsubsection.3.1.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-A}2}\textbf  {Feature Decomposition and Recovery via parallel spatial and channel attentions}}{4}{subsubsection.3.1.2}\protected@file@percent }
\citation{jin2021style,pan2018two}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-B}}Asymmetric Task-relevant Feature Decomposition Loss}{5}{subsection.3.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-C}}Applications}{5}{subsection.3.3}\protected@file@percent }
\citation{carlucci2019domain}
\citation{li2020domain}
\citation{zhou2020learning}
\citation{nam2021reducing}
\citation{zhou2021domain}
\citation{yao2022pcl}
\citation{jin2022style}
\citation{wang2022domain}
\citation{segu2023batch}
\citation{niu2023knowledge}
\citation{xu2024cbdmoe}
\citation{peng2019moment}
\citation{li2021t}
\citation{ren2022multi}
\citation{wang2022self}
\citation{wu2023domain}
\citation{wen2024training}
\citation{peng2019moment}
\citation{zhao2020multi}
\citation{wang2020learning}
\citation{xu2022graphical}
\citation{deng2022dynamic}
\citation{wang2022domain}
\citation{li2020domain}
\citation{carlucci2019domain}
\citation{nam2021reducing}
\citation{zhou2020learning}
\citation{zhou2021domain}
\citation{yao2022pcl}
\citation{jin2022style}
\citation{wang2022domain}
\citation{segu2023batch}
\citation{niu2023knowledge}
\citation{xu2024cbdmoe}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}1}\textbf  {classification}}{6}{subsubsection.3.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}2}\textbf  {Detection}}{6}{subsubsection.3.3.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}3}\textbf  { Segmentation}}{6}{subsubsection.3.3.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {IV}Experiments}{6}{section.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-A}}Object Classification}{6}{subsection.4.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}1}\textbf  {Datasets and Implementation Details}}{6}{subsubsection.4.1.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-B}}Comparison with Existing Approaches}{6}{subsection.4.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-B}1}\textbf  {Results on Domain Generalization}}{6}{subsubsection.4.2.1}\protected@file@percent }
\citation{peng2019moment}
\citation{wen2024training}
\citation{wang2022domain}
\@writefile{lot}{\contentsline {table}{\numberline {I}{\ignorespaces Comparison of domain generalization methods on PACS and Office-Home datasets.}}{7}{table.1}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {II}{\ignorespaces Comparison of UDA methods on DomainNet.}}{7}{table.2}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {III}{\ignorespaces Comparison of UDA methods on Digits-5.}}{7}{table.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-B}2}\textbf  {Results on Unsupervised Domain Adaptation}}{7}{subsubsection.4.2.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-C}}Ablation Study}{7}{subsection.4.3}\protected@file@percent }
\newlabel{sec:ablation}{{\mbox  {IV-C}}{7}{Ablation Study}{subsection.4.3}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-C}1}\textbf  {Ablation Study on Spatial Attention and Channel Attention}}{7}{subsubsection.4.3.1}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {IV}{\ignorespaces Ablation study on the channel and spatial attention.}}{8}{table.4}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {V}{\ignorespaces Ablation study on the asymmetric task-relevant feature decomposition loss.}}{8}{table.5}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-C}2}\textbf  {Effect of the asymmetric task-relevant feature decomposition loss $\mathcal  {L}_{ATFD}$}}{8}{subsubsection.4.3.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-C}3}\textbf  {Optimal Integration Strategy for PAFDR Module.}}{8}{subsubsection.4.3.3}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {VI}{\ignorespaces Ablation study on which stage to add PAFDR.}}{8}{table.6}\protected@file@percent }
\newlabel{tab:ablation_snr}{{VI}{8}{Ablation study on which stage to add PAFDR}{table.6}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-D}}Visualization}{8}{subsection.4.4}\protected@file@percent }
\newlabel{sec:vis}{{\mbox  {IV-D}}{8}{Visualization}{subsection.4.4}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-D}1}\textbf  {Visualization of Feature Map}}{8}{subsubsection.4.4.1}\protected@file@percent }
\bibstyle{IEEEtran}
\bibdata{arxiv}
\bibcite{qian2018pose}{1}
\bibcite{jin2021style}{2}
\bibcite{Wang2022DGSurvey}{3}
\bibcite{Ganin2016DANN}{4}
\bibcite{zhou2019omni}{5}
\bibcite{ioffe2015batch}{6}
\bibcite{choi2021meta}{7}
\bibcite{huang2017arbitrary}{8}
\bibcite{pan2018two}{9}
\bibcite{zhou2019osnet}{10}
\bibcite{zheng2021calibrated}{11}
\bibcite{zhou2016learning}{12}
\bibcite{li2018mmdaae}{13}
\bibcite{liu2020shape}{14}
\bibcite{cha2021domain}{15}
\bibcite{khodabandeh2019robust}{16}
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces  ($\textrm  {I}$) The activation maps of different features within a PAFDR module (PAFDR 3) show that PAFDR can well separate task-relevant/irrelevant features. The enhanced feature $\cc@accent {"707E}{F}^{+}$ has better discriminability than the original feature $F$, while the contaminated feature $\cc@accent {"707E}{F}^{-}$ has worse discriminability than the normalized feature $\cc@accent {"707E}{F}$. ($\textrm  {II}$) Activation maps of our proposed method (bottom) and the baseline (top) correspond to changes in image backgrounds and properties (taking color changes as an example). Our maps are more robust to changes in image background and properties. }}{9}{figure.3}\protected@file@percent }
\newlabel{fig:vis_feat}{{3}{9}{($\textrm {I}$) The activation maps of different features within a PAFDR module (PAFDR 3) show that PAFDR can well separate task-relevant/irrelevant features. The enhanced feature $\tilde {F}^{+}$ has better discriminability than the original feature $F$, while the contaminated feature $\tilde {F}^{-}$ has worse discriminability than the normalized feature $\tilde {F}$. ($\textrm {II}$) Activation maps of our proposed method (bottom) and the baseline (top) correspond to changes in image backgrounds and properties (taking color changes as an example). Our maps are more robust to changes in image background and properties}{figure.3}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces  T-SNE visualization of feature distributions on the digit-five dataset for unsupervised domain adaptation (UDA) classification tasks. The figure presents a comparison between our DAFDR method and the baseline approach (SNR).}}{9}{figure.4}\protected@file@percent }
\newlabel{fig:onecol}{{4}{9}{T-SNE visualization of feature distributions on the digit-five dataset for unsupervised domain adaptation (UDA) classification tasks. The figure presents a comparison between our DAFDR method and the baseline approach (SNR)}{figure.4}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-D}2}\textbf  {Visualization of Feature distribution}}{9}{subsubsection.4.4.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {V}Conclusion}{9}{section.5}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{References}{9}{section*.1}\protected@file@percent }
\bibcite{chen2018domain}{17}
\bibcite{peng2019moment}{18}
\bibcite{higgins2016beta}{19}
\bibcite{wu2021stylespace}{20}
\bibcite{he2016deep}{21}
\bibcite{carlucci2019domain}{22}
\bibcite{li2020domain}{23}
\bibcite{zhou2020learning}{24}
\bibcite{nam2021reducing}{25}
\bibcite{zhou2021domain}{26}
\bibcite{yao2022pcl}{27}
\bibcite{jin2022style}{28}
\bibcite{wang2022domain}{29}
\bibcite{segu2023batch}{30}
\bibcite{niu2023knowledge}{31}
\bibcite{xu2024cbdmoe}{32}
\bibcite{li2021t}{33}
\bibcite{ren2022multi}{34}
\bibcite{wang2022self}{35}
\bibcite{wu2023domain}{36}
\bibcite{wen2024training}{37}
\bibcite{zhao2020multi}{38}
\bibcite{wang2020learning}{39}
\bibcite{xu2022graphical}{40}
\bibcite{deng2022dynamic}{41}
\gdef \@abspage@last{10}
