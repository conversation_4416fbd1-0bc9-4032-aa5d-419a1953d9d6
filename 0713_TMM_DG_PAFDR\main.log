This is pdfTeX, Version 3.141592653-2.6-1.40.27 (MiKTeX 25.2) (preloaded format=pdflatex 2025.2.25)  13 JUL 2025 21:21
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**c:/Users/<USER>/Desktop/0713_TMM_DG_PAFDR/main.tex
(c:/Users/<USER>/Desktop/0713_TMM_DG_PAFDR/main.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(C:\Program Files\MiKTeX\tex/latex/ieeetran\IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen141
\@IEEEtrantmpdimenB=\dimen142
\@IEEEtrantmpdimenC=\dimen143
\@IEEEtrantmpcountA=\count196
\@IEEEtrantmpcountB=\count197
\@IEEEtrantmpcountC=\count198
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 503.
(C:\Program Files\MiKTeX\tex/latex/psnfss\ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
-- Using 8.5in x 11in (letter) paper.
-- Using PDF output.
\@IEEEnormalsizeunitybaselineskip=\dimen144
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen145
\CLASSINFOnormalsizeunitybaselineskip=\dimen146
\IEEEnormaljot=\dimen147
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <5> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <7> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <8> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <9> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <10> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <11> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <11> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <12> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <17> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <17> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <20> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <20> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <24> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <24> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
\IEEEquantizedlength=\dimen148
\IEEEquantizedlengthdiff=\dimen149
\IEEEquantizedtextheightdiff=\dimen150
\IEEEilabelindentA=\dimen151
\IEEEilabelindentB=\dimen152
\IEEEilabelindent=\dimen153
\IEEEelabelindent=\dimen154
\IEEEdlabelindent=\dimen155
\IEEElabelindent=\dimen156
\IEEEiednormlabelsep=\dimen157
\IEEEiedmathlabelsep=\dimen158
\IEEEiedtopsep=\skip49
\c@section=\count199
\c@subsection=\count266
\c@subsubsection=\count267
\c@paragraph=\count268
\c@IEEEsubequation=\count269
\abovecaptionskip=\skip50
\belowcaptionskip=\skip51
\c@figure=\count270
\c@table=\count271
\@IEEEeqnnumcols=\count272
\@IEEEeqncolcnt=\count273
\@IEEEsubeqnnumrollback=\count274
\@IEEEquantizeheightA=\dimen159
\@IEEEquantizeheightB=\dimen160
\@IEEEquantizeheightC=\dimen161
\@IEEEquantizeprevdepth=\dimen162
\@IEEEquantizemultiple=\count275
\@IEEEquantizeboxA=\box52
\@IEEEtmpitemindent=\dimen163
\IEEEPARstartletwidth=\dimen164
\c@IEEEbiography=\count276
\@IEEEtranrubishbin=\box53
) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip52

For additional information on amsmath, use the `?' option.
(C:\Program Files\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (C:\Program Files\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen165
)) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen166
) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count277
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count278
\leftroot@=\count279
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count280
\DOTSCASE@=\count281
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box54
\strutbox@=\box55
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen167
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count282
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count283
\dotsspace@=\muskip17
\c@parentequation=\count284
\dspbrk@lvl=\count285
\tag@help=\toks19
\row@=\count286
\column@=\count287
\maxfields@=\count288
\andhelp@=\toks20
\eqnshift@=\dimen168
\alignsep@=\dimen169
\tagshift@=\dimen170
\tagwidth@=\dimen171
\totwidth@=\dimen172
\lineht@=\dimen173
\@envbody=\toks21
\multlinegap=\skip53
\multlinetaggap=\skip54
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (C:\Program Files\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (C:\Program Files\MiKTeX\tex/latex/algorithms\algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (C:\Program Files\MiKTeX\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count289
\float@exts=\toks23
\float@box=\box56
\@float@everytoks=\toks24
\@floatcapt=\box57
) (C:\Program Files\MiKTeX\tex/latex/base\ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks25
\c@algorithm=\count290
) (C:\Program Files\MiKTeX\tex/latex/tools\array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen174
\ar@mcellbox=\box58
\extrarowheight=\dimen175
\NC@list=\toks26
\extratabsurround=\skip55
\backup@length=\skip56
\ar@cellbox=\box59
) (C:\Program Files\MiKTeX\tex/latex/subfig\subfig.sty
Package: subfig 2005/06/28 ver: 1.3 subfig package
 (C:\Program Files\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks27
) (C:\Program Files\MiKTeX\tex/latex/caption\caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen176
\captionmargin=\dimen177
\caption@leftmargin=\dimen178
\caption@rightmargin=\dimen179
\caption@width=\dimen180
\caption@indent=\dimen181
\caption@parindent=\dimen182
\caption@hangindent=\dimen183
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\ifx \@captype \@IEEEtablestring \footnotesize \bgroup \par \centering \@IEEEtabletopskipstrut {\normalfont \footnotesize #1}\\{\normalfont \footnotesize \scshape #2}\par \addvspace {0.5\baselineskip }\egroup \@IEEEtablecaptionsepspace \else \@IEEEfigurecaptionsepspace \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace #2}\ifdim \wd \@tempboxa >\hsize \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace }\parbox [t]{\hsize }{\normalfont \footnotesize \noindent \unhbox \@tempboxa #2}\else \ifCLASSOPTIONconference \hbox to\hsize {\normalfont \footnotesize \hfil \box \@tempboxa \hfil }\else \hbox to\hsize {\normalfont \footnotesize \box \@tempboxa \hfil }\fi \fi \fi  on input line 1175.
)
\c@KVtest=\count291
\sf@farskip=\skip57
\sf@captopadj=\dimen184
\sf@capskip=\skip58
\sf@nearskip=\skip59
\c@subfigure=\count292
\c@subfigure@save=\count293
\c@lofdepth=\count294
\c@subtable=\count295
\c@subtable@save=\count296
\c@lotdepth=\count297
\sf@top=\skip60
\sf@bottom=\skip61
) (C:\Program Files\MiKTeX\tex/latex/base\textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
) (C:\Program Files\MiKTeX\tex/latex/sttools\stfloats.sty
Package: stfloats 2025/06/18 v3.4 Improve float mechanism and baselineskip settings
\@dblbotnum=\count298
\c@dblbotnumber=\count299
) (C:\Program Files\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (C:\Program Files\MiKTeX\tex/latex/tools\verbatim.sty
Package: verbatim 2024-01-22 v1.5x LaTeX2e package for verbatim enhancements
\every@verbatim=\toks28
\verbatim@line=\toks29
\verbatim@in@stream=\read2
) (C:\Program Files\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (C:\Program Files\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (C:\Program Files\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (C:\Program Files\MiKTeX\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (C:\Program Files\MiKTeX\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen185
\Gin@req@width=\dimen186
) (C:\Program Files\MiKTeX\tex/latex/cite\cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (C:\Program Files\MiKTeX\tex/latex/multirow\multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip62
\multirow@cntb=\count300
\multirow@dima=\skip63
\bigstrutjot=\dimen187
) (C:\Program Files\MiKTeX\tex/latex/graphics\color.sty
Package: color 2024/06/23 v1.3e Standard LaTeX Color (DPC)
 (C:\Program Files\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.
 (C:\Program Files\MiKTeX\tex/latex/graphics\mathcolor.ltx)) (C:\Program Files\MiKTeX\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen188
\lightrulewidth=\dimen189
\cmidrulewidth=\dimen190
\belowrulesep=\dimen191
\belowbottomsep=\dimen192
\aboverulesep=\dimen193
\abovetopsep=\dimen194
\cmidrulesep=\dimen195
\cmidrulekern=\dimen196
\defaultaddspace=\dimen197
\@cmidla=\count301
\@cmidlb=\count302
\@aboverulesep=\dimen198
\@belowrulesep=\dimen199
\@thisruleclass=\count303
\@lastruleclass=\count304
\@thisrulewidth=\dimen256
) (C:\Program Files\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (C:\Program Files\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
LaTeX Info: Redefining \color on input line 762.
 (C:\Program Files\MiKTeX\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (C:\Program Files\MiKTeX\tex/latex/colortbl\colortbl.sty
Package: colortbl 2024/07/06 v1.0i Color table columns (DPC)
\everycr=\toks30
\minrowclearance=\skip64
\rownum=\count305
) (C:\Program Files\MiKTeX\tex/latex/amscls\amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks31
\thm@bodyfont=\toks32
\thm@headfont=\toks33
\thm@notefont=\toks34
\thm@headpunct=\toks35
\thm@preskip=\skip65
\thm@postskip=\skip66
\thm@headsep=\skip67
\dth@everypar=\toks36
) (C:\Program Files\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (C:\Program Files\MiKTeX\tex/latex/jknappen\mathrsfs.sty
Package: mathrsfs 1996/01/01 Math RSFS package v1.0 (jk)
\symrsfs=\mathgroup6
) (C:\Program Files\MiKTeX\tex/latex/adjustbox\adjustbox.sty
Package: adjustbox 2025/02/26 v1.3c Adjusting TeX boxes (trim, clip, ...)
 (C:\Program Files\MiKTeX\tex/latex/xkeyval\xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (C:\Program Files\MiKTeX\tex/generic/xkeyval\xkeyval.tex (C:\Program Files\MiKTeX\tex/generic/xkeyval\xkvutils.tex
\XKV@toks=\toks37
\XKV@tempa@toks=\toks38
)
\XKV@depth=\count306
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (C:\Program Files\MiKTeX\tex/latex/adjustbox\adjcalc.sty
Package: adjcalc 2012/05/16 v1.1 Provides advanced setlength with multiple back-ends (calc, etex, pgfmath)
) (C:\Program Files\MiKTeX\tex/latex/adjustbox\trimclip.sty
Package: trimclip 2025/02/21 v1.2a Trim and clip general TeX material
 (C:\Program Files\MiKTeX\tex/latex/collectbox\collectbox.sty
Package: collectbox 2022/10/17 v0.4c Collect macro arguments as boxes
\collectedbox=\box60
)
\tc@llx=\dimen257
\tc@lly=\dimen258
\tc@urx=\dimen259
\tc@ury=\dimen260
Package trimclip Info: Using driver 'tc-pdftex.def'.
 (C:\Program Files\MiKTeX\tex/latex/adjustbox\tc-pdftex.def
File: tc-pdftex.def 2025/02/26 v2.3 Clipping driver for pdftex
))
\adjbox@Width=\dimen261
\adjbox@Height=\dimen262
\adjbox@Depth=\dimen263
\adjbox@Totalheight=\dimen264
\adjbox@pwidth=\dimen265
\adjbox@pheight=\dimen266
\adjbox@pdepth=\dimen267
\adjbox@ptotalheight=\dimen268
 (C:\Program Files\MiKTeX\tex/latex/ifoddpage\ifoddpage.sty
Package: ifoddpage 2022/10/18 v1.2 Conditionals for odd/even page detection
\c@checkoddpage=\count307
) (C:\Program Files\MiKTeX\tex/latex/varwidth\varwidth.sty
Package: varwidth 2009/03/30 ver 0.92;  Variable-width minipages
\@vwid@box=\box61
\sift@deathcycles=\count308
\@vwid@loff=\dimen269
\@vwid@roff=\dimen270
)) (C:\Program Files\MiKTeX\tex/latex/psnfss\pifont.sty
Package: pifont 2020/03/25 PSNFSS-v9.3 Pi font support (SPQR) 
LaTeX Font Info:    Trying to load font information for U+pzd on input line 63.
 (C:\Program Files\MiKTeX\tex/latex/psnfss\upzd.fd
File: upzd.fd 2001/06/04 font definitions for U/pzd.
)
LaTeX Font Info:    Trying to load font information for U+psy on input line 64.
 (C:\Program Files\MiKTeX\tex/latex/psnfss\upsy.fd
File: upsy.fd 2001/06/04 font definitions for U/psy.
)) (C:\Program Files\MiKTeX\tex/latex/makecell\makecell.sty
Package: makecell 2009/08/03 V0.1e Managing of Tab Column Heads and Cells
\rotheadsize=\dimen271
\c@nlinenum=\count309
\TeXr@lab=\toks39
) (C:\Program Files\MiKTeX\tex/latex/graphics\rotating.sty
Package: rotating 2016/08/11 v2.16d rotated objects in LaTeX
\c@r@tfl@t=\count310
\rotFPtop=\skip68
\rotFPbot=\skip69
\rot@float@box=\box62
\rot@mess@toks=\toks40
) (C:\Program Files\MiKTeX\tex/latex/algorithmicx\algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count311
\c@ALG@rem=\count312
\c@ALG@nested=\count313
\ALG@tlm=\skip70
\ALG@thistlm=\skip71
\c@ALG@Lnr=\count314
\c@ALG@blocknr=\count315
\c@ALG@storecount=\count316
\c@ALG@tmpcounter=\count317
\ALG@tmplength=\skip72
) (C:\Program Files\MiKTeX\tex/latex/algorithmicx\algpseudocode.sty
Package: algpseudocode 

Document Style - pseudocode environments for use with the `algorithmicx' style
) (C:\Program Files\MiKTeX\tex/latex/accents\accents.sty
Package: accents 2006/05/12 v1.4 Math Accent Tools
\cc@skew=\dimen272
\cc@wd=\dimen273
\cc@code=\count318
\cc@group=\count319
\cc@skewchar=\count320
) (C:\Program Files\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX
 (C:\Program Files\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
) (C:\Program Files\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (C:\Program Files\MiKTeX\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (C:\Program Files\MiKTeX\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (C:\Program Files\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (C:\Program Files\MiKTeX\tex/generic/pdftexcmds\pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (C:\Program Files\MiKTeX\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (C:\Program Files\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (C:\Program Files\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (C:\Program Files\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (C:\Program Files\MiKTeX\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (C:\Program Files\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count321
) (C:\Program Files\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count322
) (C:\Program Files\MiKTeX\tex/generic/stringenc\stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen274
\Hy@linkcounter=\count323
\Hy@pagecounter=\count324
 (C:\Program Files\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (C:\Program Files\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count325
 (C:\Program Files\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count326
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen275
 (C:\Program Files\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (C:\Program Files\MiKTeX\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count327
\Field@Width=\dimen276
\Fld@charsize=\dimen277
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring ON on input line 6060.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
 (C:\Program Files\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count328
\c@Item=\count329
\c@Hfootnote=\count330
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (C:\Program Files\MiKTeX\tex/latex/hyperref\hpdftex.def
File: hpdftex.def 2024-11-05 v7.01l Hyperref driver for pdfTeX
 (C:\Program Files\MiKTeX\tex/latex/base\atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count331
\c@bookmark@seq@number=\count332
 (C:\Program Files\MiKTeX\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (C:\Program Files\MiKTeX\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip73
) (C:\Program Files\MiKTeX\tex/latex/l3backend\l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count333
\l__pdf_internal_box=\box63
) (main.aux)
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 89.
LaTeX Font Info:    ... okay on input line 89.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 89.
LaTeX Font Info:    ... okay on input line 89.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 89.
LaTeX Font Info:    ... okay on input line 89.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 89.
LaTeX Font Info:    ... okay on input line 89.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 89.
LaTeX Font Info:    ... okay on input line 89.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 89.
LaTeX Font Info:    ... okay on input line 89.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 89.
LaTeX Font Info:    ... okay on input line 89.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 89.
LaTeX Font Info:    ... okay on input line 89.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 89.
LaTeX Font Info:    ... okay on input line 89.

-- Lines per column: 58 (exact).
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: subfig package v1.3 is loaded.
Package caption Info: End \AtBeginDocument code.
(C:\Program Files\MiKTeX\tex/context/base/mkii\supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count334
\scratchdimen=\dimen278
\scratchbox=\box64
\nofMPsegments=\count335
\nofMParguments=\count336
\everyMPshowfont=\toks41
\MPscratchCnt=\count337
\MPscratchDim=\dimen279
\MPnumerator=\count338
\makeMPintoPDFobject=\count339
\everyMPtoPDFconversion=\toks42
) (C:\Program Files\MiKTeX\tex/latex/epstopdf-pkg\epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (C:\Program Files\MiKTeX\tex/latex/00miktex\epstopdf-sys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package hyperref Info: Link coloring ON on input line 89.
\@outlinefile=\write3
\openout3 = `main.out'.

LaTeX Font Info:    Calculating math sizes for size <11> on input line 113.
LaTeX Font Info:    Trying to load font information for U+msa on input line 113.
 (C:\Program Files\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 113.
 (C:\Program Files\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for U+rsfs on input line 113.
 (C:\Program Files\MiKTeX\tex/latex/jknappen\ursfs.fd
File: ursfs.fd 1998/03/24 rsfs font definition file (jk)
)

LaTeX Warning: Citation `qian2018pose' on page 1 undefined on input line 158.



pdfTeX warning: pdflatex (file ./imgs/motivation-0625.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<imgs/motivation-0625.pdf, id=3, 928.21782pt x 582.75719pt>
File: imgs/motivation-0625.pdf Graphic file (type pdf)
<use imgs/motivation-0625.pdf>
Package pdftex.def Info: imgs/motivation-0625.pdf  used on input line 164.
(pdftex.def)             Requested size: 252.0pt x 158.20908pt.

LaTeX Warning: Citation `jin2021style' on page 1 undefined on input line 170.


LaTeX Warning: Citation `jin2021style' on page 1 undefined on input line 170.


LaTeX Warning: Citation `Wang2022DGSurvey' on page 1 undefined on input line 175.


LaTeX Warning: Citation `Ganin2016DANN' on page 1 undefined on input line 175.


Underfull \hbox (badness 1509) in paragraph at lines 175--176
\OT1/ptm/m/n/10 and Un-su-per-vised Do-main Adap-ta-tion (UDA). Both ap-
 []




LaTeX Warning: Citation `zhou2019omni' on page 1 undefined on input line 181.


LaTeX Warning: Citation `ioffe2015batch' on page 1 undefined on input line 181.


LaTeX Warning: Citation `choi2021meta' on page 1 undefined on input line 182.


LaTeX Warning: Citation `huang2017arbitrary' on page 1 undefined on input line 182.


LaTeX Warning: Citation `jin2021style' on page 1 undefined on input line 182.


LaTeX Warning: Citation `pan2018two' on page 1 undefined on input line 182.


LaTeX Warning: Citation `zhou2019osnet' on page 1 undefined on input line 182.


LaTeX Warning: Citation `jin2021style' on page 1 undefined on input line 190.


LaTeX Warning: Citation `zheng2021calibrated' on page 1 undefined on input line 190.


LaTeX Warning: Citation `zhou2016learning' on page 1 undefined on input line 191.


LaTeX Warning: Citation `jin2021style' on page 1 undefined on input line 192.


Underfull \vbox (badness 1708) has occurred while \output is active []



[1{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}{C:/Program Files/MiKTeX/fonts/enc/dvips/base/8r.enc}



 <./imgs/motivation-0625.pdf>]

pdfTeX warning: pdflatex (file ./imgs/overall_0614.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<imgs/overall_0614.pdf, id=57, 519.44063pt x 295.17075pt>
File: imgs/overall_0614.pdf Graphic file (type pdf)
<use imgs/overall_0614.pdf>
Package pdftex.def Info: imgs/overall_0614.pdf  used on input line 203.
(pdftex.def)             Requested size: 505.6778pt x 287.34666pt.

Underfull \vbox (badness 10000) has occurred while \output is active []




Underfull \vbox (badness 10000) has occurred while \output is active []



[2 <./imgs/overall_0614.pdf>]
Underfull \hbox (badness 1635) in paragraph at lines 229--230
[]\OT1/ptm/m/n/10 We in-no-va-tively re-veal and ad-dress the crit-i-cal is-
 []


LaTeX Warning: Citation `li2018mmdaae' on page 3 undefined on input line 307.


LaTeX Warning: Citation `liu2020shape' on page 3 undefined on input line 307.


LaTeX Warning: Citation `cha2021domain' on page 3 undefined on input line 307.


LaTeX Warning: Citation `pan2018two' on page 3 undefined on input line 307.


LaTeX Warning: Citation `huang2017arbitrary' on page 3 undefined on input line 307.


LaTeX Warning: Citation `pan2018two' on page 3 undefined on input line 307.




LaTeX Warning: Citation `khodabandeh2019robust' on page 3 undefined on input line 371.


LaTeX Warning: Citation `chen2018domain' on page 3 undefined on input line 373.


LaTeX Warning: Citation `peng2019moment' on page 3 undefined on input line 377.


LaTeX Warning: Citation `higgins2016beta' on page 3 undefined on input line 425.


LaTeX Warning: Citation `wu2021stylespace' on page 3 undefined on input line 427.


LaTeX Warning: Citation `jin2021style' on page 3 undefined on input line 430.


LaTeX Warning: Citation `he2016deep' on page 3 undefined on input line 479.



[3]



[4]

LaTeX Warning: Citation `jin2021style' on page 5 undefined on input line 709.


LaTeX Warning: Citation `pan2018two' on page 5 undefined on input line 709.





[5]


LaTeX Warning: Citation `carlucci2019domain' on page 6 undefined on input line 990.


LaTeX Warning: Citation `li2020domain' on page 6 undefined on input line 992.


LaTeX Warning: Citation `zhou2020learning' on page 6 undefined on input line 993.


LaTeX Warning: Citation `nam2021reducing' on page 6 undefined on input line 994.


LaTeX Warning: Citation `zhou2021domain' on page 6 undefined on input line 995.


LaTeX Warning: Citation `yao2022pcl' on page 6 undefined on input line 997.


LaTeX Warning: Citation `jin2022style' on page 6 undefined on input line 998.


LaTeX Warning: Citation `wang2022domain' on page 6 undefined on input line 999.


LaTeX Warning: Citation `segu2023batch' on page 6 undefined on input line 1003.


LaTeX Warning: Citation `niu2023knowledge' on page 6 undefined on input line 1004.


LaTeX Warning: Citation `xu2024cbdmoe' on page 6 undefined on input line 1005.


LaTeX Warning: Citation `peng2019moment' on page 6 undefined on input line 1020.


LaTeX Warning: Citation `li2021t' on page 6 undefined on input line 1021.


LaTeX Warning: Citation `ren2022multi' on page 6 undefined on input line 1022.


LaTeX Warning: Citation `wang2022self' on page 6 undefined on input line 1023.


LaTeX Warning: Citation `wu2023domain' on page 6 undefined on input line 1024.


LaTeX Warning: Citation `wen2024training' on page 6 undefined on input line 1025.


LaTeX Warning: Citation `peng2019moment' on page 6 undefined on input line 1037.


LaTeX Warning: Citation `zhao2020multi' on page 6 undefined on input line 1038.


LaTeX Warning: Citation `wang2020learning' on page 6 undefined on input line 1040.


LaTeX Warning: Citation `xu2022graphical' on page 6 undefined on input line 1041.


LaTeX Warning: Citation `deng2022dynamic' on page 6 undefined on input line 1044.


LaTeX Warning: Citation `wang2022domain' on page 6 undefined on input line 1045.


LaTeX Warning: Citation `li2020domain' on page 6 undefined on input line 1097.


LaTeX Warning: Citation `carlucci2019domain' on page 6 undefined on input line 1097.


LaTeX Warning: Citation `nam2021reducing' on page 6 undefined on input line 1097.


LaTeX Warning: Citation `zhou2020learning' on page 6 undefined on input line 1097.


LaTeX Warning: Citation `zhou2021domain' on page 6 undefined on input line 1097.


LaTeX Warning: Citation `yao2022pcl' on page 6 undefined on input line 1097.


LaTeX Warning: Citation `jin2022style' on page 6 undefined on input line 1097.


LaTeX Warning: Citation `wang2022domain' on page 6 undefined on input line 1097.


LaTeX Warning: Citation `segu2023batch' on page 6 undefined on input line 1097.


LaTeX Warning: Citation `niu2023knowledge' on page 6 undefined on input line 1097.


LaTeX Warning: Citation `xu2024cbdmoe' on page 6 undefined on input line 1097.



[6]

LaTeX Warning: Citation `peng2019moment' on page 7 undefined on input line 1163.


LaTeX Warning: Citation `wen2024training' on page 7 undefined on input line 1164.


LaTeX Warning: Citation `wang2022domain' on page 7 undefined on input line 1164.





[7]

Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 1351.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `subscript' on input line 1351.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 1351.



pdfTeX warning: pdflatex (file ./imgs/vis_activation_0625.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<imgs/vis_activation_0625.pdf, id=186, 2097.27827pt x 450.9347pt>
File: imgs/vis_activation_0625.pdf Graphic file (type pdf)
<use imgs/vis_activation_0625.pdf>
Package pdftex.def Info: imgs/vis_activation_0625.pdf  used on input line 1395.
(pdftex.def)             Requested size: 516.0pt x 110.94441pt.


pdfTeX warning: pdflatex (file ./imgs/t-SNE_Digit-Five-0626.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<imgs/t-SNE_Digit-Five-0626.pdf, id=187, 1484.69475pt x 619.51347pt>
File: imgs/t-SNE_Digit-Five-0626.pdf Graphic file (type pdf)
<use imgs/t-SNE_Digit-Five-0626.pdf>
Package pdftex.def Info: imgs/t-SNE_Digit-Five-0626.pdf  used on input line 1407.
(pdftex.def)             Requested size: 252.0pt x 105.14574pt.




[8]

(main.bbl

[9 <./imgs/vis_activation_0625.pdf> <./imgs/t-SNE_Digit-Five-0626.pdf>]

)

[10] (main.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********


LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.


Package rerunfilecheck Warning: File `main.out' has changed.
(rerunfilecheck)                Rerun to get outlines right
(rerunfilecheck)                or use package `bookmark'.

Package rerunfilecheck Info: Checksums for `main.out':
(rerunfilecheck)             Before: <no file>
(rerunfilecheck)             After:  FB7E90673C23B1B00F826E76FDE595F3;6230.
 ) 
Here is how much of TeX's memory you used:
 14982 strings out of 473523
 238886 string characters out of 5724706
 691540 words of memory out of 5000000
 37836 multiletter control sequences out of 15000+600000
 611736 words of font info for 147 fonts, out of 8000000 for 9000
 1145 hyphenation exceptions out of 8191
 75i,18n,79p,1327b,697s stack positions out of 10000i,1000n,20000p,200000b,200000s
<C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmbx10.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmex10.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi10.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi6.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi7.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi8.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmr10.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmr6.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmr7.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmr8.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmsy10.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmsy6.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmsy7.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmsy8.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/cm/cmti10.pfb><C:/Program Files/MiKTeX/fonts/type1/public/amsfonts/symbols/msbm10.pfb><C:/Program Files/MiKTeX/fonts/type1/urw/times/utmb8a.pfb><C:/Program Files/MiKTeX/fonts/type1/urw/times/utmbi8a.pfb><C:/Program Files/MiKTeX/fonts/type1/urw/times/utmr8a.pfb><C:/Program Files/MiKTeX/fonts/type1/urw/times/utmri8a.pfb>
Output written on main.pdf (10 pages, 1542338 bytes).
PDF statistics:
 434 PDF objects out of 1000 (max. 8388607)
 104 named destinations out of 1000 (max. 500000)
 21 words of extra memory for PDF output out of 10000 (max. 10000000)

