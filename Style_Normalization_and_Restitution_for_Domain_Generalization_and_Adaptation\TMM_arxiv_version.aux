\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\citation{kriz<PERSON>vsky2012imagenet,long2016unsupervised,ma2019deep}
\citation{muandet2013domain,li2017deeper,shankar2018generalizing,li2018learning,carlucci2019domain,li2019episodic}
\citation{pan2009survey,ganin2014unsupervised,long2015learning,long2016unsupervised,saito2018maximum,hoffman2018cycada,peng2019moment,xu2019larger,wang2019transferable}
\citation{muandet2013domain,li2017deeper,shankar2018generalizing,li2018learning,carlucci2019domain,li2019episodic}
\citation{sun2016return,sun2016deep,peng2018synthetic}
\citation{zellinger2017central,peng2019moment}
\citation{tzeng2014deep,long2017deep}
\citation{ganin2014unsupervised,ganin2016domain,tzeng2017adversarial}
\citation{liu2019transferabletat}
\citation{muandet2013domain,ghifary2016scatter,motiian2017unified,shankar2018generalizing,jia2019frustratingly,song2019generalizable}
\citation{muandet2013domain,motiian2017unified}
\citation{li2018learning}
\citation{li2019episodic}
\citation{zhou2020domain}
\citation{jia2019frustratingly}
\citation{zhou2019omni}
\citation{huang2017arbitrary,pan2018two}
\@writefile{toc}{\contentsline {section}{\numberline {I}Introduction}{1}{section.1}\protected@file@percent }
\newlabel{sec:introduction}{{I}{1}{Introduction}{section.1}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces Due to the differences in environments (such as lighting/camera/place/weather), the captured images present style discrepancy, such as the illumination, color contrast/saturation, quality, imaging style. These result in domain gaps between the training and testing data.}}{1}{figure.caption.1}\protected@file@percent }
\providecommand*\caption@xref[2]{\@setref\relax\@undefined{#1}}
\newlabel{fig:examples}{{1}{1}{Due to the differences in environments (such as lighting/camera/place/weather), the captured images present style discrepancy, such as the illumination, color contrast/saturation, quality, imaging style. These result in domain gaps between the training and testing data}{figure.caption.1}{}}
\citation{ulyanov2017improved,dumoulin2016learned,huang2017arbitrary}
\citation{jin2020style}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces Overall flowchart. (a) Our generalizable feature learning network with the proposed Style Normalization and Restitution (SNR) module being plugged in after some convolutional blocks. Here, we use ResNet-50 as our backbone for illustration. (b) Proposed SNR module. Instance Normalization (IN) is used to eliminate some style discrepancies followed by task-relevant feature restitution (marked by red solid arrows). Note the branch with dashed green line is only used for enforcing loss constraint and is discarded in inference. (c) Dual restitution loss constraint encourages the disentanglement of a residual feature $R$ to task-relevant one ($R^+$) and task-irrelevant one ($R^-$), which decreases and enhances, respectively, the entropy by adding them to the style normalized feature $\mathaccent "0365{F}$ (see Section \ref {subsec:SNR}).}}{2}{figure.caption.2}\protected@file@percent }
\newlabel{fig:flowchart}{{2}{2}{Overall flowchart. (a) Our generalizable feature learning network with the proposed Style Normalization and Restitution (SNR) module being plugged in after some convolutional blocks. Here, we use ResNet-50 as our backbone for illustration. (b) Proposed SNR module. Instance Normalization (IN) is used to eliminate some style discrepancies followed by task-relevant feature restitution (marked by red solid arrows). Note the branch with dashed green line is only used for enforcing loss constraint and is discarded in inference. (c) Dual \ourloss constraint encourages the disentanglement of a residual feature $R$ to task-relevant one ($R^+$) and task-irrelevant one ($R^-$), which decreases and enhances, respectively, the entropy by adding them to the style normalized feature $\widetilde {F}$ (see Section \ref {subsec:SNR})}{figure.caption.2}{}}
\citation{li2018learning}
\citation{li2019episodic}
\citation{zhou2020domain}
\citation{li2018learning}
\citation{li2019episodic}
\citation{zhou2020domain}
\citation{shankar2018generalizing,volpi2018generalizing}
\citation{muandet2013domain,motiian2017unified}
\citation{pan2018two,jia2019frustratingly}
\citation{ulyanov2016instance}
\citation{bilen2017universal}
\citation{ulyanov2017improved,dumoulin2016learned,huang2017arbitrary}
\citation{bilen2017universal,pan2018two}
\citation{pan2018two}
\citation{huang2017arbitrary,pan2018two}
\citation{pan2018two}
\citation{nam2018batch}
\citation{long2015learning,long2016unsupervised,long2017deep,yan2019weighted}
\citation{sun2016return,sun2016deep,peng2018synthetic}
\citation{ganin2014unsupervised,tzeng2017adversarial,zhang2018collaborative,zhao2018adversarial}
\citation{saito2018maximum}
\citation{saito2019semi}
\citation{peng2019moment}
\citation{peng2019federated}
\citation{liu2018unified}
\citation{lee2018diverse}
\citation{he2016deep}
\@writefile{toc}{\contentsline {section}{\numberline {II}Related Work}{3}{section.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-A}}Domain Generalization (DG)}{3}{subsection.2.1}\protected@file@percent }
\newlabel{subsec:relatedDG}{{\mbox  {II-A}}{3}{Domain Generalization (DG)}{subsection.2.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-B}}Unsupervised Domain Adaptation (UDA)}{3}{subsection.2.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-C}}Feature Disentanglement}{3}{subsection.2.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {III}Style Normalization and Restitution}{3}{section.3}\protected@file@percent }
\citation{muandet2013domain}
\citation{huang2017arbitrary,pan2018two}
\citation{pan2018two,jia2019frustratingly,zhou2019omni}
\citation{ulyanov2016instance,dumoulin2016learned,ulyanov2017improved,huang2017arbitrary}
\citation{huang2017arbitrary}
\citation{pan2018two}
\citation{hu2018squeeze}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-A}}Style Normalization and Restitution Module}{4}{subsection.3.1}\protected@file@percent }
\newlabel{subsec:SNR}{{\mbox  {III-A}}{4}{Style Normalization and Restitution Module}{subsection.3.1}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-A}1}Style Normalization to Reduce Domain Discrepancy}{4}{subsubsection.3.1.1}\protected@file@percent }
\newlabel{sec:restitution}{{\mbox  {III-A}2}{4}{Feature Restitution to Preserve Discrimination}{subsubsection.3.1.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-A}2}Feature Restitution to Preserve Discrimination}{4}{subsubsection.3.1.2}\protected@file@percent }
\newlabel{eq:Residual}{{2}{4}{Feature Restitution to Preserve Discrimination}{equation.2}{}}
\newlabel{eq:seperation}{{3}{4}{Feature Restitution to Preserve Discrimination}{equation.3}{}}
\newlabel{eq:se}{{4}{4}{Feature Restitution to Preserve Discrimination}{equation.4}{}}
\newlabel{eq:addrelevant}{{5}{4}{Feature Restitution to Preserve Discrimination}{equation.5}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces Illustration of obtaining feature vector for restitution loss optimization with respect to different tasks. (a) For classification task, spatial average pooling is performed over the entire feature map ($h\times w \times c)$ to obtain a feature vector of $c$ dimensions (see Section \ref {subsubsec:cla}). (b) For segmentation task (pixel level classification), entropy is calculated for each pixel (see Section \ref {subsubsec:seg}). (c) For detection task (region level classification), spatial average pooling is performed over each groundtruth bounding box (bbox) region to obtain a feature vector of $c$ dimensions (see Section \ref {subsubsec:det}).}}{5}{figure.caption.3}\protected@file@percent }
\newlabel{fig:loss}{{3}{5}{Illustration of obtaining feature vector for \ourloss optimization with respect to different tasks. (a) For classification task, spatial average pooling is performed over the entire feature map ($h\times w \times c)$ to obtain a feature vector of $c$ dimensions (see Section \ref {subsubsec:cla}). (b) For segmentation task (pixel level classification), entropy is calculated for each pixel (see Section \ref {subsubsec:seg}). (c) For detection task (region level classification), spatial average pooling is performed over each groundtruth bounding box (bbox) region to obtain a feature vector of $c$ dimensions (see Section \ref {subsubsec:det})}{figure.caption.3}{}}
\newlabel{sec:dual_loss}{{\mbox  {III-A}3}{5}{Dual {Restitution} Loss Constraint}{subsubsection.3.1.3}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-A}3}Dual {Restitution} Loss Constraint}{5}{subsubsection.3.1.3}\protected@file@percent }
\newlabel{eq:sep_loss1}{{6}{5}{Dual {Restitution} Loss Constraint}{equation.6}{}}
\newlabel{eq:sep_loss2}{{7}{5}{Dual {Restitution} Loss Constraint}{equation.7}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-B}}Applications, Extensions, and Variants}{5}{subsection.3.2}\protected@file@percent }
\newlabel{subsubsec:cla}{{\mbox  {III-B}1}{5}{Classification}{subsubsection.3.2.1}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}1}Classification}{5}{subsubsection.3.2.1}\protected@file@percent }
\newlabel{subsubsec:seg}{{\mbox  {III-B}2}{5}{Segmentation}{subsubsection.3.2.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}2}Segmentation}{5}{subsubsection.3.2.2}\protected@file@percent }
\newlabel{eq:sep_loss}{{8}{5}{Segmentation}{equation.8}{}}
\citation{girshick2014rich}
\citation{ren2015faster}
\citation{he2017mask}
\citation{li2018domain}
\citation{motiian2017unified}
\citation{carlucci2019domain}
\citation{shankar2018generalizing}
\citation{li2019episodic}
\citation{zhou2020learning}
\citation{long2015learning}
\citation{sun2016return}
\citation{ganin2016domain}
\citation{long2017deep}
\citation{tzeng2017adversarial}
\citation{xu2018deep}
\citation{wang2018visual}
\citation{saito2018maximum}
\citation{peng2019moment}
\citation{peng2019moment}
\citation{saito2018maximum}
\citation{xu2018deep}
\citation{ganin2016domain}
\citation{peng2019moment}
\citation{peng2019moment}
\citation{saito2019semi}
\citation{saito2018maximum}
\citation{xu2018deep}
\citation{ganin2016domain}
\citation{xu2018deep}
\citation{saito2018maximum}
\citation{venkateswara2017Deep}
\citation{lecun1998mnist}
\citation{ganin2015unsupervised}
\citation{hull1994database}
\citation{netzer2011svhn}
\citation{ganin2015unsupervised}
\citation{peng2019moment}
\citation{li2018domain}
\citation{motiian2017unified}
\citation{shankar2018generalizing}
\citation{carlucci2019domain}
\citation{li2019episodic}
\citation{zhou2020learning}
\citation{zhou2020learning}
\citation{peng2019moment}
\citation{peng2019moment}
\citation{peng2019moment}
\citation{peng2019moment}
\citation{ioffe2015batch}
\citation{pan2018two}
\citation{nam2018batch}
\citation{hu2018squeeze}
\newlabel{subsubsec:det}{{\mbox  {III-B}3}{6}{Detection}{subsubsection.3.2.3}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}3}Detection}{6}{subsubsection.3.2.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {IV}Experiment}{6}{section.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-A}}Object Classification}{6}{subsection.4.1}\protected@file@percent }
\newlabel{subsec:cla}{{\mbox  {IV-A}}{6}{Object Classification}{subsection.4.1}{}}
\newlabel{subsec:dataset}{{\mbox  {IV-A}1}{6}{Datasets and Implementation Details}{subsubsection.4.1.1}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}1}Datasets and Implementation Details}{6}{table.caption.6}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}2}Results on Domain Generalization}{6}{subsubsection.4.1.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}3}Results on Unsupervised Domain Adaptation}{6}{subsubsection.4.1.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}4}Ablation Study}{6}{subsubsection.4.1.4}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {I}{\ignorespaces Performance (in accuracy \%) comparisons with the SOTA domain generalization approaches for image classification.}}{7}{table.caption.4}\protected@file@percent }
\newlabel{tab:sto_dg}{{I}{7}{Performance (in accuracy \%) comparisons with the SOTA domain generalization approaches for image classification}{table.caption.4}{}}
\@writefile{lot}{\contentsline {table}{\numberline {II}{\ignorespaces Ablation study and performance comparisons (\%) with the SOTA UDA approaches for image classification.}}{7}{table.caption.5}\protected@file@percent }
\newlabel{tab:sto_digit5}{{IIa}{7}{Subtable IIa}{subtable.II.1}{}}
\newlabel{sub@tab:sto_digit5}{{(a)}{a}{Subtable IIa\relax }{subtable.II.1}{}}
\newlabel{tab:sto_mini_domainNet}{{IIb}{7}{Subtable IIb}{subtable.II.2}{}}
\newlabel{sub@tab:sto_mini_domainNet}{{(b)}{b}{Subtable IIb\relax }{subtable.II.2}{}}
\newlabel{tab:sto_uda}{{II}{7}{Ablation study and performance comparisons (\%) with the SOTA UDA approaches for image classification}{subtable.II.2}{}}
\@writefile{lot}{\contentsline {subtable}{\numberline{(a)}{\ignorespaces {Results on Digit-Five. }}}{7}{subtable.2.1}\protected@file@percent }
\@writefile{lot}{\contentsline {subtable}{\numberline{(b)}{\ignorespaces {Results on mini-DomainNet. }}}{7}{subtable.2.2}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {III}{\ignorespaces Performance (\%) comparisons with the SOTA approaches for UDA on the full DomainNet dataset.}}{7}{table.caption.6}\protected@file@percent }
\newlabel{tab:full_domainnet}{{III}{7}{Performance (\%) comparisons with the SOTA approaches for UDA on the full DomainNet dataset}{table.caption.6}{}}
\@writefile{lot}{\contentsline {table}{\numberline {IV}{\ignorespaces Effectiveness of our SNR, compared to other normalization-based methods for domain generalizable classification. Note that the \emph  {italics} denotes the left-out target domain. We use ResNet18 as our backbone. }}{7}{table.caption.7}\protected@file@percent }
\newlabel{tab:ablation_study}{{IV}{7}{Effectiveness of our SNR, compared to other normalization-based methods for domain generalizable classification. Note that the \emph {italics} denotes the left-out target domain. We use ResNet18 as our backbone}{table.caption.7}{}}
\@writefile{lot}{\contentsline {table}{\numberline {V}{\ignorespaces Ablation study on the dual restitution loss $\mathcal  {L}_{SNR}$ for domain generalizable classification. Backbone is ResNet18.}}{8}{table.caption.8}\protected@file@percent }
\newlabel{tab:loss_snr}{{V}{8}{Ablation study on the dual \ourloss $\mathcal {L}_{SNR}$ for domain generalizable classification. Backbone is ResNet18}{table.caption.8}{}}
\@writefile{lot}{\contentsline {table}{\numberline {VI}{\ignorespaces Influence of SNR modules for DG and UDA respectively on top of a simple ResNet-50 baseline without incorporating other UDA methods. DG schemes \emph  {Baseline(AGG)} and \emph  {SNR} do not use target domain data for training. \emph  {SNR-UDA} uses target domain unlabeled data for training.}}{8}{table.caption.9}\protected@file@percent }
\newlabel{tab:SNR-UDA}{{VI}{8}{Influence of SNR modules for DG and UDA respectively on top of a simple ResNet-50 baseline without incorporating other UDA methods. DG schemes \emph {Baseline(AGG)} and \emph {SNR} do not use target domain data for training. \emph {SNR-UDA} uses target domain unlabeled data for training}{table.caption.9}{}}
\newlabel{subsec:design}{{\mbox  {IV-A}5}{8}{Design Choices of SNR}{subsubsection.4.1.5}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}5}Design Choices of SNR}{8}{subsubsection.4.1.5}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {VII}{\ignorespaces Ablation study on which stage to add SNR.}}{8}{table.caption.10}\protected@file@percent }
\newlabel{tab:stage}{{VII}{8}{Ablation study on which stage to add SNR}{table.caption.10}{}}
\citation{zheng2011person,zhou2019omni}
\citation{maaten2008visualizing}
\citation{peng2019moment}
\citation{Cordts2016Cityscapes}
\citation{ros2016synthia}
\citation{Richter_2016_ECCV}
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces (a) Activation maps of different features within an SNR module (SNR 3). They show that SNR can disentangle out the task-relevant (classification-relevant) object features well (\textit  {i}.\textit  {e}., $R^+$). (b) Activation maps of ours (bottom) and the baseline \emph  {Baseline (AGG)} (top) w.r.t images of varied styles. The maps of SNR are more consistent for images of different styles.}}{9}{figure.caption.11}\protected@file@percent }
\newlabel{fig:vis_ftp}{{4}{9}{(a) Activation maps of different features within an SNR module (SNR 3). They show that SNR can disentangle out the task-relevant (classification-relevant) object features well (\ieno , $R^+$). (b) Activation maps of ours (bottom) and the baseline \emph {Baseline (AGG)} (top) w.r.t images of varied styles. The maps of SNR are more consistent for images of different styles}{figure.caption.11}{}}
\@writefile{lot}{\contentsline {table}{\numberline {VIII}{\ignorespaces Study on the disentanglement designs in SNR}}{9}{table.caption.12}\protected@file@percent }
\newlabel{tab:disentangle}{{VIII}{9}{Study on the disentanglement designs in SNR}{table.caption.12}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5}{\ignorespaces Visualization of t-SNE distributions on the Digit-Five dataset for UDA classification task. We compare our \emph  {SNR-M3SDA} with the baseline scheme \emph  {Baseline (M3SDA)}.}}{9}{figure.caption.13}\protected@file@percent }
\newlabel{fig:tSNE}{{5}{9}{Visualization of t-SNE distributions on the Digit-Five dataset for UDA classification task. We compare our \emph {SNR-M3SDA} with the baseline scheme \emph {Baseline (M3SDA)}}{figure.caption.13}{}}
\newlabel{subsec:visualization}{{\mbox  {IV-A}6}{9}{Visualization}{subsubsection.4.1.6}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}6}Visualization}{9}{subsubsection.4.1.6}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-B}}Semantic Segmentation}{9}{subsection.4.2}\protected@file@percent }
\newlabel{subsec:seg}{{\mbox  {IV-B}}{9}{Semantic Segmentation}{subsection.4.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-B}1}Datasets and Implementation Details}{9}{subsubsection.4.2.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-B}2}Results on Domain Generalization}{9}{subsubsection.4.2.2}\protected@file@percent }
\citation{ganin2016domain}
\citation{saito2018maximum}
\citation{tsai2018learning}
\citation{vu2019advent}
\citation{vu2019advent}
\citation{chen2019domain}
\citation{ganin2016domain}
\citation{saito2018maximum}
\citation{tsai2018learning}
\citation{vu2019advent}
\citation{vu2019advent}
\citation{chen2019domain}
\citation{saito2018maximum,chen2019domain}
\citation{saito2018maximum}
\citation{chen2019domain}
\citation{saito2018maximum}
\citation{chen2019domain}
\citation{saito2018maximum}
\citation{chen2019domain}
\citation{saito2018maximum}
\citation{saito2018maximum}
\citation{chen2018domain,khodabandeh2019robust}
\citation{Cordts2016Cityscapes}
\citation{sakaridis2018semantic}
\citation{Geiger2013IJRR}
\citation{ren2015faster}
\citation{chen2018domain}
\citation{ren2015faster}
\citation{chen2018domain}
\citation{chen2018domain}
\citation{chen2018domain}
\citation{ren2015faster}
\citation{ren2015faster}
\@writefile{lot}{\contentsline {table}{\numberline {IX}{\ignorespaces Domain generalization performance (\%) for semantic segmentation when we train on GTA5 and test on Cityscapes.}}{10}{table.caption.14}\protected@file@percent }
\newlabel{tab:dg_seg}{{IX}{10}{Domain generalization performance (\%) for semantic segmentation when we train on GTA5 and test on Cityscapes}{table.caption.14}{}}
\@writefile{lot}{\contentsline {table}{\numberline {X}{\ignorespaces Domain generalization performance (\%) of semantic segmentation when we train on Synthia and test on Cityscapes.}}{10}{table.caption.15}\protected@file@percent }
\newlabel{tab:dg_seg_2}{{X}{10}{Domain generalization performance (\%) of semantic segmentation when we train on Synthia and test on Cityscapes}{table.caption.15}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-B}3}Results on Unsupervised Domain Adaptation}{10}{subsubsection.4.2.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-B}4}Visualization of DG and UDA Results}{10}{subsubsection.4.2.4}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {6}{\ignorespaces Qualitative results on domain generable segmentation (first row) and domain adaptive segmentation (second row) from GTA5 to Cityscapes. For DG (first row), \emph  {Baseline} denotes the baseline scheme trained with source domain dataset while testing on the target domain directly. \emph  {SNR} denotes our scheme which adds SNR modules to \emph  {Baseline}. For UDA (second row), we compare the baseline scheme \emph  {Baseline (MCD)}~\cite  {saito2018maximum} to the scheme \emph  {SNR+MCD} which is powered by our SNR.}}{10}{figure.caption.18}\protected@file@percent }
\newlabel{fig:seg}{{6}{10}{Qualitative results on domain generable segmentation (first row) and domain adaptive segmentation (second row) from GTA5 to Cityscapes. For DG (first row), \emph {Baseline} denotes the baseline scheme trained with source domain dataset while testing on the target domain directly. \emph {SNR} denotes our scheme which adds SNR modules to \emph {Baseline}. For UDA (second row), we compare the baseline scheme \emph {Baseline (MCD)}~\cite {saito2018maximum} to the scheme \emph {SNR+MCD} which is powered by our SNR}{figure.caption.18}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-C}}Object Detection}{10}{subsection.4.3}\protected@file@percent }
\newlabel{subsec:det}{{\mbox  {IV-C}}{10}{Object Detection}{subsection.4.3}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-C}1}Datasets and Implementation Details}{10}{subsubsection.4.3.1}\protected@file@percent }
\citation{chen2018domain}
\@writefile{lot}{\contentsline {table}{\numberline {XI}{\ignorespaces Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for GTA5-to-Cityscape.}}{11}{table.caption.16}\protected@file@percent }
\newlabel{tab:uda_seg_1}{{XI}{11}{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for GTA5-to-Cityscape}{table.caption.16}{}}
\@writefile{lot}{\contentsline {table}{\numberline {XII}{\ignorespaces Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for Synthia-to-Cityscape.}}{11}{table.caption.17}\protected@file@percent }
\newlabel{tab:uda_seg_2}{{XII}{11}{Performance (\%) comparisons with the state-of-the-art semantic segmentation approaches for unsupervised domain adaptation for Synthia-to-Cityscape}{table.caption.17}{}}
\@writefile{lot}{\contentsline {table}{\numberline {XIII}{\ignorespaces Performance (in mAP accuracy \%) of object detection on the Foggy Cityscapes validation set, models are trained on the Cityscapes training set.}}{11}{table.caption.19}\protected@file@percent }
\newlabel{tab:detec_1}{{XIII}{11}{Performance (in mAP accuracy \%) of object detection on the Foggy Cityscapes validation set, models are trained on the Cityscapes training set}{table.caption.19}{}}
\@writefile{lot}{\contentsline {table}{\numberline {XIV}{\ignorespaces Performance (in AP accuracy \%) for the class of Car for object detection on KITTI (K) and Cityscapes (C).}}{11}{table.caption.20}\protected@file@percent }
\newlabel{tab:detec_2}{{XIV}{11}{Performance (in AP accuracy \%) for the class of Car for object detection on KITTI (K) and Cityscapes (C)}{table.caption.20}{}}
\@writefile{lot}{\contentsline {table}{\numberline {XV}{\ignorespaces Comparisons of complexity and model sizes. FLOPs: the number of FLoating-point OPerations; Params: the number of parameter.}}{11}{table.caption.22}\protected@file@percent }
\newlabel{tab:complexity}{{XV}{11}{Comparisons of complexity and model sizes. FLOPs: the number of FLoating-point OPerations; Params: the number of parameter}{table.caption.22}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7}{\ignorespaces Qualitative comparisons of the baseline approach DA Faster R-CNN ~\cite  {chen2018domain} and the baseline powered by our SNR on ``Cityscapes{} $\rightarrow $ KITTI{}''. Top and bottom rows denote the detected cars by the baseline scheme \emph  {DA Faster R-CNN} and our scheme \emph  {SNR-DA Faster R-CNN} respectively. }}{11}{figure.caption.21}\protected@file@percent }
\newlabel{fig:det}{{7}{11}{Qualitative comparisons of the baseline approach DA Faster R-CNN ~\cite {chen2018domain} and the baseline powered by our SNR on ``\city {} $\rightarrow $ \kit {}''. Top and bottom rows denote the detected cars by the baseline scheme \emph {DA Faster R-CNN} and our scheme \emph {SNR-DA Faster R-CNN} respectively}{figure.caption.21}{}}
\citation{sakaridis2018semantic}
\citation{ren2015faster}
\citation{chen2018domain}
\citation{torralba2011unbiased}
\citation{ren2015faster}
\citation{chen2018domain}
\citation{he2016deep}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-C}2}Results on DG and UDA}{12}{subsubsection.4.3.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-C}3}Qualitative Results}{12}{subsubsection.4.3.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-D}}Complexity Analysis}{12}{subsection.4.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {V}Conclusion}{12}{section.5}\protected@file@percent }
\citation{lecun1998mnist}
\citation{ganin2015unsupervised}
\citation{hull1994database}
\citation{netzer2011svhn}
\citation{ganin2015unsupervised}
\citation{zhou2020domain}
\citation{lecun1998mnist}
\citation{ganin2015unsupervised}
\citation{hull1994database}
\citation{netzer2011svhn}
\citation{ganin2015unsupervised}
\citation{zhou2020domain}
\citation{li2017deeper}
\citation{office_home}
\citation{lecun1998mnist}
\citation{ganin2015unsupervised}
\citation{hull1994database}
\citation{netzer2011svhn}
\citation{ganin2015unsupervised}
\citation{peng2019moment}
\citation{peng2019moment}
\citation{zhou2020domain}
\citation{peng2019moment}
\citation{li2019episodic}
\citation{cvpr19JiGen,li2019episodic}
\citation{li2017deeper,cvpr19JiGen,li2019episodic}
\citation{peng2019moment}
\citation{zhou2020domain}
\citation{he2016deep}
\citation{he2016deep}
\citation{cosineLR}
\citation{hoffman2016fcns,zhang2017curriculum,saito2018maximum}
\citation{tsai2018learning,chen2019domain}
\citation{saito2018maximum}
\citation{yu2017dilated,saito2018maximum}
\citation{he2016deep}
\citation{deeplabv2}
\citation{ResNet}
\citation{ImageNet}
\citation{AdaptSegNet,ADVENT}
\citation{chen2019domain}
\citation{deeplabv2}
\citation{zhao2017pyramid}
\citation{Cordts2016Cityscapes}
\citation{chen2018domain}
\citation{sakaridis2018semantic}
\citation{sakaridis2018semantic}
\citation{Geiger2013IJRR}
\citation{chen2018domain}
\@writefile{toc}{\contentsline {section}{\numberline {VI}Datasets and Implementation Details}{13}{section.6}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {VI-A}}Object Classification}{13}{subsection.6.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {VI-B}}Semantic Segmentation}{13}{subsection.6.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {VI-C}}Object Detection}{13}{subsection.6.3}\protected@file@percent }
\bibstyle{IEEEtran}
\bibdata{IEEEabrv}
\bibcite{krizhevsky2012imagenet}{1}
\bibcite{long2016unsupervised}{2}
\bibcite{ma2019deep}{3}
\bibcite{muandet2013domain}{4}
\bibcite{li2017deeper}{5}
\bibcite{shankar2018generalizing}{6}
\bibcite{li2018learning}{7}
\bibcite{carlucci2019domain}{8}
\bibcite{li2019episodic}{9}
\bibcite{pan2009survey}{10}
\bibcite{ganin2014unsupervised}{11}
\bibcite{long2015learning}{12}
\bibcite{saito2018maximum}{13}
\bibcite{hoffman2018cycada}{14}
\bibcite{peng2019moment}{15}
\bibcite{xu2019larger}{16}
\bibcite{wang2019transferable}{17}
\bibcite{sun2016return}{18}
\bibcite{sun2016deep}{19}
\bibcite{peng2018synthetic}{20}
\bibcite{zellinger2017central}{21}
\bibcite{tzeng2014deep}{22}
\bibcite{long2017deep}{23}
\bibcite{ganin2016domain}{24}
\bibcite{tzeng2017adversarial}{25}
\bibcite{liu2019transferabletat}{26}
\bibcite{ghifary2016scatter}{27}
\bibcite{motiian2017unified}{28}
\bibcite{jia2019frustratingly}{29}
\bibcite{song2019generalizable}{30}
\bibcite{zhou2020domain}{31}
\bibcite{zhou2019omni}{32}
\bibcite{huang2017arbitrary}{33}
\bibcite{pan2018two}{34}
\bibcite{ulyanov2017improved}{35}
\bibcite{dumoulin2016learned}{36}
\bibcite{jin2020style}{37}
\bibcite{volpi2018generalizing}{38}
\@writefile{lof}{\contentsline {figure}{\numberline {8}{\ignorespaces Four classification datasets (first two for DG and last two for UDA). (a) PACS, which includes \textit  {Sketch}, \textit  {Photo}, \textit  {Cartoon}, and \textit  {Art}. (b) Office-Home, which includes {Real-world} (\textit  {Real}), \textit  {Product}, \textit  {Clipart}, and \textit  {Art}. (c) Digit-Five, which includes MNIST \cite  {lecun1998mnist} (\textit  {\textbf  {mt}}), MNIST-M \cite  {ganin2015unsupervised} (\textit  {\textbf  {mm}}), USPS \cite  {hull1994database} (\textit  {\textbf  {up}}), SVHN \cite  {netzer2011svhn} (\textit  {\textbf  {sv}}), and Synthetic \cite  {ganin2015unsupervised} (\textit  {\textbf  {syn}}). (d) DomainNet, which includes {Clipart} (\textit  {\textbf  {clp}}), {Infograph} (\textit  {\textbf  {inf}}), {Painting} (\textit  {\textbf  {pnt}}), {Quickdraw} (\textit  {\textbf  {qdr}}), {Real} (\textit  {\textbf  {rel}}), and {Sktech} (\textit  {\textbf  {skt}}). Considering the required huge computation resources, we use a subset of DomainNet (\textit  {i}.\textit  {e}., mini-DomainNet) following \cite  {zhou2020domain} for ablation experiments. The full DomainNet dataset is also used for performance comparison with the state-of-the-art methods.}}{14}{figure.caption.23}\protected@file@percent }
\newlabel{fig:datasets}{{8}{14}{Four classification datasets (first two for DG and last two for UDA). (a) PACS, which includes \textit {Sketch}, \textit {Photo}, \textit {Cartoon}, and \textit {Art}. (b) Office-Home, which includes {Real-world} (\textit {Real}), \textit {Product}, \textit {Clipart}, and \textit {Art}. (c) Digit-Five, which includes MNIST \cite {lecun1998mnist} (\textit {\textbf {mt}}), MNIST-M \cite {ganin2015unsupervised} (\textit {\textbf {mm}}), USPS \cite {hull1994database} (\textit {\textbf {up}}), SVHN \cite {netzer2011svhn} (\textit {\textbf {sv}}), and Synthetic \cite {ganin2015unsupervised} (\textit {\textbf {syn}}). (d) DomainNet, which includes {Clipart} (\textit {\textbf {clp}}), {Infograph} (\textit {\textbf {inf}}), {Painting} (\textit {\textbf {pnt}}), {Quickdraw} (\textit {\textbf {qdr}}), {Real} (\textit {\textbf {rel}}), and {Sktech} (\textit {\textbf {skt}}). Considering the required huge computation resources, we use a subset of DomainNet (\ieno , mini-DomainNet) following \cite {zhou2020domain} for ablation experiments. The full DomainNet dataset is also used for performance comparison with the state-of-the-art methods}{figure.caption.23}{}}
\@writefile{toc}{\contentsline {section}{References}{14}{section*.24}\protected@file@percent }
\bibcite{ulyanov2016instance}{39}
\bibcite{bilen2017universal}{40}
\bibcite{nam2018batch}{41}
\bibcite{yan2019weighted}{42}
\bibcite{zhang2018collaborative}{43}
\bibcite{zhao2018adversarial}{44}
\bibcite{saito2019semi}{45}
\bibcite{peng2019federated}{46}
\bibcite{liu2018unified}{47}
\bibcite{lee2018diverse}{48}
\bibcite{he2016deep}{49}
\bibcite{hu2018squeeze}{50}
\bibcite{girshick2014rich}{51}
\bibcite{ren2015faster}{52}
\bibcite{he2017mask}{53}
\bibcite{li2018domain}{54}
\bibcite{zhou2020learning}{55}
\bibcite{xu2018deep}{56}
\bibcite{wang2018visual}{57}
\bibcite{venkateswara2017Deep}{58}
\bibcite{lecun1998mnist}{59}
\bibcite{ganin2015unsupervised}{60}
\bibcite{hull1994database}{61}
\bibcite{netzer2011svhn}{62}
\bibcite{ioffe2015batch}{63}
\bibcite{zheng2011person}{64}
\bibcite{maaten2008visualizing}{65}
\bibcite{Cordts2016Cityscapes}{66}
\bibcite{ros2016synthia}{67}
\bibcite{Richter_2016_ECCV}{68}
\bibcite{tsai2018learning}{69}
\bibcite{vu2019advent}{70}
\bibcite{chen2019domain}{71}
\bibcite{chen2018domain}{72}
\bibcite{khodabandeh2019robust}{73}
\bibcite{sakaridis2018semantic}{74}
\bibcite{Geiger2013IJRR}{75}
\bibcite{torralba2011unbiased}{76}
\bibcite{office_home}{77}
\bibcite{cvpr19JiGen}{78}
\bibcite{cosineLR}{79}
\bibcite{hoffman2016fcns}{80}
\bibcite{zhang2017curriculum}{81}
\bibcite{yu2017dilated}{82}
\bibcite{deeplabv2}{83}
\bibcite{ResNet}{84}
\bibcite{ImageNet}{85}
\bibcite{AdaptSegNet}{86}
\bibcite{ADVENT}{87}
\bibcite{zhao2017pyramid}{88}
\gdef \@abspage@last{15}
