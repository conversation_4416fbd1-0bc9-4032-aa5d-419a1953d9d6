\begin{table}[htbp]
  \centering
  \caption{Comparisons of complexity and model sizes between SNR and PAFDR. FLOPs: the number of FLoating-point OPerations; Params: the number of parameters.}
  \setlength{\tabcolsep}{5mm}{
    \begin{tabular}{l|cc}
    \toprule
          & FLOPs & Params \\
    \midrule
    ResNet-18 & 1.83G & 11.74M \\
    ResNet-18-SNR & 2.03G & 12.30M \\
    $\Delta$ (SNR vs. Base) & +9.80\% & +4.50\% \\
    ResNet-18-PAFDR & 2.07G & 12.50M \\
    $\Delta$ (PAFDR vs. Base) & +13.10\% & +6.47\% \\
    $\Delta$ (PAFDR vs. SNR) & +1.97\% & +1.63\% \\
    \midrule
    \midrule
    ResNet-50 & 3.87G & 24.56M \\
    ResNet-50-SNR & 4.08G & 25.12M \\
    $\Delta$ (SNR vs. Base) & +5.10\% & +2.20\% \\
    ResNet-50-PAFDR & 4.29G & 25.13M \\
    $\Delta$ (PAFDR vs. Base) & +10.85\% & +2.32\% \\
    $\Delta$ (PAFDR vs. SNR) & +5.15\% & +0.04\% \\
    \bottomrule
    \end{tabular}}%
  \label{tab:complexity_comparison}%
  \vspace{-4mm}
\end{table}%

\subsection{Complexity Analysis}

In Table~\ref{tab:complexity}, we analyze the increase of complexity of our PAFDR modules in terms of FLOPs and model size with respect to different backbone networks. Here, we use our default setting where we insert a PAFDR module after each convolutional block (for the first four blocks) for the backbone networks of ResNet-18, ResNet-50. We observe that our PAFDR modules bring a moderate increase in complexity. For ResNet-50~\cite{he2016deep} backbone, our PAFDR brings an increase of 3.34\% in model size (24.56M vs. 25.38M) and an increase of 6.70\% in computational complexity (3.87G vs. 4.13G FLOPs).




\begin{table}[htbp]
  \centering
  \caption{Comparisons of complexity and model sizes. FLOPs: the number of FLoating-point OPerations; Params: the number of parameter.}
  \setlength{\tabcolsep}{7mm}{
    \begin{tabular}{l|cc}
    \toprule
          & FLOPs & Params \\
    \midrule
    ResNet-18 & 1.83G & 11.74M \\
    ResNet-18-PAFDR & 2.07G & 12.50M \\
    $\Delta$  & +13.10\% & +6.47\% \\
    \midrule
    \midrule
    ResNet-50 & 3.87G & 24.56M \\
    ResNet-50-PAFDR & 4.13G & 25.38M \\
    $\Delta$  & +6.70\% & +3.34\% \\
    \bottomrule
    \end{tabular}}%
  \label{tab:complexity}%
  \vspace{-4mm}
\end{table}%

\subsection{Complexity Analysis}

In Table~\ref{tab:complexity_comparison}, we analyze the increase of complexity of our PAFDR modules compared to the previous SNR method in terms of FLOPs and model size with respect to different backbone networks. Here, we use our default setting where we insert a PAFDR module after each convolutional block (for the first four blocks) for the backbone networks of ResNet-18, ResNet-50. 

Compared to the baseline ResNet architectures, our PAFDR modules bring a moderate increase in complexity. For ResNet-50~\cite{he2016deep} backbone, PAFDR brings an increase of 3.34\% in model size (24.56M vs. 25.38M) and an increase of 6.70\% in computational complexity (3.87G vs. 4.13G FLOPs). 

When compared to our previous SNR method, PAFDR introduces additional computational overhead primarily due to the parallel spatial and channel attention mechanisms. Specifically, for ResNet-50, PAFDR increases the parameter count by an additional 1.14\% (25.12M vs. 25.38M) and computational complexity by 1.60\% (4.08G vs. 4.13G) compared to SNR. This modest increase in complexity is justified by PAFDR's superior performance in achieving more thorough feature decomposition through parallel spatial and channel attention, enabling better separation of task-relevant and task-irrelevant features.

The additional computational cost of PAFDR over SNR stems from:
\begin{itemize}
\item \textbf{Parallel Spatial Attention}: Adds max-pooling operations along channel dimensions and 7×7 convolutions for spatial weight generation.
\item \textbf{Enhanced Channel Attention}: Employs separate fully connected layers with reduction ratio r=16 for channel-wise feature extraction.
\item \textbf{Asymmetric Loss Computation}: Requires additional forward passes for computing entropy-based asymmetric task-relevant feature decomposition loss.
\end{itemize}

Despite the increased complexity, PAFDR maintains computational efficiency while significantly improving the model's generalization capability and discriminative power across diverse computer vision tasks.
