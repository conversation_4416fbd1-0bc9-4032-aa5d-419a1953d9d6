% Generated by IEEEtran.bst, version: 1.14 (2015/08/26)
\begin{thebibliography}{10}
\providecommand{\url}[1]{#1}
\csname url@samestyle\endcsname
\providecommand{\newblock}{\relax}
\providecommand{\bibinfo}[2]{#2}
\providecommand{\BIBentrySTDinterwordspacing}{\spaceskip=0pt\relax}
\providecommand{\BIBentryALTinterwordstretchfactor}{4}
\providecommand{\BIBentryALTinterwordspacing}{\spaceskip=\fontdimen2\font plus
\BIBentryALTinterwordstretchfactor\fontdimen3\font minus
  \fontdimen4\font\relax}
\providecommand{\BIBforeignlanguage}[2]{{%
\expandafter\ifx\csname l@#1\endcsname\relax
\typeout{** WARNING: IEEEtran.bst: No hyphenation pattern has been}%
\typeout{** loaded for the language `#1'. Using the pattern for}%
\typeout{** the default language instead.}%
\else
\language=\csname l@#1\endcsname
\fi
#2}}
\providecommand{\BIBdecl}{\relax}
\BIBdecl

\bibitem{krizhevsky2012imagenet}
A.~<PERSON><PERSON><PERSON><PERSON>, <PERSON>.~<PERSON><PERSON>ever, and G.~E. Hinton, ``Imagenet classification with
  deep convolutional neural networks,'' in \emph{NeurIPS}, 2012, pp.
  1097--1105.

\bibitem{long2016unsupervised}
M.~Long, H.~Zhu, J.~Wang, and M.~I. Jordan, ``Unsupervised domain adaptation
  with residual transfer networks,'' in \emph{NeurIPS}, 2016, pp. 136--144.

\bibitem{ma2019deep}
X.~Ma, T.~Zhang, and C.~Xu, ``Deep multi-modality adversarial networks for
  unsupervised domain adaptation,'' \emph{IEEE TMM}, vol.~21, no.~9, pp.
  2419--2431, 2019.

\bibitem{muandet2013domain}
K.~Muandet, D.~Balduzzi, and B.~Sch{\"o}lkopf, ``Domain generalization via
  invariant feature representation,'' in \emph{International Conference on
  Machine Learning}, 2013, pp. 10--18.

\bibitem{li2017deeper}
D.~Li, Y.~Yang, Y.-Z. Song, and T.~M. Hospedales, ``Deeper, broader and artier
  domain generalization,'' in \emph{ICCV}, 2017, pp. 5542--5550.

\bibitem{shankar2018generalizing}
S.~Shankar, V.~Piratla, S.~Chakrabarti, S.~Chaudhuri, P.~Jyothi, and
  S.~Sarawagi, ``Generalizing across domains via cross-gradient training,''
  \emph{ICLR}, 2018.

\bibitem{li2018learning}
D.~Li, Y.~Yang, Y.-Z. Song, and T.~M. Hospedales, ``Learning to generalize:
  Meta-learning for domain generalization,'' in \emph{AAAI}, 2018.

\bibitem{carlucci2019domain}
F.~M. Carlucci, A.~D'Innocente, S.~Bucci, B.~Caputo, and T.~Tommasi, ``Domain
  generalization by solving jigsaw puzzles,'' in \emph{CVPR}, 2019, pp.
  2229--2238.

\bibitem{li2019episodic}
D.~Li, J.~Zhang, Y.~Yang, C.~Liu, Y.-Z. Song, and T.~M. Hospedales, ``Episodic
  training for domain generalization,'' in \emph{ICCV}, 2019, pp. 1446--1455.

\bibitem{pan2009survey}
S.~J. Pan and Q.~Yang, ``A survey on transfer learning,'' \emph{IEEE
  Transactions on knowledge and data engineering}, vol.~22, no.~10, pp.
  1345--1359, 2009.

\bibitem{ganin2014unsupervised}
Y.~Ganin and V.~Lempitsky, ``Unsupervised domain adaptation by
  backpropagation,'' \emph{ICML}, 2014.

\bibitem{long2015learning}
M.~Long, Y.~Cao, J.~Wang, and M.~I. Jordan, ``Learning transferable features
  with deep adaptation networks,'' \emph{ICML}, 2015.

\bibitem{saito2018maximum}
K.~Saito, K.~Watanabe, Y.~Ushiku, and T.~Harada, ``Maximum classifier
  discrepancy for unsupervised domain adaptation,'' in \emph{CVPR}, 2018, pp.
  3723--3732.

\bibitem{hoffman2018cycada}
J.~Hoffman, E.~Tzeng, T.~Park, J.-Y. Zhu, P.~Isola, K.~Saenko, A.~A. Efros, and
  T.~Darrell, ``Cycada: Cycle-consistent adversarial domain adaptation,''
  \emph{ICML}, 2018.

\bibitem{peng2019moment}
X.~Peng, Q.~Bai, X.~Xia, Z.~Huang, K.~Saenko, and B.~Wang, ``Moment matching
  for multi-source domain adaptation,'' in \emph{ICCV}, 2019, pp. 1406--1415.

\bibitem{xu2019larger}
R.~Xu, G.~Li, J.~Yang, and L.~Lin, ``Larger norm more transferable: An adaptive
  feature norm approach for unsupervised domain adaptation,'' in \emph{ICCV},
  2019, pp. 1426--1435.

\bibitem{wang2019transferable}
X.~Wang, Y.~Jin, M.~Long, J.~Wang, and M.~I. Jordan, ``Transferable
  normalization: Towards improving transferability of deep neural networks,''
  in \emph{NeurIPS}, 2019, pp. 1951--1961.

\bibitem{sun2016return}
B.~Sun, J.~Feng, and K.~Saenko, ``Return of frustratingly easy domain
  adaptation,'' in \emph{AAAI}, 2016.

\bibitem{sun2016deep}
B.~Sun and K.~Saenko, ``Deep coral: Correlation alignment for deep domain
  adaptation,'' in \emph{ECCV}, 2016, pp. 443--450.

\bibitem{peng2018synthetic}
X.~Peng and K.~Saenko, ``Synthetic to real adaptation with generative
  correlation alignment networks,'' in \emph{WACV}.\hskip 1em plus 0.5em minus
  0.4em\relax IEEE, 2018, pp. 1982--1991.

\bibitem{zellinger2017central}
W.~Zellinger, T.~Grubinger, E.~Lughofer, T.~Natschl{\"a}ger, and
  S.~Saminger-Platz, ``Central moment discrepancy (cmd) for domain-invariant
  representation learning,'' \emph{CoRR}, 2017.

\bibitem{tzeng2014deep}
E.~Tzeng, J.~Hoffman, N.~Zhang, K.~Saenko, and T.~Darrell, ``Deep domain
  confusion: Maximizing for domain invariance,'' \emph{arXiv preprint
  arXiv:1412.3474}, 2014.

\bibitem{long2017deep}
M.~Long, H.~Zhu, J.~Wang, and M.~I. Jordan, ``Deep transfer learning with joint
  adaptation networks,'' in \emph{ICML}, 2017, pp. 2208--2217.

\bibitem{ganin2016domain}
Y.~Ganin, E.~Ustinova, H.~Ajakan, P.~Germain, H.~Larochelle, F.~Laviolette,
  M.~Marchand, and V.~Lempitsky, ``Domain-adversarial training of neural
  networks,'' \emph{The Journal of Machine Learning Research}, vol.~17, no.~1,
  pp. 2096--2030, 2016.

\bibitem{tzeng2017adversarial}
E.~Tzeng, J.~Hoffman, K.~Saenko, and T.~Darrell, ``Adversarial discriminative
  domain adaptation,'' in \emph{CVPR}, 2017, pp. 7167--7176.

\bibitem{liu2019transferabletat}
H.~Liu, M.~Long, J.~Wang, and M.~Jordan, ``Transferable adversarial training: A
  general approach to adapting deep classifiers,'' in \emph{ICML}, 2019, pp.
  4013--4022.

\bibitem{ghifary2016scatter}
M.~Ghifary, D.~Balduzzi, W.~B. Kleijn, and M.~Zhang, ``Scatter component
  analysis: A unified framework for domain adaptation and domain
  generalization,'' \emph{TPAMI}, vol.~39, no.~7, pp. 1414--1430, 2016.

\bibitem{motiian2017unified}
S.~Motiian, M.~Piccirilli, D.~A. Adjeroh, and G.~Doretto, ``Unified deep
  supervised domain adaptation and generalization,'' in \emph{ICCV}, 2017, pp.
  5715--5725.

\bibitem{jia2019frustratingly}
J.~Jia, Q.~Ruan, and T.~M. Hospedales, ``Frustratingly easy person
  re-identification: Generalizing person re-id in practice,'' 2019.

\bibitem{song2019generalizable}
J.~Song, Y.~Yang, Y.-Z. Song, T.~Xiang, and T.~M. Hospedales, ``Generalizable
  person re-identification by domain-invariant mapping network,'' 2019.

\bibitem{zhou2020domain}
K.~Zhou, Y.~Yang, Y.~Qiao, and T.~Xiang, ``Domain adaptive ensemble learning,''
  \emph{arXiv preprint arXiv:2003.07325}, 2020.

\bibitem{zhou2019omni}
K.~Zhou, Y.~Yang, A.~Cavallaro \emph{et~al.}, ``Omni-scale feature learning for
  person re-identification,'' 2019.

\bibitem{huang2017arbitrary}
X.~Huang and S.~Belongie, ``Arbitrary style transfer in real-time with adaptive
  instance normalization,'' in \emph{ICCV}, 2017, pp. 1501--1510.

\bibitem{pan2018two}
X.~Pan, P.~Luo, J.~Shi, and X.~Tang, ``Two at once: Enhancing learning and
  generalization capacities via ibn-net,'' in \emph{ECCV}, 2018, pp. 464--479.

\bibitem{ulyanov2017improved}
D.~Ulyanov, A.~Vedaldi, and V.~Lempitsky, ``Improved texture networks:
  Maximizing quality and diversity in feed-forward stylization and texture
  synthesis,'' in \emph{CVPR}, 2017, pp. 6924--6932.

\bibitem{dumoulin2016learned}
V.~Dumoulin, J.~Shlens, and M.~Kudlur, ``A learned representation for artistic
  style,'' \emph{ICLR}, 2017.

\bibitem{jin2020style}
X.~Jin, C.~Lan, W.~Zeng, Z.~Chen, and L.~Zhang, ``Style normalization and
  restitution for generalizable person re-identification,'' \emph{arXiv
  preprint arXiv:2005.11037}, 2020.

\bibitem{volpi2018generalizing}
R.~Volpi, H.~Namkoong, O.~Sener, J.~C. Duchi, V.~Murino, and S.~Savarese,
  ``Generalizing to unseen domains via adversarial data augmentation,'' in
  \emph{NeurIPS}, 2018, pp. 5334--5344.

\bibitem{ulyanov2016instance}
D.~Ulyanov, A.~Vedaldi, and V.~Lempitsky, ``Instance normalization: The missing
  ingredient for fast stylization,'' \emph{arXiv preprint arXiv:1607.08022},
  2016.

\bibitem{bilen2017universal}
H.~Bilen and A.~Vedaldi, ``Universal representations: The missing link between
  faces, text, planktons, and cat breeds,'' \emph{arXiv preprint
  arXiv:1701.07275}, 2017.

\bibitem{nam2018batch}
H.~Nam and H.-E. Kim, ``Batch-instance normalization for adaptively
  style-invariant neural networks,'' in \emph{NeurIPS}, 2018, pp. 2558--2567.

\bibitem{yan2019weighted}
H.~Yan, Z.~Li, Q.~Wang, P.~Li, Y.~Xu, and W.~Zuo, ``Weighted and class-specific
  maximum mean discrepancy for unsupervised domain adaptation,'' \emph{IEEE
  TMM}, vol.~22, no.~9, pp. 2420--2433, 2019.

\bibitem{zhang2018collaborative}
W.~Zhang, W.~Ouyang, W.~Li, and D.~Xu, ``Collaborative and adversarial network
  for unsupervised domain adaptation,'' in \emph{CVPR}, 2018, pp. 3801--3809.

\bibitem{zhao2018adversarial}
H.~Zhao, S.~Zhang, G.~Wu, J.~M. Moura, J.~P. Costeira, and G.~J. Gordon,
  ``Adversarial multiple source domain adaptation,'' in \emph{NeurIPS}, 2018,
  pp. 8559--8570.

\bibitem{saito2019semi}
K.~Saito, D.~Kim, S.~Sclaroff, T.~Darrell, and K.~Saenko, ``Semi-supervised
  domain adaptation via minimax entropy,'' in \emph{ICCV}, 2019, pp.
  8050--8058.

\bibitem{peng2019federated}
X.~Peng, Z.~Huang, Y.~Zhu, and K.~Saenko, ``Federated adversarial domain
  adaptation,'' \emph{ICLR}, 2020.

\bibitem{liu2018unified}
A.~H. Liu, Y.-C. Liu, Y.-Y. Yeh, and Y.-C.~F. Wang, ``A unified feature
  disentangler for multi-domain image translation and manipulation,'' in
  \emph{NeurIPS}, 2018, pp. 2590--2599.

\bibitem{lee2018diverse}
H.-Y. Lee, H.-Y. Tseng, J.-B. Huang, M.~Singh, and M.-H. Yang, ``Diverse
  image-to-image translation via disentangled representations,'' in
  \emph{ECCV}, 2018, pp. 35--51.

\bibitem{he2016deep}
K.~He, X.~Zhang, S.~Ren, and J.~Sun, ``Deep residual learning for image
  recognition,'' in \emph{CVPR}, 2016.

\bibitem{hu2018squeeze}
J.~Hu, L.~Shen, and G.~Sun, ``Squeeze-and-excitation networks,'' in
  \emph{CVPR}, 2018, pp. 7132--7141.

\bibitem{girshick2014rich}
R.~Girshick, J.~Donahue, T.~Darrell, and J.~Malik, ``Rich feature hierarchies
  for accurate object detection and semantic segmentation,'' in \emph{CVPR},
  2014, pp. 580--587.

\bibitem{ren2015faster}
S.~Ren, K.~He, R.~Girshick, and J.~Sun, ``Faster r-cnn: Towards real-time
  object detection with region proposal networks,'' in \emph{NeurIPS}, 2015,
  pp. 91--99.

\bibitem{he2017mask}
K.~He, G.~Gkioxari, P.~Doll{\'a}r, and R.~Girshick, ``Mask r-cnn,'' in
  \emph{ICCV}, 2017, pp. 2961--2969.

\bibitem{li2018domain}
H.~Li, S.~Jialin~Pan, S.~Wang, and A.~C. Kot, ``Domain generalization with
  adversarial feature learning,'' in \emph{CVPR}, 2018, pp. 5400--5409.

\bibitem{zhou2020learning}
K.~Zhou, Y.~Yang, T.~Hospedales, and T.~Xiang, ``Learning to generate novel
  domains for domain generalization,'' in \emph{ECCV}.\hskip 1em plus 0.5em
  minus 0.4em\relax Springer, 2020, pp. 561--578.

\bibitem{xu2018deep}
R.~Xu, Z.~Chen, W.~Zuo, J.~Yan, and L.~Lin, ``Deep cocktail network:
  Multi-source unsupervised domain adaptation with category shift,'' in
  \emph{CVPR}, 2018, pp. 3964--3973.

\bibitem{wang2018visual}
J.~Wang, W.~Feng, Y.~Chen, H.~Yu, M.~Huang, and P.~S. Yu, ``Visual domain
  adaptation with manifold embedded distribution alignment,'' in \emph{ACMMM},
  2018, pp. 402--410.

\bibitem{venkateswara2017Deep}
H.~Venkateswara, J.~Eusebio, S.~Chakraborty, and S.~Panchanathan, ``Deep
  hashing network for unsupervised domain adaptation,'' in \emph{CVPR}, 2017.

\bibitem{lecun1998mnist}
Y.~LeCun, L.~Bottou, Y.~Bengio, and P.~Haffner, ``Gradient-based learning
  applied to document recognition,'' in \emph{IEEE}, 1998.

\bibitem{ganin2015unsupervised}
Y.~Ganin and V.~S. Lempitsky, ``Unsupervised domain adaptation by
  backpropagation,'' in \emph{ICML}, 2015.

\bibitem{hull1994database}
J.~J. Hull, ``A database for handwritten text recognition research,''
  \emph{TPAMI}, vol.~16, no.~5, pp. 550--554, 1994.

\bibitem{netzer2011svhn}
Y.~Netzer, T.~Wang, A.~Coates, A.~Bissacco, B.~Wu, and A.~Y. Ng, ``Reading
  digits in natural images with unsupervised feature learning,'' in
  \emph{NeurIPS-W}, 2011.

\bibitem{ioffe2015batch}
S.~Ioffe and C.~Szegedy, ``Batch normalization: Accelerating deep network
  training by reducing internal covariate shift,'' \emph{arXiv preprint
  arXiv:1502.03167}, 2015.

\bibitem{zheng2011person}
W.-S. Zheng, S.~Gong, and T.~Xiang, ``Person re-identification by probabilistic
  relative distance comparison,'' 2011.

\bibitem{maaten2008visualizing}
L.~v.~d. Maaten and G.~Hinton, ``Visualizing data using t-sne,'' 2008.

\bibitem{Cordts2016Cityscapes}
M.~Cordts, M.~Omran, S.~Ramos, T.~Rehfeld, M.~Enzweiler, R.~Benenson,
  U.~Franke, S.~Roth, and B.~Schiele, ``The cityscapes dataset for semantic
  urban scene understanding,'' in \emph{CVPR}, 2016.

\bibitem{ros2016synthia}
G.~Ros, L.~Sellart, J.~Materzynska, D.~Vazquez, and A.~M. Lopez, ``The synthia
  dataset: A large collection of synthetic images for semantic segmentation of
  urban scenes,'' in \emph{CVPR}, 2016, pp. 3234--3243.

\bibitem{Richter_2016_ECCV}
S.~R. Richter, V.~Vineet, S.~Roth, and V.~Koltun, ``Playing for data: {G}round
  truth from computer games,'' in \emph{ECCV}, ser. LNCS, B.~Leibe, J.~Matas,
  N.~Sebe, and M.~Welling, Eds., vol. 9906.\hskip 1em plus 0.5em minus
  0.4em\relax Springer International Publishing, 2016, pp. 102--118.

\bibitem{tsai2018learning}
Y.-H. Tsai, W.-C. Hung, S.~Schulter, K.~Sohn, M.-H. Yang, and M.~Chandraker,
  ``Learning to adapt structured output space for semantic segmentation,'' in
  \emph{CVPR}, 2018, pp. 7472--7481.

\bibitem{vu2019advent}
T.-H. Vu, H.~Jain, M.~Bucher, M.~Cord, and P.~P{\'e}rez, ``Advent: Adversarial
  entropy minimization for domain adaptation in semantic segmentation,'' in
  \emph{CVPR}, 2019, pp. 2517--2526.

\bibitem{chen2019domain}
M.~Chen, H.~Xue, and D.~Cai, ``Domain adaptation for semantic segmentation with
  maximum squares loss,'' in \emph{ICCV}, 2019, pp. 2090--2099.

\bibitem{chen2018domain}
Y.~Chen, W.~Li, C.~Sakaridis, D.~Dai, and L.~Van~Gool, ``Domain adaptive faster
  r-cnn for object detection in the wild,'' in \emph{CVPR}, 2018, pp.
  3339--3348.

\bibitem{khodabandeh2019robust}
M.~Khodabandeh, A.~Vahdat, M.~Ranjbar, and W.~G. Macready, ``A robust learning
  approach to domain adaptive object detection,'' in \emph{ICCV}, 2019, pp.
  480--490.

\bibitem{sakaridis2018semantic}
C.~Sakaridis, D.~Dai, and L.~Van~Gool, ``Semantic foggy scene understanding
  with synthetic data,'' \emph{IJCV}, pp. 1--20, 2018.

\bibitem{Geiger2013IJRR}
A.~Geiger, P.~Lenz, C.~Stiller, and R.~Urtasun, ``Vision meets robotics: The
  kitti dataset,'' \emph{IJRR}, 2013.

\bibitem{torralba2011unbiased}
A.~Torralba and A.~A. Efros, ``Unbiased look at dataset bias,'' in \emph{CVPR
  2011}.\hskip 1em plus 0.5em minus 0.4em\relax IEEE, 2011, pp. 1521--1528.

\bibitem{office_home}
H.~Venkateswara, J.~Eusebio, S.~Chakraborty, and S.~Panchanathan, ``Deep
  hashing network for unsupervised domain adaptation,'' in \emph{CVPR}, 2017.

\bibitem{cvpr19JiGen}
F.~M. Carlucci, A.~D'Innocente, S.~Bucci, B.~Caputo, and T.~Tommasi, ``Domain
  generalization by solving jigsaw puzzles,'' in \emph{CVPR}, 2019.

\bibitem{cosineLR}
I.~Loshchilov and F.~Hutter, ``Sgdr: Stochastic gradient descent with warm
  restarts,'' in \emph{ICLR}, 2017.

\bibitem{hoffman2016fcns}
J.~Hoffman, D.~Wang, F.~Yu, and T.~Darrell, ``Fcns in the wild: Pixel-level
  adversarial and constraint-based adaptation,'' \emph{arXiv preprint
  arXiv:1612.02649}, 2016.

\bibitem{zhang2017curriculum}
Y.~Zhang, P.~David, and B.~Gong, ``Curriculum domain adaptation for semantic
  segmentation of urban scenes,'' in \emph{ICCV}, 2017, pp. 2020--2030.

\bibitem{yu2017dilated}
F.~Yu, V.~Koltun, and T.~Funkhouser, ``Dilated residual networks,'' in
  \emph{CVPR}, 2017, pp. 472--480.

\bibitem{deeplabv2}
L.~Chen, G.~Papandreou, I.~Kokkinos, K.~Murphy, and A.~L. Yuille, ``Deeplab:
  Semantic image segmentation with deep convolutional nets, atrous convolution,
  and fully connected crfs,'' \emph{CoRR}, vol. abs/1606.00915, 2016.

\bibitem{ResNet}
K.~He, X.~Zhang, S.~Ren, and J.~Sun, ``Deep residual learning for image
  recognition,'' in \emph{CVPR}, 2016.

\bibitem{ImageNet}
J.~Deng, W.~Dong, R.~Socher, L.~Li, K.~Li, and F.~Li, ``Imagenet: {A}
  large-scale hierarchical image database,'' in \emph{CVPR}, 2009.

\bibitem{AdaptSegNet}
Y.~Tsai, W.~Hung, S.~Schulter, K.~Sohn, M.~Yang, and M.~Chandraker, ``Learning
  to adapt structured output space for semantic segmentation,'' in \emph{CVPR},
  2018.

\bibitem{ADVENT}
T.~Vu, H.~Jain, M.~Bucher, M.~Cord, and P.~P{\'{e}}rez, ``{ADVENT:} adversarial
  entropy minimization for domain adaptation in semantic segmentation,''
  \emph{CoRR}, vol. abs/1811.12833, 2018.

\bibitem{zhao2017pyramid}
H.~Zhao, J.~Shi, X.~Qi, X.~Wang, and J.~Jia, ``Pyramid scene parsing network,''
  in \emph{CVPR}, 2017, pp. 2881--2890.

\end{thebibliography}
